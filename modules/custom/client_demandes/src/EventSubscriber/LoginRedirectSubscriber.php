<?php

namespace Drupal\client_demandes\EventSubscriber;

use <PERSON><PERSON>al\Core\EventSubscriber\HttpExceptionSubscriberBase;
use <PERSON><PERSON>al\Core\Routing\TrustedRedirectResponse;
use <PERSON><PERSON>al\Core\Session\AccountProxyInterface;
use <PERSON><PERSON>al\Core\Url;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpKernel\Event\RequestEvent;
use Symfony\Component\HttpKernel\KernelEvents;

/**
 * Redirects users to their appropriate dashboard after login.
 */
class LoginRedirectSubscriber implements EventSubscriberInterface {

  /**
   * The current user.
   *
   * @var \Drupal\Core\Session\AccountProxyInterface
   */
  protected $currentUser;

  /**
   * Constructs a new LoginRedirectSubscriber.
   *
   * @param \Drupal\Core\Session\AccountProxyInterface $current_user
   *   The current user.
   */
  public function __construct(AccountProxyInterface $current_user) {
    $this->currentUser = $current_user;
  }

  /**
   * {@inheritdoc}
   */
  public static function getSubscribedEvents() {
    // Subscribe to the kernel request event with a high priority
    // to ensure it runs before other redirects
    $events[KernelEvents::REQUEST][] = ['onRequest', 100];
    return $events;
  }

  /**
   * Handles the request event.
   *
   * @param \Symfony\Component\HttpKernel\Event\RequestEvent $event
   *   The request event.
   */
  public function onRequest(RequestEvent $event) {
    $request = $event->getRequest();
    
    // Only process main requests
    if (!$event->isMasterRequest()) {
      return;
    }

    // Check if this is a login redirect scenario
    // We look for specific conditions that indicate a fresh login
    $current_path = $request->getPathInfo();
    $referer = $request->headers->get('referer');
    
    // Skip if already on a dashboard
    if (strpos($current_path, '/prestataire/dashboard') === 0 || 
        strpos($current_path, '/client/dashboard') === 0) {
      return;
    }
    
    // Skip for admin pages, AJAX requests, etc.
    if (strpos($current_path, '/admin') === 0 || 
        $request->isXmlHttpRequest() ||
        strpos($current_path, '/system') === 0) {
      return;
    }
    
    // Check if user just logged in (coming from user/login or front page after login)
    $is_login_redirect = (
      $current_path === '/' || 
      $current_path === '/user' ||
      ($referer && strpos($referer, '/user/login') !== false)
    );
    
    if (!$is_login_redirect) {
      return;
    }
    
    // Check user roles and redirect accordingly
    if ($this->currentUser->isAuthenticated()) {
      $account = \Drupal::entityTypeManager()->getStorage('user')->load($this->currentUser->id());
      
      if ($account->hasRole('prestataire')) {
        $url = Url::fromRoute('client_demandes.prestataire_dashboard');
        $response = new TrustedRedirectResponse($url->toString());
        $event->setResponse($response);
      }
      elseif ($account->hasRole('client')) {
        $url = Url::fromRoute('client_demandes.client_dashboard');
        $response = new TrustedRedirectResponse($url->toString());
        $event->setResponse($response);
      }
    }
  }
}
