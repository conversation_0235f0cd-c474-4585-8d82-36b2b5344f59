<?php

namespace Drupal\client_demandes\Controller;

use <PERSON><PERSON><PERSON>\Core\Controller\ControllerBase;
use <PERSON><PERSON><PERSON>\node\Entity\Node;
use Symfony\Component\DependencyInjection\ContainerInterface;

class PrestataireDashboardController extends ControllerBase
{

  /**
   * Page du tableau de bord prestataire.
   */
  public function dashboard()
  {
    $demandes = $this->getDemandesPourPrestataire();

    return [
      '#theme' => 'prestataire_dashboard',
      '#demandes' => $demandes,
      '#devis' => [], // À compléter selon vos besoins
      '#messages' => [], // À compléter aussi
    ];
  }

  /**
   * Récupère les demandes compatibles avec les catégories du prestataire connecté.
   *
   * @return \Drupal\node\NodeInterface[]
   *   Liste des nœuds "demande" pertinents.
   */
  protected function getDemandesPourPrestataire()
  {
    $current_user = $this->currentUser();
    $account = \Drupal\user\Entity\User::load($current_user->id());

    // Vérifier que l'utilisateur a bien une catégorie définie.
    if (!$account->hasField('field_categorie') || $account->get('field_categorie')->isEmpty()) {
      return [];
    }

    $categories_ids = [];
    foreach ($account->get('field_categorie') as $term_ref) {
      $categories_ids[] = $term_ref->target_id;
    }

    if (empty($categories_ids)) {
      return [];
    }

    // Requête vers les demandes correspondantes.
    $query = \Drupal::entityQuery('node')
      ->condition('type', 'demande')
      ->condition('status', 1)
      ->condition('field_categorie', $categories_ids, 'IN')
      ->accessCheck(TRUE);

    $nids = $query->execute();
    return Node::loadMultiple($nids);
  }
}
