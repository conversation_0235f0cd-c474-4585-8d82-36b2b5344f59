<?php

namespace Dr<PERSON>al\client_demandes\Controller;

use <PERSON><PERSON>al\Core\Controller\ControllerBase;
use <PERSON><PERSON><PERSON>\Core\Session\AccountInterface;
use <PERSON><PERSON>al\Core\Entity\EntityTypeManagerInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;
use Drupal\commerce_order\Entity\Order;
use Drupal\node\Entity\Node;
use Drupal\user\Entity\User;
use Drupal\Core\Url;
use Drupal\Core\Link;

/**
 * Contrôleur pour le tableau de bord du prestataire.
 */
class PrestataireController extends ControllerBase
{

  protected $entityTypeManager;
  protected $currentUser;

  public function __construct(EntityTypeManagerInterface $entity_type_manager, AccountInterface $current_user)
  {
    $this->entityTypeManager = $entity_type_manager;
    $this->currentUser = $current_user;
  }

  public static function create(ContainerInterface $container)
  {
    return new static(
      $container->get('entity_type.manager'),
      $container->get('current_user')
    );
  }

  /**
   * Page principale du tableau de bord prestataire.
   */
  public function dashboard()
  {
    $current_uid = $this->currentUser->id();
    $current_user = User::load($current_uid);

    // Statistiques des devis envoyés
    $sent_quotes_stats = $this->getSentQuotesStats($current_uid);

    // Statistiques des revenus Stripe
    $stripe_stats = $this->getStripeEarningsStats($current_uid);

    // Informations Stripe Connect
    $stripe_connect_info = $this->getStripeConnectInfo($current_user);

    $total_revenue = 0;
    $order_count = 0;

    $query = \Drupal::entityQuery('commerce_order')
      ->accessCheck(TRUE)
      ->sort('created', 'DESC')
      ->range(0, 50);
    $order_ids = $query->execute();

    foreach (Order::loadMultiple($order_ids) as $order) {
      foreach ($order->getItems() as $item) {
        $product = $item->getPurchasedEntity();
        if ($product && $product->getOwnerId() == $current_uid) {
          if ($order->getState()->value === 'completed') {
            $total_revenue += (float) $order->getTotalPrice()->getNumber();
            $order_count++;
          }
        }
      }
    }

    $summary = [
      '#theme' => 'item_list',
      '#title' => $this->t('Résumé'),
      '#items' => [
        'Nombre de commandes payées : ' . $order_count,
        'Chiffre d’affaires total : ' . number_format($total_revenue, 2) . ' €',
      ],
    ];

    $block = \Drupal::service('plugin.manager.block')->createInstance('paiement_notification_block', []);
    $block_build = $block->build();

    $demandes = $this->getDemandesPourPrestataire();

    $items = [];
    foreach ($demandes as $demande) {
      $title = $demande->label();
      $link = $demande->toLink($this->t('Voir la demande'))->toString();
      $quote_url = Url::fromRoute('client_demandes.send_quote_form', ['node' => $demande->id()]);
      $quote_link = Link::fromTextAndUrl($this->t('Faire une proposition'), $quote_url)->toString();
      $items[] = [
        '#markup' => "<strong>$title</strong><br />$link | $quote_link",
      ];
    }

    return [
      '#theme' => 'prestataire_dashboard',
      '#demandes' => $demandes,
      '#sent_quotes_stats' => $sent_quotes_stats,
      '#stripe_stats' => $stripe_stats,
      '#stripe_connect_info' => $stripe_connect_info,
      'summary' => $summary,
      'notifications' => $block_build,
      '#attached' => [
        'library' => [
          'client_demandes/prestataire_dashboard',
        ],
      ],
    ];
  }

  /**
   * Récupère les demandes compatibles avec les catégories métier du prestataire.
   */
  protected function getDemandesPourPrestataire()
  {
    $account = User::load($this->currentUser->id());

    // Debug: Vérifier les champs disponibles sur l'utilisateur
    $user_fields = [];
    foreach ($account->getFields() as $field_name => $field) {
      if (strpos($field_name, 'field_') === 0) {
        $user_fields[] = $field_name;
      }
    }

    // Si pas de champ catégorie métier, retourner toutes les demandes pour debug
    if (!$account->hasField('field_categorie_metier') || $account->get('field_categorie_metier')->isEmpty()) {
      // Debug: Retourner quelques demandes pour voir si le problème vient des catégories
      $debug_query = \Drupal::entityQuery('node')
        ->condition('type', 'demande')
        ->condition('status', 1)
        ->sort('created', 'DESC')
        ->range(0, 5)
        ->accessCheck(TRUE);

      $debug_nids = $debug_query->execute();
      $debug_demandes = $this->entityTypeManager->getStorage('node')->loadMultiple($debug_nids);

      // Ajouter un message de debug
      \Drupal::messenger()->addWarning('Debug: Aucune catégorie métier trouvée. Champs utilisateur disponibles: ' . implode(', ', $user_fields) . '. Demandes trouvées: ' . count($debug_demandes));

      return $debug_demandes;
    }

    $categories_ids = [];
    foreach ($account->get('field_categorie_metier') as $term_ref) {
      $categories_ids[] = $term_ref->target_id;
    }

    // Debug: Vérifier les champs sur les demandes
    $sample_query = \Drupal::entityQuery('node')
      ->condition('type', 'demande')
      ->condition('status', 1)
      ->range(0, 1)
      ->accessCheck(TRUE);

    $sample_nids = $sample_query->execute();
    if (!empty($sample_nids)) {
      $sample_demande = $this->entityTypeManager->getStorage('node')->load(reset($sample_nids));
      $demande_fields = [];
      foreach ($sample_demande->getFields() as $field_name => $field) {
        if (strpos($field_name, 'field_') === 0) {
          $demande_fields[] = $field_name;
        }
      }
      \Drupal::messenger()->addWarning('Debug: Champs demande disponibles: ' . implode(', ', $demande_fields));
    }

    $query = \Drupal::entityQuery('node')
      ->condition('type', 'demande')
      ->condition('status', 1)
      ->condition('field_categorie', $categories_ids, 'IN')
      ->sort('created', 'DESC')
      ->accessCheck(TRUE);

    $nids = $query->execute();
    $demandes = $this->entityTypeManager->getStorage('node')->loadMultiple($nids);

    \Drupal::messenger()->addWarning('Debug: Catégories prestataire: ' . implode(', ', $categories_ids) . '. Demandes trouvées: ' . count($demandes));

    return $demandes;
  }

  /**
   * Récupère les statistiques des devis envoyés par le prestataire.
   */
  protected function getSentQuotesStats($uid)
  {
    $query = \Drupal::entityQuery('node')
      ->condition('type', 'devis')
      ->condition('uid', $uid)
      ->accessCheck(TRUE);

    $total_sent = count($query->execute());

    $accepted_query = \Drupal::entityQuery('node')
      ->condition('type', 'devis')
      ->condition('uid', $uid)
      ->condition('field_status', 'accepted')
      ->accessCheck(TRUE);

    $accepted = count($accepted_query->execute());

    // Calculer le taux de réussite
    $success_rate = $total_sent > 0 ? round(($accepted / $total_sent) * 100, 1) : 0;

    return [
      'total_sent' => $total_sent,
      'accepted' => $accepted,
      'pending' => $total_sent - $accepted,
      'success_rate' => $success_rate,
    ];
  }

  /**
   * Récupère les statistiques des revenus Stripe.
   */
  protected function getStripeEarningsStats($uid)
  {
    // Récupérer les devis acceptés du prestataire
    $query = \Drupal::entityQuery('node')
      ->condition('type', 'devis')
      ->condition('uid', $uid)
      ->condition('field_status', 'accepted')
      ->accessCheck(TRUE);

    $devis_ids = $query->execute();
    $devis_nodes = $this->entityTypeManager->getStorage('node')->loadMultiple($devis_ids);

    $total_earnings = 0;
    $transaction_count = 0;

    foreach ($devis_nodes as $devis) {
      if ($devis->hasField('field_prix') && !$devis->get('field_prix')->isEmpty()) {
        $total_earnings += (float) $devis->get('field_prix')->value;
        $transaction_count++;
      }
    }

    return [
      'total_earnings' => $total_earnings,
      'transaction_count' => $transaction_count,
      'average_transaction' => $transaction_count > 0 ? $total_earnings / $transaction_count : 0,
    ];
  }

  /**
   * Récupère les informations Stripe Connect du prestataire.
   */
  protected function getStripeConnectInfo($user)
  {
    $stripe_account_id = '';
    $stripe_status = 'Non connecté';

    if ($user->hasField('field_stripe_account_id') && !$user->get('field_stripe_account_id')->isEmpty()) {
      $stripe_account_id = $user->get('field_stripe_account_id')->value;
      $stripe_status = 'Connecté';
    } elseif ($user->hasField('field_stripe_user_id') && !$user->get('field_stripe_user_id')->isEmpty()) {
      $stripe_account_id = $user->get('field_stripe_user_id')->value;
      $stripe_status = 'Connecté (legacy)';
    }

    // Générer l'URL de connexion Stripe
    $connect_url = '';
    if ($stripe_status === 'Non connecté') {
      try {
        $route_provider = \Drupal::service('router.route_provider');
        $route_provider->getRouteByName('stripe_oauth_connect.start');
        $connect_url = Url::fromRoute('stripe_oauth_connect.start')->toString();
      } catch (\Exception $e) {
        // Route n'existe pas, utiliser notre formulaire de configuration
        $connect_url = '/prestataire/stripe/configure';
      }
    }

    return [
      'status' => $stripe_status,
      'account_id' => $stripe_account_id,
      'connect_url' => $connect_url,
    ];
  }

  /**
   * Liste des devis envoyés par le prestataire connecté.
   */
  public function mySentOffers()
  {
    $uid = $this->currentUser->id();

    $query = $this->entityTypeManager->getStorage('node')->getQuery()
      ->condition('type', 'devis')
      ->condition('uid', $uid)
      ->sort('created', 'DESC')
      ->accessCheck(TRUE);

    $nids = $query->execute();
    $offers = $this->entityTypeManager->getStorage('node')->loadMultiple($nids);

    $rows = [];
    foreach ($offers as $offer) {
      $title = $offer->label();
      $created = \Drupal::service('date.formatter')->format($offer->getCreatedTime(), 'short');

      // Récupérer le nom du client
      $client_name = '';
      if ($offer->hasField('field_client') && !$offer->get('field_client')->isEmpty()) {
        $client = $offer->get('field_client')->entity;
        $client_name = $client ? $client->getDisplayName() : '';
      }

      // Récupérer le statut du devis
      $status = 'pending'; // Valeur par défaut
      $status_label = $this->t('En attente');
      $status_class = 'status-pending';

      if ($offer->hasField('field_status') && !$offer->get('field_status')->isEmpty()) {
        $status = $offer->get('field_status')->value;
        switch ($status) {
          case 'accepted':
            $status_label = $this->t('Accepté');
            $status_class = 'status-accepted';
            break;
          case 'refused':
            $status_label = $this->t('Refusé');
            $status_class = 'status-refused';
            break;
          case 'pending':
          default:
            $status_label = $this->t('En attente');
            $status_class = 'status-pending';
            break;
        }
      }

      // Récupérer le prix du devis
      $price = '';
      if ($offer->hasField('field_prix') && !$offer->get('field_prix')->isEmpty()) {
        $price = number_format((float) $offer->get('field_prix')->value, 2) . ' €';
      }

      // Créer le badge de statut
      $status_badge = [
        '#markup' => '<span class="badge ' . $status_class . '">' . $status_label . '</span>',
      ];

      $rows[] = [
        'data' => [
          $title,
          $client_name,
          $price,
          \Drupal::service('renderer')->render($status_badge),
          $created,
          $offer->toLink($this->t('Voir'))->toString(),
        ],
      ];
    }

    return [
      '#type' => 'table',
      '#header' => [
        $this->t('Titre'),
        $this->t('Client'),
        $this->t('Prix'),
        $this->t('Statut'),
        $this->t('Date'),
        $this->t('Actions')
      ],
      '#rows' => $rows,
      '#empty' => $this->t('Aucune offre envoyée.'),
      '#attached' => [
        'library' => [
          'client_demandes/prestataire_dashboard',
        ],
      ],
    ];
  }

  /**
   * Hook de personnalisation d'e-mail.
   */
  public function client_demandes_mail($key, &$message, $params)
  {
    switch ($key) {
      case 'facture_envoyee':
        $message['subject'] = $params['subject'];
        $message['body'][] = $params['body'];
        if (!empty($params['attachments'])) {
          $message['attachments'] = $params['attachments'];
        }
        break;
    }
  }

  /**
   * Demo du tableau de bord prestataire avec données fictives.
   */
  public function dashboardDemo()
  {
    // Données fictives pour la démo
    $sent_quotes_stats = [
      'total_sent' => 15,
      'accepted' => 8,
      'pending' => 7,
      'success_rate' => 53.3,
    ];

    $stripe_stats = [
      'total_earnings' => 3250.50,
      'transaction_count' => 8,
      'average_transaction' => 406.31,
    ];

    $stripe_connect_info = [
      'status' => 'Connecté',
      'account_id' => 'acct_demo123456789',
      'connect_url' => '',
    ];

    // Créer des demandes fictives
    $demo_demandes = [
      (object) [
        'id' => 1,
        'label' => 'Création site e-commerce',
        'created' => (object) ['value' => time() - 86400],
        'field_localisation' => (object) ['value' => 'Paris, France'],
        'field_budget' => (object) ['value' => 2500],
        'field_price' => (object) [
          'value' => 2800,
          '0' => (object) ['number' => 2800, 'value' => 2800],
          'isEmpty' => function () {
            return false;
          }
        ],
        'field_delai' => (object) ['value' => '3 semaines'],
        'field_description' => (object) ['value' => 'Besoin d\'un site e-commerce moderne avec paiement en ligne et gestion des stocks.'],
        'owner' => (object) ['getDisplayName' => function () {
          return 'Marie Dupont';
        }],
      ],
      (object) [
        'id' => 2,
        'label' => 'Refonte application mobile',
        'created' => (object) ['value' => time() - 172800],
        'field_localisation' => (object) ['value' => 'Lyon, France'],
        'field_budget' => (object) ['value' => 4000],
        'field_price' => (object) [
          'value' => 3750,
          '0' => (object) ['number' => 3750, 'value' => 3750],
          'isEmpty' => function () {
            return false;
          }
        ],
        'field_delai' => (object) ['value' => '6 semaines'],
        'field_description' => (object) ['value' => 'Modernisation d\'une application mobile existante avec nouvelles fonctionnalités.'],
        'owner' => (object) ['getDisplayName' => function () {
          return 'Jean Martin';
        }],
      ],
      (object) [
        'id' => 3,
        'label' => 'Audit SEO complet',
        'created' => (object) ['value' => time() - 259200],
        'field_localisation' => (object) ['value' => 'Marseille, France'],
        'field_budget' => (object) ['value' => 800],
        'field_price' => (object) [
          'value' => 750,
          '0' => (object) ['number' => 750, 'value' => 750],
          'isEmpty' => function () {
            return false;
          }
        ],
        'field_delai' => (object) ['value' => '2 semaines'],
        'field_description' => (object) ['value' => 'Analyse complète du référencement naturel avec recommandations d\'amélioration.'],
        'owner' => (object) ['getDisplayName' => function () {
          return 'Sophie Durand';
        }],
      ],
    ];

    $summary = [
      '#theme' => 'item_list',
      '#title' => $this->t('Résumé financier (Demo)'),
      '#items' => [
        'Nombre de commandes payées : 5',
        'Chiffre d\'affaires Commerce : 1,850.00 €',
        'Devis envoyés : ' . $sent_quotes_stats['total_sent'],
        'Devis acceptés : ' . $sent_quotes_stats['accepted'],
        'Revenus Stripe : ' . number_format($stripe_stats['total_earnings'], 2) . ' €',
        'Statut Stripe Connect : ' . $stripe_connect_info['status'],
      ],
    ];

    return [
      '#theme' => 'prestataire_dashboard',
      '#demandes' => $demo_demandes,
      '#sent_quotes_stats' => $sent_quotes_stats,
      '#stripe_stats' => $stripe_stats,
      '#stripe_connect_info' => $stripe_connect_info,
      'summary' => $summary,
      'notifications' => ['#markup' => '<div class="alert alert-info">🎯 Ceci est une démonstration du tableau de bord prestataire amélioré.</div>'],
      '#attached' => [
        'library' => [
          'client_demandes/prestataire_dashboard',
        ],
      ],
    ];
  }

  /**
   * Page de configuration Stripe Connect pour les prestataires.
   */
  public function stripeConnectSetup()
  {
    $current_user = User::load($this->currentUser->id());
    $stripe_info = $this->getStripeConnectInfo($current_user);

    $content = [
      '#markup' => '
        <div class="stripe-connect-setup">
          <h2>Configuration Stripe Connect</h2>
          <p>Connectez votre compte Stripe pour recevoir des paiements directement.</p>

          <div class="current-status">
            <h3>Statut actuel</h3>
            <p><strong>Statut :</strong> ' . $stripe_info['status'] . '</p>
            ' . ($stripe_info['account_id'] ? '<p><strong>ID Compte :</strong> ' . $stripe_info['account_id'] . '</p>' : '') . '
          </div>

          <div class="setup-instructions">
            <h3>Instructions</h3>
            <ol>
              <li>Créez un compte Stripe si vous n\'en avez pas déjà un</li>
              <li>Configurez votre compte Stripe Connect</li>
              <li>Ajoutez votre ID de compte Stripe dans votre profil utilisateur</li>
            </ol>
          </div>

          <div class="actions">
            <a href="https://stripe.com/connect" target="_blank" class="button button--primary">
              Créer un compte Stripe
            </a>
            <a href="/user/' . $this->currentUser->id() . '/edit" class="button">
              Modifier mon profil
            </a>
          </div>
        </div>
      ',
      '#attached' => [
        'library' => [
          'client_demandes/prestataire_dashboard',
        ],
      ],
    ];

    return $content;
  }

  /**
   * Affiche le formulaire de configuration Stripe Connect.
   */
  public function stripeConnectForm()
  {
    // Test simple d'abord
    return [
      '#markup' => '
        <div class="stripe-connect-setup">
          <h2>Configuration Stripe Connect</h2>
          <p>Cette page fonctionne ! Le problème était le cache des routes.</p>
          <p><a href="/prestataire/dashboard" class="button">Retour au tableau de bord</a></p>

          <div style="margin-top: 20px; padding: 15px; background: #f0f0f0; border-radius: 5px;">
            <h3>Prochaines étapes :</h3>
            <ol>
              <li>Vider le cache Drupal : <code>drush cache:rebuild</code></li>
              <li>Ou aller dans Administration > Configuration > Performance > Vider tous les caches</li>
              <li>Ensuite cette page affichera le formulaire complet</li>
            </ol>
          </div>
        </div>
      ',
      '#attached' => [
        'library' => [
          'client_demandes/prestataire_dashboard',
        ],
      ],
    ];
  }

  /**
   * Demo de la page mes offres envoyées avec données fictives.
   */
  public function mySentOffersDemo()
  {
    // Créer des données fictives pour la démo
    $demo_rows = [
      [
        'data' => [
          'Création site e-commerce',
          'Marie Dupont',
          '2,500.00 €',
          '<span class="badge status-accepted">Accepté</span>',
          '15/06/2025',
          '<a href="#" class="button">Voir</a>',
        ],
      ],
      [
        'data' => [
          'Refonte application mobile',
          'Jean Martin',
          '4,000.00 €',
          '<span class="badge status-pending">En attente</span>',
          '20/06/2025',
          '<a href="#" class="button">Voir</a>',
        ],
      ],
      [
        'data' => [
          'Audit SEO complet',
          'Claire Morel',
          '800.00 €',
          '<span class="badge status-refused">Refusé</span>',
          '18/06/2025',
          '<a href="#" class="button">Voir</a>',
        ],
      ],
      [
        'data' => [
          'Développement API REST',
          'Pierre Durand',
          '1,200.00 €',
          '<span class="badge status-accepted">Accepté</span>',
          '12/06/2025',
          '<a href="#" class="button">Voir</a>',
        ],
      ],
      [
        'data' => [
          'Formation WordPress',
          'Sophie Bernard',
          '600.00 €',
          '<span class="badge status-pending">En attente</span>',
          '25/06/2025',
          '<a href="#" class="button">Voir</a>',
        ],
      ],
    ];

    return [
      '#markup' => '<div class="alert alert-info">🎯 <strong>Démonstration</strong> - Voici comment apparaissent vos devis avec leur statut.</div>',
      'table' => [
        '#type' => 'table',
        '#header' => [
          $this->t('Titre'),
          $this->t('Client'),
          $this->t('Prix'),
          $this->t('Statut'),
          $this->t('Date'),
          $this->t('Actions')
        ],
        '#rows' => $demo_rows,
        '#empty' => $this->t('Aucune offre envoyée.'),
        '#attached' => [
          'library' => [
            'client_demandes/prestataire_dashboard',
          ],
        ],
      ],
    ];
  }
}
