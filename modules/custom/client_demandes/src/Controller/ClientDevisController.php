<?php

namespace Drupal\client_demandes\Controller;

use <PERSON><PERSON>al\Core\Controller\ControllerBase;
use <PERSON><PERSON>al\Core\Messenger\MessengerInterface;
use <PERSON><PERSON>al\Core\Routing\TrustedRedirectResponse;
use <PERSON><PERSON>al\node\Entity\Node;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Drupal\Core\Url;

/**
 * Contrôleur pour accepter un devis.
 */
class ClientDevisController extends ControllerBase
{

  /**
   * Le service messenger.
   *
   * @var \Drupal\Core\Messenger\MessengerInterface
   */
  protected $messenger;

  /**
   * Constructeur.
   */
  public function __construct(MessengerInterface $messenger)
  {
    $this->messenger = $messenger;
  }

  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container)
  {
    return new static(
      $container->get('messenger')
    );
  }

  /**
   * Marque un devis comme accepté.
   *
   * @param \Drupal\node\Entity\Node $devis
   *   Le node de type "devis".
   *
   * @return \Symfony\Component\HttpFoundation\RedirectResponse
   *   Redirection vers le tableau de bord du client.
   */
  public function acceptDevis(Node $devis)
  {
    // Vérification du type de node.
    if ($devis->bundle() !== 'devis') {
      throw new \Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException();
    }

    // Vérifier si l'utilisateur a le droit (est le propriétaire de la demande liée).
    $demande = $devis->get('field_demande_ref')->entity ?? NULL;
    if (!$demande || $demande->getOwnerId() !== $this->currentUser()->id()) {
      throw new \Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException();
    }

    // Marquer le devis comme accepté.
    $devis->set('field_status', 'accepted');
    $devis->save();

    $this->messenger->addMessage($this->t('Le devis a été accepté.'));

    // Rediriger vers le tableau de bord client.
    return new RedirectResponse($this->url('client_demandes.client_dashboard'));
  }
}
