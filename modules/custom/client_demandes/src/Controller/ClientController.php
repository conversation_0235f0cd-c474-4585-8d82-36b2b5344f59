<?php

namespace Drupal\client_demandes\Controller;

use <PERSON><PERSON><PERSON>\Core\Controller\ControllerBase;
use <PERSON><PERSON>al\Core\Session\AccountInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Drupal\Core\Url;
use <PERSON><PERSON><PERSON>\node\Entity\Node;

/**
 * Controller for the client dashboard.
 */
class ClientController extends ControllerBase
{

  /**
   * The current user.
   *
   * @var \Drupal\Core\Session\AccountInterface
   */
  protected $currentUser;

  /**
   * Constructs a new ClientController object.
   *
   * @param \Drupal\Core\Session\AccountInterface $current_user
   *   The current user.
   */
  public function __construct(AccountInterface $current_user)
  {
    $this->currentUser = $current_user;
  }

  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container)
  {
    return new static(
      $container->get('current_user')
    );
  }

  /**
   * Displays the client dashboard.
   *
   * @return array
   *   A renderable array.
   */
  public function dashboard()
  {
    // Load client's own demandes.
    $demandes_ids = \Drupal::entityQuery('node')
      ->condition('type', 'demande')
      ->condition('uid', $this->currentUser->id())
      ->sort('created', 'DESC')
      ->accessCheck(TRUE)
      ->execute();
    $demandes = Node::loadMultiple($demandes_ids);

    // Load devis linked to those demandes.
    $devis_ids = \Drupal::entityQuery('node')
      ->condition('type', 'devis')
      ->condition('field_demande_ref.entity.uid', $this->currentUser->id())
      ->sort('created', 'DESC')
      ->accessCheck(TRUE)
      ->execute();
    $devis = Node::loadMultiple($devis_ids);

    // Attach additional data to each devis.
    foreach ($devis as $devis_node) {
      if ($devis_node->getOwner()) {
        $devis_node->prestataire_user = $devis_node->getOwner();
      }

      // Add accept URL.
      $devis_node->accept_url = Url::fromRoute('client_demandes.accept_devis', [
        'devis' => $devis_node->id(),
      ])->toString();
    }

    return [
      '#theme' => 'client_dashboard',
      '#title' => $this->t('Client Dashboard'),

      '#actions' => [
        '#type' => 'container',
        '#attributes' => ['class' => ['dashboard-actions']],
        'publier_demande' => [
          '#type' => 'link',
          '#title' => $this->t('📤 Publish a new request'),
          '#url' => Url::fromRoute('client_demandes.demande_form'),
          '#attributes' => [
            'class' => ['button', 'button--primary'],
          ],
        ],
      ],

      '#demandes' => $demandes,
      '#devis_proposes' => $devis,
      '#attached' => [
        'library' => [
          'client_demandes/client_dashboard',
        ],
      ],
    ];
  }

  /**
   * Demo du tableau de bord client avec données fictives.
   */
  public function dashboardDemo()
  {
    // Créer des demandes fictives
    $demo_demandes = [
      (object) [
        'id' => 1,
        'label' => 'Création site e-commerce',
        'created' => (object) ['value' => time() - 86400],
        'field_budget' => (object) ['value' => 2500],
        'field_description' => (object) ['value' => 'Besoin d\'un site e-commerce moderne avec paiement en ligne.'],
      ],
      (object) [
        'id' => 2,
        'label' => 'Refonte application mobile',
        'created' => (object) ['value' => time() - 172800],
        'field_budget' => (object) ['value' => 4000],
        'field_description' => (object) ['value' => 'Modernisation d\'une application mobile existante.'],
      ],
      (object) [
        'id' => 3,
        'label' => 'Audit SEO complet',
        'created' => (object) ['value' => time() - 259200],
        'field_budget' => (object) ['value' => 800],
        'field_description' => (object) ['value' => 'Analyse complète du référencement naturel.'],
      ],
    ];

    return [
      '#theme' => 'client_dashboard',
      'actions' => [
        '#markup' => '
          <div class="alert alert-info">
            🎯 <strong>Démonstration</strong> - Voici comment apparaît votre tableau de bord client avec les boutons "Modifier".
          </div>
          <a href="/client/demande/add" class="button button--primary">
            ➕ Publier une nouvelle demande
          </a>
        ',
      ],
      '#demandes' => $demo_demandes,
      '#devis_proposes' => [],
      '#attached' => [
        'library' => [
          'client_demandes/client_dashboard',
        ],
      ],
    ];
  }
}
