<?php

namespace Drupal\client_demandes\Controller;

use <PERSON>upal\Core\Controller\ControllerBase;
use Drupal\commerce_product\Entity\Product;

/**
 * Contrôleur pour la boutique et l'ajout au panier.
 */
class ShopController extends ControllerBase {

  /**
   * Page boutique simple avec produits.
   */
  public function shopPage() {
    $content = [];
    
    $content['title'] = [
      '#markup' => '<h1>🛍️ Boutique - Services Disponibles</h1>',
    ];
    
    $content['description'] = [
      '#markup' => '<p>Découvrez nos services et ajoutez-les à votre panier.</p>',
    ];
    
    // Récupérer les produits
    $query = \Drupal::entityQuery('commerce_product')
      ->condition('status', 1)
      ->sort('created', 'DESC')
      ->accessCheck(TRUE);
    
    $product_ids = $query->execute();
    
    if (empty($product_ids)) {
      $content['no_products'] = [
        '#markup' => '
          <div style="background: #fff3cd; padding: 30px; border-radius: 12px; margin: 20px 0; text-align: center;">
            <h3>📦 Aucun Produit Disponible</h3>
            <p>Il n\'y a actuellement aucun service disponible.</p>
            <a href="/admin/client-demandes/create-test-products" class="button" style="background: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block; margin-top: 15px;">
              ➕ Créer des produits de test
            </a>
          </div>
        ',
      ];
      return $content;
    }
    
    $products = Product::loadMultiple($product_ids);
    
    $content['products'] = [
      '#type' => 'container',
      '#attributes' => [
        'class' => ['products-grid'],
        'style' => 'display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 25px; margin: 30px 0;',
      ],
    ];
    
    foreach ($products as $product) {
      $variations = $product->getVariations();
      if (empty($variations)) {
        continue;
      }
      
      $variation = reset($variations);
      $price = $variation->getPrice();
      
      // Créer le formulaire d'ajout au panier pour chaque produit
      $form = \Drupal::formBuilder()->getForm(
        'Drupal\commerce_cart\Form\AddToCartForm',
        $variation
      );
      
      $content['products']['product_' . $product->id()] = [
        '#type' => 'container',
        '#attributes' => [
          'class' => ['product-card'],
          'style' => 'border: 1px solid #e9ecef; border-radius: 12px; padding: 25px; background: white; box-shadow: 0 2px 8px rgba(0,0,0,0.1); transition: transform 0.2s ease;',
        ],
        'title' => [
          '#markup' => '<h3 style="color: #2c3e50; margin-bottom: 15px; font-size: 1.3em;">' . $product->getTitle() . '</h3>',
        ],
        'description' => [
          '#markup' => '<div style="color: #6c757d; margin-bottom: 20px; line-height: 1.6;">' . 
                      ($product->hasField('body') && !$product->get('body')->isEmpty() ? 
                       $product->get('body')->value : 'Description non disponible') . '</div>',
        ],
        'price' => [
          '#markup' => '<div style="font-size: 1.4em; font-weight: 600; color: #28a745; margin-bottom: 20px;">💰 ' . 
                      ($price ? $price->getNumber() . ' ' . $price->getCurrencyCode() : 'Prix sur demande') . '</div>',
        ],
        'sku' => [
          '#markup' => '<div style="font-size: 0.9em; color: #6c757d; margin-bottom: 20px;">SKU: ' . $variation->getSku() . '</div>',
        ],
        'add_to_cart_form' => $form,
      ];
    }
    
    // Statut du panier
    $cart_provider = \Drupal::service('commerce_cart.cart_provider');
    $store = \Drupal::service('commerce_store.current_store')->getStore();
    $cart = $cart_provider->getCart('default', $store);
    
    if ($cart && !$cart->getItems()->isEmpty()) {
      $items_count = count($cart->getItems());
      $total = $cart->getTotalPrice();
      $total_formatted = $total ? $total->getNumber() . ' ' . $total->getCurrencyCode() : '0 EUR';
      
      $content['cart_summary'] = [
        '#markup' => '
          <div style="position: fixed; top: 20px; right: 20px; background: #28a745; color: white; padding: 15px 20px; border-radius: 8px; box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3); z-index: 1000;">
            <div style="font-weight: 600; margin-bottom: 8px;">🛒 Panier (' . $items_count . ' article' . ($items_count > 1 ? 's' : '') . ')</div>
            <div style="margin-bottom: 12px;">Total: ' . $total_formatted . '</div>
            <div>
              <a href="/cart" style="background: rgba(255,255,255,0.2); color: white; padding: 6px 12px; text-decoration: none; border-radius: 4px; font-size: 0.9em; margin-right: 8px;">Voir</a>
              <a href="/checkout" style="background: white; color: #28a745; padding: 6px 12px; text-decoration: none; border-radius: 4px; font-size: 0.9em; font-weight: 600;">Payer</a>
            </div>
          </div>
        ',
      ];
    }
    
    $content['#attached']['library'][] = 'client_demandes/shop';
    
    return $content;
  }

}
