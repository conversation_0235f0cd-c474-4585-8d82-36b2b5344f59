<?php

namespace Drupal\client_demandes\Controller;

use Drupal\Core\Controller\ControllerBase;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Stripe\Stripe;
use Stripe\Checkout\Session;
use Drupal\node\Entity\Node;
use Drupal\user\Entity\User;

class StripeController extends ControllerBase {

  /**
   * Crée une session Stripe Checkout pour le paiement du devis.
   */
  public function checkout(Request $request, $devis_id, $mode = 'complet') {
    $devis = Node::load($devis_id);
    if (!$devis || $devis->bundle() !== 'devis') {
      throw new \Symfony\Component\HttpKernel\Exception\NotFoundHttpException('Devis non trouvé.');
    }

    $amount = (float) $devis->get('field_price')->value;
    $prestataire_id = $devis->getOwnerId();
    $prestataire = User::load($prestataire_id);

    $commission_percent = \Drupal::config('client_demandes.settings')->get('commission_percent') ?? 10;
    $amount_cents = (int) ($amount * 100);

    \Stripe\Stripe::setApiKey(getenv('STRIPE_SECRET_KEY'));

    $params = [
      'payment_method_types' => ['card'],
      'line_items' => [[
        'price_data' => [
          'currency' => 'eur',
          'product_data' => [
            'name' => 'Paiement du devis',
          ],
          'unit_amount' => $amount_cents,
        ],
        'quantity' => 1,
      ]],
      'mode' => 'payment',
      'success_url' => $GLOBALS['base_url'] . '/paiement/success?devis=' . $devis_id,
      'cancel_url' => $GLOBALS['base_url'] . '/paiement/cancel?devis=' . $devis_id,
    ];

    if ($mode === 'multi') {
      $account_id = $prestataire->get('field_stripe_account_id')->value;
      if (!$account_id) {
        \Drupal::messenger()->addError("Ce prestataire n'a pas configuré Stripe Connect.");
        return new RedirectResponse('/devis/' . $devis_id);
      }

      $commission = round($amount_cents * $commission_percent / 100);
      $params['payment_intent_data'] = [
        'application_fee_amount' => $commission,
        'transfer_data' => [
          'destination' => $account_id,
        ],
      ];
    }

    $session = Session::create($params);
    return new RedirectResponse($session->url, 303);
  }

  /**
   * Page de succès.
   */
  public function success(Request $request) {
    $devis_id = $request->query->get('devis');
    $devis = Node::load($devis_id);
    if ($devis) {
      $devis->set('field_status', 'accepted');
      $devis->save();
    }
    return [
      '#markup' => '<h2>Paiement réussi ! Merci pour votre confiance.</h2>',
    ];
  }

  /**
   * Page d'annulation.
   */
  public function cancel(Request $request) {
    return [
      '#markup' => '<h2>Paiement annulé. Vous pouvez réessayer ou contacter le support.</h2>',
    ];
  }
}
