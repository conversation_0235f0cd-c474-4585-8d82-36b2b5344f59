<?php

namespace Drupal\client_demandes\Controller;

use <PERSON>upal\Core\Controller\ControllerBase;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Drupal\node\Entity\Node;
use Drupal\Core\Url;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;

/**
 * Contrôleur pour accepter ou refuser un devis.
 */
class DevisActionController extends ControllerBase
{

  /**
   * Accepter un devis.
   */
  public function accept($node)
  {
    $devis = Node::load($node);
    if (!$devis || $devis->bundle() !== 'devis') {
      throw new AccessDeniedHttpException();
    }

    // Vérifie si l'utilisateur est bien le propriétaire de la demande liée.
    if ($devis->hasField('field_demande_ref') && !$devis->get('field_demande_ref')->isEmpty()) {
      $demande = $devis->get('field_demande_ref')->entity;
      if ($demande && $demande->getOwnerId() != $this->currentUser()->id()) {
        throw new AccessDeniedHttpException();
      }
    }

    $devis->set('field_status', 'accepted');
    $devis->save();

    $this->messenger()->addStatus($this->t('Le devis a été accepté.'));
    return new RedirectResponse('/client/dashboard');
  }

  /**
   * Refuser un devis.
   */
  public function refuse($node)
  {
    $devis = Node::load($node);
    if (!$devis || $devis->bundle() !== 'devis') {
      throw new AccessDeniedHttpException();
    }

    if ($devis->hasField('field_demande_ref') && !$devis->get('field_demande_ref')->isEmpty()) {
      $demande = $devis->get('field_demande_ref')->entity;
      if ($demande && $demande->getOwnerId() != $this->currentUser()->id()) {
        throw new AccessDeniedHttpException();
      }
    }

    $devis->set('field_status', 'refused');
    $devis->save();

    $this->messenger()->addWarning($this->t('Le devis a été refusé.'));
    return new RedirectResponse('/client/dashboard');
  }
}
