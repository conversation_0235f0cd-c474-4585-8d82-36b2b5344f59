<?php

namespace Drupal\client_demandes\Controller;

use <PERSON><PERSON><PERSON>\Core\Controller\ControllerBase;
use <PERSON><PERSON><PERSON>\node\Entity\Node;
use <PERSON><PERSON>al\user\Entity\User;

/**
 * Contrôleur pour tester les notifications.
 */
class NotificationTestController extends ControllerBase
{

  /**
   * Page de test pour les notifications.
   */
  public function testPage()
  {
    $content = [];

    $content['title'] = [
      '#markup' => '<h1>Test des Notifications</h1>',
    ];

    $content['description'] = [
      '#markup' => '<p>Cette page permet de tester le système de notifications pour les nouvelles demandes.</p>',
    ];

    // Formulaire de test
    $content['test_form'] = [
      '#markup' => '
        <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3>🧪 Test de Notification</h3>
          <p>Pour tester le système :</p>
          <ol>
            <li><strong>Créer un prestataire :</strong> Créez un utilisateur avec le rôle "prestataire"</li>
            <li><strong>Ajouter des catégories :</strong> Assignez des catégories métier au prestataire</li>
            <li><strong>Publier une demande :</strong> Créez une nouvelle demande avec une catégorie correspondante</li>
            <li><strong>Vérifier :</strong> Le prestataire devrait recevoir un email et un message interne</li>
          </ol>
        </div>
      ',
    ];

    // Statistiques actuelles
    $stats = $this->getNotificationStats();

    $content['stats'] = [
      '#markup' => '
        <div style="background: #e8f5e8; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3>📊 Statistiques</h3>
          <ul>
            <li><strong>Prestataires actifs :</strong> ' . $stats['prestataires'] . '</li>
            <li><strong>Demandes publiées :</strong> ' . $stats['demandes'] . '</li>
            <li><strong>Messages envoyés :</strong> ' . $stats['messages'] . '</li>
          </ul>
        </div>
      ',
    ];

    // Bouton de test manuel
    $content['manual_test'] = [
      '#markup' => '
        <div style="background: #fff3cd; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3>🔧 Test Manuel</h3>
          <p>Vous pouvez tester manuellement en créant une demande fictive :</p>
          <a href="/admin/content/add/demande" class="button" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
            ➕ Créer une demande de test
          </a>
        </div>
      ',
    ];

    // Logs récents
    $content['logs'] = [
      '#markup' => '
        <div style="background: #f0f0f0; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3>📝 Logs Récents</h3>
          <p>Consultez les logs Drupal pour voir les notifications envoyées :</p>
          <a href="/admin/reports/dblog" class="button" style="background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
            📋 Voir les logs
          </a>
        </div>
      ',
    ];

    return $content;
  }

  /**
   * Récupère les statistiques de notification.
   */
  private function getNotificationStats()
  {
    // Compter les prestataires
    $prestataires_query = \Drupal::entityQuery('user')
      ->condition('status', 1)
      ->condition('roles', 'prestataire')
      ->accessCheck(TRUE);
    $prestataires_count = $prestataires_query->count()->execute();

    // Compter les demandes
    $demandes_query = \Drupal::entityQuery('node')
      ->condition('type', 'demande')
      ->condition('status', 1)
      ->accessCheck(TRUE);
    $demandes_count = $demandes_query->count()->execute();

    // Compter les messages (si le module Message est installé)
    $messages_count = 0;
    if (\Drupal::moduleHandler()->moduleExists('message')) {
      $messages_query = \Drupal::entityQuery('message')
        ->condition('template', 'nouvelle_demande_prestataire')
        ->accessCheck(TRUE);
      $messages_count = $messages_query->count()->execute();
    }

    return [
      'prestataires' => $prestataires_count,
      'demandes' => $demandes_count,
      'messages' => $messages_count,
    ];
  }

  /**
   * Test de création d'une demande fictive.
   */
  public function createTestDemande()
  {
    try {
      // Créer une demande de test
      $demande = Node::create([
        'type' => 'demande',
        'title' => 'Test - Demande de notification ' . date('Y-m-d H:i:s'),
        'field_description' => 'Ceci est une demande de test pour vérifier le système de notifications.',
        'field_price' => [
          'number' => 1000,
          'currency_code' => 'EUR',
        ],
        'field_localisation' => 'Test, France',
        'status' => 1,
        'uid' => \Drupal::currentUser()->id(),
      ]);

      // Ajouter une catégorie si elle existe
      if ($demande->hasField('field_categorie')) {
        $terms = \Drupal::entityTypeManager()
          ->getStorage('taxonomy_term')
          ->loadByProperties(['vid' => 'categories_services']);

        if (!empty($terms)) {
          $first_term = reset($terms);
          $demande->set('field_categorie', $first_term->id());
        }
      }

      $demande->save();

      \Drupal::messenger()->addStatus('Demande de test créée avec succès ! ID: ' . $demande->id());
    } catch (\Exception $e) {
      \Drupal::messenger()->addError('Erreur lors de la création de la demande de test: ' . $e->getMessage());
    }

    return $this->redirect('client_demandes.notification_test');
  }

  /**
   * Page de test pour les redirections de connexion.
   */
  public function loginRedirectTest()
  {
    $content = [];

    $content['title'] = [
      '#markup' => '<h1>Test des Redirections de Connexion</h1>',
    ];

    $content['description'] = [
      '#markup' => '<p>Cette page permet de tester les redirections automatiques après connexion.</p>',
    ];

    // Informations sur l'utilisateur actuel
    $current_user = \Drupal::currentUser();
    $user_info = '';

    if ($current_user->isAuthenticated()) {
      $account = \Drupal\user\Entity\User::load($current_user->id());
      $roles = $account->getRoles();
      $user_info = '
        <div style="background: #e8f5e8; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3>👤 Utilisateur Actuel</h3>
          <ul>
            <li><strong>Nom :</strong> ' . $account->getDisplayName() . '</li>
            <li><strong>Email :</strong> ' . $account->getEmail() . '</li>
            <li><strong>Rôles :</strong> ' . implode(', ', $roles) . '</li>
          </ul>
        </div>
      ';
    } else {
      $user_info = '
        <div style="background: #fff3cd; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3>⚠️ Non connecté</h3>
          <p>Vous devez être connecté pour tester les redirections.</p>
        </div>
      ';
    }

    $content['user_info'] = [
      '#markup' => $user_info,
    ];

    // Instructions de test
    $content['instructions'] = [
      '#markup' => '
        <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3>🧪 Comment Tester</h3>
          <ol>
            <li><strong>Créer des utilisateurs test :</strong>
              <ul>
                <li>Un utilisateur avec le rôle "prestataire"</li>
                <li>Un utilisateur avec le rôle "client"</li>
              </ul>
            </li>
            <li><strong>Se déconnecter</strong> (si connecté)</li>
            <li><strong>Se reconnecter</strong> avec un compte prestataire</li>
            <li><strong>Vérifier</strong> la redirection vers <code>/prestataire/dashboard</code></li>
            <li><strong>Répéter</strong> avec un compte client</li>
            <li><strong>Vérifier</strong> la redirection vers <code>/client/dashboard</code></li>
          </ol>
        </div>
      ',
    ];

    // Liens utiles
    $content['links'] = [
      '#markup' => '
        <div style="background: #cce5ff; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3>🔗 Liens Utiles</h3>
          <ul>
            <li><a href="/user/logout" class="button" style="background: #dc3545; color: white; padding: 8px 15px; text-decoration: none; border-radius: 5px;">🚪 Se déconnecter</a></li>
            <li><a href="/user/login" class="button" style="background: #007bff; color: white; padding: 8px 15px; text-decoration: none; border-radius: 5px;">🔑 Page de connexion</a></li>
            <li><a href="/prestataire/dashboard" class="button" style="background: #28a745; color: white; padding: 8px 15px; text-decoration: none; border-radius: 5px;">📊 Dashboard Prestataire</a></li>
            <li><a href="/client/dashboard" class="button" style="background: #17a2b8; color: white; padding: 8px 15px; text-decoration: none; border-radius: 5px;">📋 Dashboard Client</a></li>
          </ul>
        </div>
      ',
    ];

    return $content;
  }
}
