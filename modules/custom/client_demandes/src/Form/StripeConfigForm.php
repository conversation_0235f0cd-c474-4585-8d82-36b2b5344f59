<?php

namespace Drupal\client_demandes\Form;

use <PERSON>upal\Core\Form\ConfigFormBase;
use <PERSON>upal\Core\Form\FormStateInterface;

/**
 * Formulaire de configuration Stripe.
 */
class StripeConfigForm extends ConfigFormBase {

  /**
   * {@inheritdoc}
   */
  protected function getEditableConfigNames() {
    return ['client_demandes.settings'];
  }

  /**
   * {@inheritdoc}
   */
  public function getFormId() {
    return 'client_demandes_stripe_config_form';
  }

  /**
   * {@inheritdoc}
   */
  public function buildForm(array $form, FormStateInterface $form_state) {
    $config = $this->config('client_demandes.settings');

    $form['stripe_settings'] = [
      '#type' => 'fieldset',
      '#title' => $this->t('Configuration Stripe Connect'),
      '#description' => $this->t('Configurez vos clés API Stripe pour permettre aux prestataires de connecter leurs comptes.'),
    ];

    $form['stripe_settings']['stripe_mode'] = [
      '#type' => 'radios',
      '#title' => $this->t('Mode Stripe'),
      '#default_value' => $config->get('stripe_mode') ?: 'test',
      '#options' => [
        'test' => $this->t('Test (Sandbox)'),
        'live' => $this->t('Production (Live)'),
      ],
      '#description' => $this->t('Sélectionnez le mode de fonctionnement de Stripe.'),
    ];

    $form['stripe_settings']['stripe_client_id'] = [
      '#type' => 'textfield',
      '#title' => $this->t('Client ID Stripe Connect'),
      '#default_value' => $config->get('stripe_client_id'),
      '#description' => $this->t('Votre Client ID Stripe Connect (commence par ca_).'),
      '#required' => TRUE,
    ];

    $form['stripe_settings']['stripe_secret_key'] = [
      '#type' => 'password',
      '#title' => $this->t('Clé secrète Stripe'),
      '#default_value' => $config->get('stripe_secret_key'),
      '#description' => $this->t('Votre clé secrète Stripe (commence par sk_). Cette clé ne sera pas affichée après sauvegarde.'),
      '#attributes' => [
        'placeholder' => $config->get('stripe_secret_key') ? '••••••••••••••••' : '',
      ],
    ];

    $form['stripe_settings']['stripe_webhook_secret'] = [
      '#type' => 'textfield',
      '#title' => $this->t('Secret Webhook Stripe'),
      '#default_value' => $config->get('stripe_webhook_secret'),
      '#description' => $this->t('Secret pour valider les webhooks Stripe (commence par whsec_).'),
    ];

    $form['payment_settings'] = [
      '#type' => 'fieldset',
      '#title' => $this->t('Configuration des Paiements'),
    ];

    $form['payment_settings']['commission_rate'] = [
      '#type' => 'number',
      '#title' => $this->t('Taux de commission'),
      '#default_value' => $config->get('commission_rate') ?: 0.05,
      '#min' => 0,
      '#max' => 1,
      '#step' => 0.01,
      '#description' => $this->t('Taux de commission prélevé sur chaque transaction (0.05 = 5%).'),
    ];

    $form['notification_settings'] = [
      '#type' => 'fieldset',
      '#title' => $this->t('Configuration des Notifications'),
    ];

    $form['notification_settings']['notifications_enabled'] = [
      '#type' => 'checkbox',
      '#title' => $this->t('Activer les notifications'),
      '#default_value' => $config->get('notifications_enabled') ?? TRUE,
    ];

    $form['notification_settings']['email_notifications'] = [
      '#type' => 'checkbox',
      '#title' => $this->t('Notifications par email'),
      '#default_value' => $config->get('email_notifications') ?? TRUE,
      '#states' => [
        'visible' => [
          ':input[name="notifications_enabled"]' => ['checked' => TRUE],
        ],
      ],
    ];

    $form['notification_settings']['message_notifications'] = [
      '#type' => 'checkbox',
      '#title' => $this->t('Notifications par message interne'),
      '#default_value' => $config->get('message_notifications') ?? TRUE,
      '#states' => [
        'visible' => [
          ':input[name="notifications_enabled"]' => ['checked' => TRUE],
        ],
      ],
    ];

    $form['help'] = [
      '#type' => 'fieldset',
      '#title' => $this->t('Aide'),
      '#collapsible' => TRUE,
      '#collapsed' => TRUE,
    ];

    $form['help']['instructions'] = [
      '#markup' => '
        <div style="background: #f8f9fa; padding: 20px; border-radius: 8px;">
          <h3>Instructions de configuration Stripe Connect :</h3>
          <ol>
            <li>Créez un compte Stripe sur <a href="https://stripe.com" target="_blank">stripe.com</a></li>
            <li>Activez Stripe Connect dans votre tableau de bord Stripe</li>
            <li>Récupérez votre Client ID et votre clé secrète</li>
            <li>Configurez l\'URL de redirection : <code>' . \Drupal::request()->getSchemeAndHttpHost() . '/prestataire/stripe/callback</code></li>
            <li>Saisissez vos clés dans ce formulaire</li>
          </ol>
          
          <h4>URLs importantes :</h4>
          <ul>
            <li><strong>Redirect URI :</strong> <code>' . \Drupal::request()->getSchemeAndHttpHost() . '/prestataire/stripe/callback</code></li>
            <li><strong>Webhook URL :</strong> <code>' . \Drupal::request()->getSchemeAndHttpHost() . '/stripe/webhook</code></li>
          </ul>
        </div>
      ',
    ];

    return parent::buildForm($form, $form_state);
  }

  /**
   * {@inheritdoc}
   */
  public function submitForm(array &$form, FormStateInterface $form_state) {
    $config = $this->config('client_demandes.settings');
    
    $config->set('stripe_mode', $form_state->getValue('stripe_mode'));
    $config->set('stripe_client_id', $form_state->getValue('stripe_client_id'));
    $config->set('stripe_webhook_secret', $form_state->getValue('stripe_webhook_secret'));
    $config->set('commission_rate', $form_state->getValue('commission_rate'));
    $config->set('notifications_enabled', $form_state->getValue('notifications_enabled'));
    $config->set('email_notifications', $form_state->getValue('email_notifications'));
    $config->set('message_notifications', $form_state->getValue('message_notifications'));
    
    // Ne sauvegarder la clé secrète que si elle a été modifiée
    $stripe_secret_key = $form_state->getValue('stripe_secret_key');
    if (!empty($stripe_secret_key) && $stripe_secret_key !== '••••••••••••••••') {
      $config->set('stripe_secret_key', $stripe_secret_key);
    }
    
    $config->save();

    $this->messenger()->addStatus($this->t('La configuration Stripe a été sauvegardée.'));
    
    parent::submitForm($form, $form_state);
  }

}
