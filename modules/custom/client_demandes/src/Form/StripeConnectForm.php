<?php

namespace Drupal\client_demandes\Form;

use <PERSON><PERSON>al\Core\Form\FormBase;
use <PERSON><PERSON><PERSON>\Core\Form\FormStateInterface;
use <PERSON><PERSON><PERSON>\user\Entity\User;
use Drupal\Core\Session\AccountInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;

/**
 * Form pour que les prestataires saisissent leur ID Stripe Connect.
 */
class StripeConnectForm extends FormBase {

  /**
   * The current user.
   *
   * @var \Drupal\Core\Session\AccountInterface
   */
  protected $currentUser;

  /**
   * Constructs a new StripeConnectForm.
   *
   * @param \Drupal\Core\Session\AccountInterface $current_user
   *   The current user.
   */
  public function __construct(AccountInterface $current_user) {
    $this->currentUser = $current_user;
  }

  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container) {
    return new static(
      $container->get('current_user')
    );
  }

  /**
   * {@inheritdoc}
   */
  public function getFormId() {
    return 'client_demandes_stripe_connect_form';
  }

  /**
   * {@inheritdoc}
   */
  public function buildForm(array $form, FormStateInterface $form_state) {
    $user = User::load($this->currentUser->id());
    
    // Récupérer les valeurs actuelles
    $stripe_account_id = '';
    $stripe_user_id = '';
    
    if ($user->hasField('field_stripe_account_id') && !$user->get('field_stripe_account_id')->isEmpty()) {
      $stripe_account_id = $user->get('field_stripe_account_id')->value;
    }
    
    if ($user->hasField('field_stripe_user_id') && !$user->get('field_stripe_user_id')->isEmpty()) {
      $stripe_user_id = $user->get('field_stripe_user_id')->value;
    }

    $form['#attached']['library'][] = 'client_demandes/prestataire_dashboard';

    $form['intro'] = [
      '#markup' => '
        <div class="stripe-connect-form-intro">
          <h2>Configuration Stripe Connect</h2>
          <p>Saisissez votre ID de compte Stripe Connect pour recevoir des paiements directement.</p>
        </div>
      ',
    ];

    $form['current_status'] = [
      '#type' => 'fieldset',
      '#title' => $this->t('Statut actuel'),
      '#collapsible' => FALSE,
    ];

    $status = 'Non connecté';
    $status_class = 'status-refused';
    if ($stripe_account_id || $stripe_user_id) {
      $status = 'Connecté';
      $status_class = 'status-accepted';
    }

    $form['current_status']['status_display'] = [
      '#markup' => '<p><strong>Statut :</strong> <span class="badge ' . $status_class . '">' . $status . '</span></p>',
    ];

    if ($stripe_account_id) {
      $form['current_status']['current_account_id'] = [
        '#markup' => '<p><strong>ID Compte Stripe :</strong> <code>' . $stripe_account_id . '</code></p>',
      ];
    }

    if ($stripe_user_id) {
      $form['current_status']['current_user_id'] = [
        '#markup' => '<p><strong>ID Utilisateur Stripe (legacy) :</strong> <code>' . $stripe_user_id . '</code></p>',
      ];
    }

    $form['stripe_fields'] = [
      '#type' => 'fieldset',
      '#title' => $this->t('Informations Stripe Connect'),
      '#collapsible' => FALSE,
    ];

    $form['stripe_fields']['stripe_account_id'] = [
      '#type' => 'textfield',
      '#title' => $this->t('ID Compte Stripe Connect'),
      '#description' => $this->t('Votre ID de compte Stripe Connect (commence par "acct_"). Exemple: acct_1234567890'),
      '#default_value' => $stripe_account_id,
      '#maxlength' => 255,
      '#placeholder' => 'acct_...',
    ];

    $form['stripe_fields']['stripe_user_id'] = [
      '#type' => 'textfield',
      '#title' => $this->t('ID Utilisateur Stripe (legacy)'),
      '#description' => $this->t('Votre ancien ID utilisateur Stripe si vous en avez un.'),
      '#default_value' => $stripe_user_id,
      '#maxlength' => 255,
      '#placeholder' => 'acct_... ou ca_...',
    ];

    $form['help'] = [
      '#type' => 'fieldset',
      '#title' => $this->t('Comment obtenir votre ID Stripe Connect ?'),
      '#collapsible' => TRUE,
      '#collapsed' => TRUE,
    ];

    $form['help']['instructions'] = [
      '#markup' => '
        <div class="stripe-help">
          <ol>
            <li><strong>Créez un compte Stripe :</strong> Rendez-vous sur <a href="https://stripe.com" target="_blank">stripe.com</a></li>
            <li><strong>Activez Stripe Connect :</strong> Dans votre tableau de bord Stripe, activez Stripe Connect</li>
            <li><strong>Trouvez votre ID :</strong> Dans les paramètres de votre compte, copiez votre ID de compte (commence par "acct_")</li>
            <li><strong>Collez-le ici :</strong> Saisissez votre ID dans le champ ci-dessus et enregistrez</li>
          </ol>
          <p><strong>Note :</strong> Votre ID de compte Stripe Connect est nécessaire pour recevoir des paiements via notre plateforme.</p>
        </div>
      ',
    ];

    $form['actions'] = [
      '#type' => 'actions',
    ];

    $form['actions']['submit'] = [
      '#type' => 'submit',
      '#value' => $this->t('Enregistrer la configuration Stripe'),
      '#button_type' => 'primary',
    ];

    $form['actions']['cancel'] = [
      '#type' => 'link',
      '#title' => $this->t('Annuler'),
      '#url' => \Drupal\Core\Url::fromRoute('client_demandes.prestataire_dashboard'),
      '#attributes' => ['class' => ['button']],
    ];

    return $form;
  }

  /**
   * {@inheritdoc}
   */
  public function validateForm(array &$form, FormStateInterface $form_state) {
    $stripe_account_id = $form_state->getValue('stripe_account_id');
    $stripe_user_id = $form_state->getValue('stripe_user_id');

    // Validation de l'ID de compte Stripe
    if (!empty($stripe_account_id)) {
      if (!preg_match('/^acct_[a-zA-Z0-9]+$/', $stripe_account_id)) {
        $form_state->setErrorByName('stripe_account_id', $this->t('L\'ID de compte Stripe doit commencer par "acct_" suivi de caractères alphanumériques.'));
      }
    }

    // Validation de l'ID utilisateur Stripe (legacy)
    if (!empty($stripe_user_id)) {
      if (!preg_match('/^(acct_|ca_)[a-zA-Z0-9]+$/', $stripe_user_id)) {
        $form_state->setErrorByName('stripe_user_id', $this->t('L\'ID utilisateur Stripe doit commencer par "acct_" ou "ca_" suivi de caractères alphanumériques.'));
      }
    }

    // Au moins un ID doit être fourni
    if (empty($stripe_account_id) && empty($stripe_user_id)) {
      $form_state->setErrorByName('stripe_account_id', $this->t('Veuillez fournir au moins un ID Stripe (compte ou utilisateur).'));
    }
  }

  /**
   * {@inheritdoc}
   */
  public function submitForm(array &$form, FormStateInterface $form_state) {
    $user = User::load($this->currentUser->id());
    $stripe_account_id = $form_state->getValue('stripe_account_id');
    $stripe_user_id = $form_state->getValue('stripe_user_id');

    // Sauvegarder l'ID de compte Stripe
    if ($user->hasField('field_stripe_account_id')) {
      $user->set('field_stripe_account_id', $stripe_account_id);
    }

    // Sauvegarder l'ID utilisateur Stripe (legacy)
    if ($user->hasField('field_stripe_user_id')) {
      $user->set('field_stripe_user_id', $stripe_user_id);
    }

    $user->save();

    $this->messenger()->addStatus($this->t('Votre configuration Stripe Connect a été enregistrée avec succès.'));
    
    // Rediriger vers le tableau de bord
    $form_state->setRedirect('client_demandes.prestataire_dashboard');
  }

}
