<?php

namespace Drupal\client_demandes\Form;

use <PERSON><PERSON>al\Core\Form\FormBase;
use <PERSON><PERSON>al\Core\Form\FormStateInterface;
use <PERSON><PERSON>al\user\Entity\User;
use Drupal\Core\Session\AccountInterface;
use Drupal\Core\Url;
use Symfony\Component\DependencyInjection\ContainerInterface;

/**
 * Form pour que les prestataires saisissent leur ID Stripe Connect.
 */
class StripeConnectForm extends FormBase
{

  /**
   * The current user.
   *
   * @var \Drupal\Core\Session\AccountInterface
   */
  protected $currentUser;

  /**
   * Constructs a new StripeConnectForm.
   *
   * @param \Drupal\Core\Session\AccountInterface $current_user
   *   The current user.
   */
  public function __construct(AccountInterface $current_user)
  {
    $this->currentUser = $current_user;
  }

  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container)
  {
    return new static(
      $container->get('current_user')
    );
  }

  /**
   * {@inheritdoc}
   */
  public function getFormId()
  {
    return 'client_demandes_stripe_connect_form';
  }

  /**
   * {@inheritdoc}
   */
  public function buildForm(array $form, FormStateInterface $form_state)
  {
    $current_user = \Drupal::currentUser();
    $user = User::load($current_user->id());

    // Charger le profil prestataire et récupérer les valeurs actuelles
    $stripe_account_id = '';
    $stripe_publishable_key = '';
    $has_secret_key = FALSE;

    $profile_storage = \Drupal::entityTypeManager()->getStorage('profile');
    $prestataire_profile = $profile_storage->loadByUser($user, 'prestataire');

    if ($prestataire_profile) {
      if ($prestataire_profile->hasField('field_stripe_account_id') && !$prestataire_profile->get('field_stripe_account_id')->isEmpty()) {
        $stripe_account_id = $prestataire_profile->get('field_stripe_account_id')->value;
      }

      if ($prestataire_profile->hasField('field_stripe_publishable_key') && !$prestataire_profile->get('field_stripe_publishable_key')->isEmpty()) {
        $stripe_publishable_key = $prestataire_profile->get('field_stripe_publishable_key')->value;
      }

      if ($prestataire_profile->hasField('field_stripe_access_token') && !$prestataire_profile->get('field_stripe_access_token')->isEmpty()) {
        $has_secret_key = TRUE;
      }
    }

    $form['#attached']['library'][] = 'client_demandes/prestataire_dashboard';

    $form['intro'] = [
      '#markup' => '
        <div class="stripe-connect-form-intro">
          <h2>🔗 Configuration Stripe Connect</h2>
          <p>Configurez vos informations Stripe pour recevoir des paiements directement de vos clients.</p>
        </div>
      ',
    ];

    $form['current_status'] = [
      '#type' => 'fieldset',
      '#title' => $this->t('Statut actuel'),
      '#collapsible' => FALSE,
    ];

    $status = 'Non connecté';
    $status_class = 'status-refused';
    if ($stripe_account_id && ($stripe_publishable_key || $has_secret_key)) {
      $status = 'Connecté';
      $status_class = 'status-accepted';
    } elseif ($stripe_account_id) {
      $status = 'Partiellement configuré';
      $status_class = 'status-pending';
    }

    $form['current_status']['status_display'] = [
      '#markup' => '<p><strong>Statut :</strong> <span class="badge ' . $status_class . '">' . $status . '</span></p>',
    ];

    if ($stripe_account_id) {
      $form['current_status']['current_account_id'] = [
        '#markup' => '<p><strong>ID Compte Stripe :</strong> <code>' . $stripe_account_id . '</code></p>',
      ];
    }

    if ($stripe_publishable_key) {
      $form['current_status']['current_publishable_key'] = [
        '#markup' => '<p><strong>Clé publique :</strong> <code>' . substr($stripe_publishable_key, 0, 20) . '...</code></p>',
      ];
    }

    if ($has_secret_key) {
      $form['current_status']['current_secret_key'] = [
        '#markup' => '<p><strong>Clé secrète :</strong> <span style="color: green;">✓ Configurée</span></p>',
      ];
    }

    $form['stripe_fields'] = [
      '#type' => 'fieldset',
      '#title' => $this->t('Informations Stripe'),
      '#description' => $this->t('Saisissez vos identifiants Stripe. Vous pouvez les trouver dans votre tableau de bord Stripe.'),
      '#collapsible' => FALSE,
    ];

    $form['stripe_fields']['stripe_account_id'] = [
      '#type' => 'textfield',
      '#title' => $this->t('ID de compte Stripe'),
      '#description' => $this->t('Votre identifiant de compte Stripe (commence par acct_). Trouvez-le dans Paramètres > Informations sur le compte.'),
      '#default_value' => $stripe_account_id,
      '#maxlength' => 255,
      '#placeholder' => 'acct_1234567890abcdef',
      '#required' => TRUE,
    ];

    $form['stripe_fields']['stripe_publishable_key'] = [
      '#type' => 'textfield',
      '#title' => $this->t('Clé publique Stripe'),
      '#description' => $this->t('Votre clé publique Stripe (commence par pk_). Trouvez-la dans Développeurs > Clés API.'),
      '#default_value' => $stripe_publishable_key,
      '#maxlength' => 255,
      '#placeholder' => 'pk_live_... ou pk_test_...',
      '#required' => TRUE,
    ];

    $form['stripe_fields']['stripe_secret_key'] = [
      '#type' => 'password',
      '#title' => $this->t('Clé secrète Stripe'),
      '#description' => $this->t('Votre clé secrète Stripe (commence par sk_). Cette clé ne sera pas affichée après sauvegarde.') .
        ($has_secret_key ? '<br><em>Laissez vide pour conserver la clé actuelle.</em>' : ''),
      '#maxlength' => 255,
      '#placeholder' => 'sk_live_... ou sk_test_...',
      '#required' => !$has_secret_key,
    ];

    $form['help'] = [
      '#type' => 'details',
      '#title' => $this->t('Aide - Comment trouver mes clés Stripe ?'),
      '#collapsed' => TRUE,
    ];

    $form['help']['instructions'] = [
      '#markup' => '
        <div class="stripe-help">
          <h4>📋 Instructions étape par étape :</h4>
          <ol>
            <li>Connectez-vous à votre <a href="https://dashboard.stripe.com" target="_blank">tableau de bord Stripe</a></li>
            <li><strong>Pour l\'ID de compte :</strong>
              <ul>
                <li>Allez dans "Paramètres" → "Informations sur le compte"</li>
                <li>Copiez l\'ID qui commence par "acct_"</li>
              </ul>
            </li>
            <li><strong>Pour les clés API :</strong>
              <ul>
                <li>Allez dans "Développeurs" → "Clés API"</li>
                <li>Copiez la "Clé publique" (pk_...)</li>
                <li>Révélez et copiez la "Clé secrète" (sk_...)</li>
              </ul>
            </li>
            <li><strong>Mode Test vs Live :</strong>
              <ul>
                <li>Utilisez les clés de test (pk_test_, sk_test_) pour les tests</li>
                <li>Utilisez les clés live (pk_live_, sk_live_) pour la production</li>
              </ul>
            </li>
          </ol>

          <h4>🔒 Sécurité :</h4>
          <ul>
            <li>Ne partagez jamais votre clé secrète</li>
            <li>Vos clés sont stockées de manière sécurisée</li>
            <li>Seuls vous et les administrateurs peuvent voir ces informations</li>
          </ul>

          <h4>❓ Besoin d\'aide ?</h4>
          <p>Si vous n\'avez pas encore de compte Stripe, créez-en un sur <a href="https://stripe.com" target="_blank">stripe.com</a></p>
        </div>
      ',
    ];

    $form['actions'] = [
      '#type' => 'actions',
    ];

    $form['actions']['submit'] = [
      '#type' => 'submit',
      '#value' => $this->t('Enregistrer la configuration Stripe'),
      '#button_type' => 'primary',
    ];

    $form['actions']['cancel'] = [
      '#type' => 'link',
      '#title' => $this->t('Annuler'),
      '#url' => Url::fromUserInput('/prestataire/dashboard'),
      '#attributes' => ['class' => ['button']],
    ];

    return $form;
  }

  /**
   * {@inheritdoc}
   */
  public function validateForm(array &$form, FormStateInterface $form_state)
  {
    $stripe_account_id = $form_state->getValue('stripe_account_id');
    $stripe_publishable_key = $form_state->getValue('stripe_publishable_key');
    $stripe_secret_key = $form_state->getValue('stripe_secret_key');

    // Valider l'ID de compte Stripe
    if (!preg_match('/^acct_[a-zA-Z0-9]+$/', $stripe_account_id)) {
      $form_state->setErrorByName('stripe_account_id', $this->t('L\'ID de compte Stripe doit commencer par "acct_" suivi de caractères alphanumériques.'));
    }

    // Valider la clé publique
    if (!preg_match('/^pk_(test_|live_)[a-zA-Z0-9]+$/', $stripe_publishable_key)) {
      $form_state->setErrorByName('stripe_publishable_key', $this->t('La clé publique Stripe doit commencer par "pk_test_" ou "pk_live_".'));
    }

    // Valider la clé secrète (si fournie)
    if (!empty($stripe_secret_key) && !preg_match('/^sk_(test_|live_)[a-zA-Z0-9]+$/', $stripe_secret_key)) {
      $form_state->setErrorByName('stripe_secret_key', $this->t('La clé secrète Stripe doit commencer par "sk_test_" ou "sk_live_".'));
    }

    // Vérifier la cohérence test/live
    $is_publishable_test = strpos($stripe_publishable_key, 'pk_test_') === 0;
    $is_secret_test = !empty($stripe_secret_key) && strpos($stripe_secret_key, 'sk_test_') === 0;
    $is_secret_live = !empty($stripe_secret_key) && strpos($stripe_secret_key, 'sk_live_') === 0;

    if (!empty($stripe_secret_key)) {
      if ($is_publishable_test && !$is_secret_test) {
        $form_state->setErrorByName('stripe_secret_key', $this->t('La clé secrète doit être en mode test (sk_test_) pour correspondre à la clé publique test.'));
      }
      if (!$is_publishable_test && !$is_secret_live) {
        $form_state->setErrorByName('stripe_secret_key', $this->t('La clé secrète doit être en mode live (sk_live_) pour correspondre à la clé publique live.'));
      }
    }
  }

  /**
   * {@inheritdoc}
   */
  public function submitForm(array &$form, FormStateInterface $form_state)
  {
    $current_user = \Drupal::currentUser();
    $user = User::load($current_user->id());

    $stripe_account_id = $form_state->getValue('stripe_account_id');
    $stripe_publishable_key = $form_state->getValue('stripe_publishable_key');
    $stripe_secret_key = $form_state->getValue('stripe_secret_key');

    // Charger le profil prestataire
    $profile_storage = \Drupal::entityTypeManager()->getStorage('profile');
    $prestataire_profile = $profile_storage->loadByUser($user, 'prestataire');

    if (!$prestataire_profile) {
      $this->messenger()->addError($this->t('Profil prestataire introuvable.'));
      return;
    }

    // Sauvegarder l'ID de compte dans le profil
    if ($prestataire_profile->hasField('field_stripe_account_id')) {
      $prestataire_profile->set('field_stripe_account_id', $stripe_account_id);
    }

    // Sauvegarder la clé publique dans le profil
    if ($prestataire_profile->hasField('field_stripe_publishable_key')) {
      $prestataire_profile->set('field_stripe_publishable_key', $stripe_publishable_key);
    }

    // Sauvegarder la clé secrète (si fournie) dans le profil
    if (!empty($stripe_secret_key) && $prestataire_profile->hasField('field_stripe_access_token')) {
      $prestataire_profile->set('field_stripe_access_token', $stripe_secret_key);
    }

    $prestataire_profile->save();

    // Message de confirmation
    $this->messenger()->addStatus($this->t('Votre configuration Stripe a été sauvegardée avec succès.'));

    // Log pour audit
    \Drupal::logger('client_demandes')->info('Configuration Stripe mise à jour pour l\'utilisateur @uid', [
      '@uid' => $user->id(),
    ]);

    // Rediriger vers le tableau de bord
    $form_state->setRedirect('client_demandes.prestataire_dashboard');
  }
}
