# Menu links for Client Demandes module

client_demandes.admin:
  title: 'Client Demandes'
  description: 'Configuration du module Client Demandes'
  route_name: client_demandes.notification_test
  parent: system.admin_config_services
  weight: 10

client_demandes.stripe_config:
  title: 'Configuration Stripe Connect'
  description: 'Configurez les clés API Stripe Connect'
  route_name: client_demandes.stripe_connect_config
  parent: client_demandes.admin
  weight: 20

client_demandes.field_diagnostic:
  title: 'Diagnostic des Champs'
  description: 'Vérifiez la configuration des champs'
  route_name: client_demandes.field_diagnostic
  parent: client_demandes.admin
  weight: 30

client_demandes.tests:
  title: 'Tests et Diagnostics'
  description: 'Pages de test du module'
  parent: client_demandes.admin
  weight: 40
