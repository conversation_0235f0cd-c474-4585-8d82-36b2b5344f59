{#
  Template : prestataire_dashboard.html.twig
  Affiche les demandes compatibles pour un prestataire.
#}

<div class="layout-content">
	<div class="container">
		<h1>{{ 'Tableau de bord prestataire'|t }}</h1>

		<div class="dashboard-summary">
			{{ summary }}
		</div>

		<div class="dashboard-notifications">
			{{ notifications }}
		</div>

		{# Section Stripe Connect #}
		<div class="stripe-connect-section">
			<h2>{{ 'Stripe Connect'|t }}</h2>
			<div class="stripe-info">
				<p>
					<strong>{{ 'Statut :'|t }}</strong>
					{% if stripe_connect_info.status == 'Connecté' %}
						<span class="badge status-accepted">{{ stripe_connect_info.status }}</span>
					{% else %}
						<span class="badge status-pending">{{ stripe_connect_info.status }}</span>
					{% endif %}
				</p>
				{% if stripe_connect_info.account_id %}
					<p>
						<strong>{{ 'ID Compte :'|t }}</strong>
						<code>{{ stripe_connect_info.account_id }}</code>
					</p>
				{% endif %}
				<div class="stripe-actions">
					{% if stripe_connect_info.connect_url %}
						<a href="{{ stripe_connect_info.connect_url }}" class="button button--primary">
							💳
							{{ 'Connecter Stripe'|t }}
						</a>
					{% endif %}
					<a href="/prestataire/stripe/configure" class="button">
						⚙️
						{{ 'Configurer mon ID Stripe'|t }}
					</a>
				</div>
			</div>
		</div>

		{# Section 3 Valeurs Clés #}
		<div class="quotes-stats-section">
			<h2>{{ 'Mes 3 indicateurs clés'|t }}</h2>
			<div class="stats-grid-three">
				<div class="stat-item stat-earnings">
					<span class="stat-number">{{ stripe_stats.total_earnings|number_format(2, ',', ' ') }}€</span>
					<span class="stat-label">{{ 'Revenus totaux'|t }}</span>
					<span class="stat-detail">{{ stripe_stats.transaction_count }}
						{{ 'transactions'|t }}</span>
				</div>
				<div class="stat-item stat-accepted">
					<span class="stat-number">{{ sent_quotes_stats.accepted }}</span>
					<span class="stat-label">{{ 'Devis acceptés'|t }}</span>
					<span class="stat-detail">{{ 'sur'|t }}
						{{ sent_quotes_stats.total_sent }}
						{{ 'envoyés'|t }}</span>
				</div>
				<div class="stat-item stat-success">
					<span class="stat-number">{{ sent_quotes_stats.success_rate }}%</span>
					<span class="stat-label">{{ 'Taux de réussite'|t }}</span>
					<span class="stat-detail">{{ 'Conversion devis'|t }}</span>
				</div>
			</div>
		</div>

		<h2>{{ 'Demandes compatibles avec votre domaine'|t }}</h2>

		{% if demandes|length %}
			<div class="demandes-list">
				{% for demande in demandes %}
					<div class="demande-card">
						<h3>{{ demande.label }}</h3>

						<div class="demande-meta">
							<p>
								<strong>{{ 'Publié le'|t }}:</strong>
								{{ demande.created.value|date('d/m/Y') }}</p>
							{% if demande.field_localisation is defined and demande.field_localisation.value %}
								<p>
									<strong>{{ 'Localisation'|t }}:</strong>
									{{ demande.field_localisation.value }}</p>
							{% endif %}
							{% if demande.field_budget is defined and demande.field_budget.value %}
								<p class="demande-budget">
									<strong>{{ 'Budget client'|t }}:</strong>
									<span class="budget-amount">{{ demande.field_budget.value|number_format(2, ',', ' ') }}
										€</span>
								</p>
							{% endif %}

							{# Commerce Price Field - Multiple ways to access #}
							{% if demande.field_prix is defined and not demande.field_prix.isEmpty %}
								{% set price_value = null %}
								{% if demande.field_prix.0.number %}
									{% set price_value = demande.field_prix.0.number %}
								{% elseif demande.field_prix.value %}
									{% set price_value = demande.field_prix.value %}
								{% elseif demande.field_prix.0.value %}
									{% set price_value = demande.field_prix.0.value %}
								{% endif %}

								{% if price_value %}
									<p class="demande-price">
										<strong>{{ 'Prix proposé'|t }}:</strong>
										<span class="price-amount">{{ price_value|number_format(2, ',', ' ') }}
											€</span>
									</p>
								{% endif %}
							{% endif %}

							{# Alternative: Try to render the field directly #}
							{% if demande.field_prix is defined and not demande.field_prix.isEmpty and not price_value %}
								<p class="demande-price">
									<strong>{{ 'Prix proposé'|t }}:</strong>
									<span class="price-amount">{{ demande.field_prix }}</span>
								</p>
							{% endif %}
							{% if demande.field_delai is defined and demande.field_delai.value %}
								<p>
									<strong>{{ 'Délai'|t }}:</strong>
									{{ demande.field_delai.value }}</p>
							{% endif %}
						</div>

						<div class="demande-actions">
							<a href="{{ path('entity.node.canonical', {'node': demande.id}) }}" class="button">
								{{ 'Voir la demande'|t }}
							</a>
							<a href="{{ path('client_demandes.send_quote_form', {'node': demande.id}) }}" class="button button--primary">
								📝
								{{ 'Créer un devis'|t }}
							</a>
						</div>
					</div>
				{% endfor %}
			</div>
		{% else %}
			<div class="no-demandes">
				<p>{{ 'Aucune demande correspondante trouvée.'|t }}</p>
				<p>{{ 'Les demandes apparaîtront ici selon votre catégorie métier.'|t }}</p>
			</div>
		{% endif %}

		{# Section des devis envoyés récemment #}
		<div class="recent-quotes-section">
			<h2>{{ 'Mes devis envoyés'|t }}</h2>
			<p>{{ 'Consultez et gérez tous vos devis envoyés aux clients.'|t }}</p>
			<a href="{{ path('client_demandes.my_sent_offers') }}" class="button">
				📋
				{{ 'Voir tous mes devis'|t }}
			</a>
		</div>
	</div>
</div>
