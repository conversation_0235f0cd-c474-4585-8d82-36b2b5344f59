{# templates/client-dashboard.html.twig #}
<div class="layout-content container">
	<h1>{{ 'Tableau de bord client'|t }}</h1>

	<div class="dashboard-actions">
		{{ actions }}
	</div>

	<h2>{{ 'Mes demandes publiées'|t }}</h2>
	{% if demandes|length > 0 %}
		<ul class="client-demandes-list">
			{% for demande in demandes %}
				<li>
					<div class="demande-item">
						<h3>{{ demande.label }}</h3>
						<p>
							<strong>{{ 'Publié le'|t }}
								:</strong>
							{{ demande.created.value|date('d/m/Y') }}</p>
						<div class="demande-actions">
							<a href="{{ path('entity.node.canonical', {'node': demande.id}) }}" class="button">
								👁️
								{{ 'Voir la demande'|t }}
							</a>
							<a href="{{ path('entity.node.edit_form', {'node': demande.id}) }}" class="button button--secondary">
								✏️
								{{ 'Modifier'|t }}
							</a>
						</div>
					</div>
				</li>
			{% endfor %}
		</ul>
	{% else %}
		<p>{{ 'Vous n’avez publié aucune demande pour l’instant.'|t }}</p>
	{% endif %}

	<h2>{{ 'Devis reçus'|t }}</h2>
	{% if devis_proposes|length > 0 %}
		<ul class="client-devis-list">
			{% for devis in devis_proposes %}
				<li>
					<div class="devis-item">
						<h3>{{ devis.label }}</h3>
						<p>
							<strong>{{ 'Reçu le'|t }}
								:</strong>
							{{ devis.created.value|date('d/m/Y') }}</p>
						{% if devis.prestataire_user %}
							<p>
								<strong>{{ 'Prestataire :'|t }}</strong>
								{{ devis.prestataire_user.getDisplayName() }}</p>
						{% endif %}
						<a href="{{ path('entity.node.canonical', {'node': devis.id}) }}" class="button">
							{{ 'Voir le devis'|t }}
						</a>

						<div class="devis-actions">
							{% if devis.field_status.value == 'accepted' %}
								<span class="badge badge-success">{{ 'Accepté'|t }}</span>
							{% elseif devis.field_status.value == 'refused' %}
								<span class="badge badge-danger">{{ 'Refusé'|t }}</span>
							{% else %}
								<a href="{{ path('client_demandes.accept_devis', {'devis': devis.id}) }}" class="button button--primary">
									✅
									{{ 'Accepter'|t }}
								</a>
								<a href="{{ path('client_demandes.refuse_devis', {'node': devis.id}) }}" class="button button--danger">
									❌
									{{ 'Refuser'|t }}
								</a>
							{% endif %}
						</div>

					</div>
				</li>
			{% endfor %}
		</ul>
	{% else %}
		<p>{{ 'Aucun devis reçu pour l’instant.'|t }}</p>
	{% endif %}
</div>
