{# templates/client-dashboard.html.twig #}
<div class="layout-content container">
	<h1>{{ 'Tableau de bord client'|t }}</h1>

	{# Action Region - Create Demande #}
	<div class="dashboard-action-region">
		<div class="action-hero">
			<div class="action-content">
				<h2>{{ 'Besoin d\'un service ?'|t }}</h2>
				<p>{{ 'Publiez votre demande et recevez des propositions de prestataires qualifiés.'|t }}</p>
				<a href="{{ path('client_demandes.demande_form') }}" class="btn-create-demande">
					<span class="btn-icon">➕</span>
					<span class="btn-text">{{ 'Publier une nouvelle demande'|t }}</span>
				</a>
			</div>
			<div class="action-stats">
				<div class="stat-item">
					<span class="stat-number">{{ demandes|length }}</span>
					<span class="stat-label">{{ 'Demandes publiées'|t }}</span>
				</div>
				<div class="stat-item">
					<span class="stat-number">{{ devis_proposes|length }}</span>
					<span class="stat-label">{{ 'Devis reçus'|t }}</span>
				</div>
			</div>
		</div>
	</div>

	{# Additional Actions #}
	<div class="dashboard-actions">
		{{ actions }}
	</div>

	{# Dashboard Content Grid #}
	<div class="dashboard-content-grid">

		{# Section Devis Reçus #}
		<div class="dashboard-section">
			<div class="section-header">
				<h2>{{ 'Devis reçus'|t }}</h2>
				<span class="section-count">{{ devis_proposes|length }}</span>
			</div>

			{% if devis_proposes|length > 0 %}
				<div class="devis-list">
					{% for devis in devis_proposes %}
						<div class="devis-item">
							<div class="devis-header">
								<h3>{{ devis.label }}</h3>
								{% if devis.field_status is defined %}
									{% set status = devis.field_status.value %}
									{% if status == 'accepted' %}
										<span class="badge status-accepted">{{ 'Accepté'|t }}</span>
									{% elseif status == 'refused' %}
										<span class="badge status-refused">{{ 'Refusé'|t }}</span>
									{% else %}
										<span class="badge status-pending">{{ 'En attente'|t }}</span>
									{% endif %}
								{% endif %}
							</div>

							<div class="devis-meta">
								<p><strong>{{ 'Reçu le'|t }}:</strong> {{ devis.created.value|date('d/m/Y') }}</p>
								{% if devis.field_price is defined and devis.field_price.value %}
									<p class="devis-price">
										<strong>{{ 'Prix proposé'|t }}:</strong>
										<span class="price-amount">{{ devis.field_price.value|number_format(2, ',', ' ') }} €</span>
									</p>
								{% endif %}
								{% if devis.owner %}
									<p><strong>{{ 'Prestataire'|t }}:</strong> {{ devis.owner.getDisplayName() }}</p>
								{% endif %}
								{% if devis.field_description is defined and devis.field_description.value %}
									<p><strong>{{ 'Description'|t }}:</strong> {{ devis.field_description.value|striptags|slice(0, 100) }}{% if devis.field_description.value|length > 100 %}...{% endif %}</p>
								{% endif %}
							</div>

							<div class="devis-actions">
								<a href="{{ path('entity.node.canonical', {'node': devis.id}) }}" class="button">
									👁️ {{ 'Voir le devis'|t }}
								</a>
								{% if devis.field_status.value == 'pending' %}
									<a href="{{ path('client_demandes.accept_devis', {'devis': devis.id}) }}" class="button button--success">
										✅ {{ 'Accepter'|t }}
									</a>
									<a href="{{ path('client_demandes.refuse_devis', {'node': devis.id}) }}" class="button button--danger">
										❌ {{ 'Refuser'|t }}
									</a>
								{% endif %}
							</div>
						</div>
					{% endfor %}
				</div>
			{% else %}
				<div class="empty-state">
					<div class="empty-icon">📋</div>
					<p>{{ 'Aucun devis reçu pour le moment.'|t }}</p>
					<p class="empty-subtitle">{{ 'Les prestataires vous enverront des propositions pour vos demandes.'|t }}</p>
				</div>
			{% endif %}
		</div>

		{# Section Mes Demandes Publiées #}
		<div class="dashboard-section">
			<div class="section-header">
				<h2>{{ 'Mes demandes publiées'|t }}</h2>
				<span class="section-count">{{ demandes|length }}</span>
			</div>

			{% if demandes|length > 0 %}
				<div class="demandes-list">
					{% for demande in demandes %}
						<div class="demande-item">
							<div class="demande-header">
								<h3>{{ demande.label }}</h3>
								<span class="badge status-active">{{ 'Active'|t }}</span>
							</div>

							<div class="demande-meta">
								<p><strong>{{ 'Publié le'|t }}:</strong> {{ demande.created.value|date('d/m/Y') }}</p>
								{# Budget avec champ Commerce Price #}
								{% if demande.field_price is defined and not demande.field_price.isEmpty %}
									{% set budget_value = null %}
									{% if demande.field_price.0.number %}
										{% set budget_value = demande.field_price.0.number %}
									{% elseif demande.field_price.value %}
										{% set budget_value = demande.field_price.value %}
									{% elseif demande.field_price.0.value %}
										{% set budget_value = demande.field_price.0.value %}
									{% endif %}

									{% if budget_value %}
										<p class="demande-budget">
											<strong>{{ 'Budget'|t }}:</strong>
											<span class="budget-amount">{{ budget_value|number_format(2, ',', ' ') }} €</span>
										</p>
									{% endif %}
								{% endif %}
								{% if demande.field_localisation is defined and demande.field_localisation.value %}
									<p><strong>{{ 'Localisation'|t }}:</strong> {{ demande.field_localisation.value }}</p>
								{% endif %}
								{% if demande.field_description is defined and demande.field_description.value %}
									<p><strong>{{ 'Description'|t }}:</strong> {{ demande.field_description.value|striptags|slice(0, 100) }}{% if demande.field_description.value|length > 100 %}...{% endif %}</p>
								{% endif %}
							</div>

							<div class="demande-actions">
								<a href="{{ path('entity.node.canonical', {'node': demande.id}) }}" class="button">
									👁️ {{ 'Voir la demande'|t }}
								</a>
								<a href="{{ path('entity.node.edit_form', {'node': demande.id}) }}" class="button button--secondary">
									✏️ {{ 'Modifier'|t }}
								</a>
								<a href="#" class="button button--info" onclick="alert('Fonctionnalité à venir')">
									📊 {{ 'Statistiques'|t }}
								</a>
							</div>
						</div>
					{% endfor %}
				</div>
			{% else %}
				<div class="empty-state">
					<div class="empty-icon">📝</div>
					<p>{{ 'Vous n\'avez publié aucune demande pour l\'instant.'|t }}</p>
					<p class="empty-subtitle">{{ 'Commencez par publier votre première demande ci-dessus.'|t }}</p>
				</div>
			{% endif %}
		</div>

	</div>
</div>
