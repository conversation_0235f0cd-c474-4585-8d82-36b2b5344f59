{# templates/devis-contact-info.html.twig #}
{# Template pour afficher les coordonnées après paiement #}

{% if payment_completed %}
  <div class="contact-info-section">
    <div class="contact-info-header">
      <h3>{{ 'Coordonnées de contact'|t }}</h3>
      <span class="payment-status paid">{{ 'Paiement confirmé'|t }}</span>
    </div>
    
    <div class="contact-cards">
      {% if show_prestataire_info %}
        <div class="contact-card prestataire-card">
          <div class="contact-card-header">
            <h4>{{ 'Prestataire'|t }}</h4>
            <span class="contact-role">{{ 'Votre prestataire'|t }}</span>
          </div>
          <div class="contact-details">
            <div class="contact-item">
              <span class="contact-label">{{ 'Nom'|t }}:</span>
              <span class="contact-value">{{ prestataire_name }}</span>
            </div>
            <div class="contact-item">
              <span class="contact-label">{{ 'Email'|t }}:</span>
              <span class="contact-value">
                <a href="mailto:{{ prestataire_email }}">{{ prestataire_email }}</a>
              </span>
            </div>
            {% if prestataire_phone %}
              <div class="contact-item">
                <span class="contact-label">{{ 'Téléphone'|t }}:</span>
                <span class="contact-value">
                  <a href="tel:{{ prestataire_phone }}">{{ prestataire_phone }}</a>
                </span>
              </div>
            {% endif %}
          </div>
        </div>
      {% endif %}
      
      {% if show_client_info %}
        <div class="contact-card client-card">
          <div class="contact-card-header">
            <h4>{{ 'Client'|t }}</h4>
            <span class="contact-role">{{ 'Votre client'|t }}</span>
          </div>
          <div class="contact-details">
            <div class="contact-item">
              <span class="contact-label">{{ 'Nom'|t }}:</span>
              <span class="contact-value">{{ client_name }}</span>
            </div>
            <div class="contact-item">
              <span class="contact-label">{{ 'Email'|t }}:</span>
              <span class="contact-value">
                <a href="mailto:{{ client_email }}">{{ client_email }}</a>
              </span>
            </div>
            {% if client_phone %}
              <div class="contact-item">
                <span class="contact-label">{{ 'Téléphone'|t }}:</span>
                <span class="contact-value">
                  <a href="tel:{{ client_phone }}">{{ client_phone }}</a>
                </span>
              </div>
            {% endif %}
          </div>
        </div>
      {% endif %}
    </div>
    
    <div class="contact-info-footer">
      <p class="contact-note">
        {{ 'Ces coordonnées sont disponibles suite à la confirmation du paiement.'|t }}
      </p>
    </div>
  </div>
{% else %}
  <div class="contact-info-locked">
    <div class="locked-message">
      <div class="lock-icon">🔒</div>
      <h3>{{ 'Coordonnées de contact'|t }}</h3>
      <p>{{ 'Les coordonnées de contact seront disponibles après confirmation du paiement.'|t }}</p>
      {% if payment_url %}
        <a href="{{ payment_url }}" class="payment-button">
          {{ 'Procéder au paiement'|t }}
        </a>
      {% endif %}
    </div>
  </div>
{% endif %}
