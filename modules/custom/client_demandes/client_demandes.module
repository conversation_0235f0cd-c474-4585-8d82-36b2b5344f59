<?php

/**
 * @file
 * Fonctions principales du module Client Demandes.
 */

use Drupal\Core\Routing\RouteMatchInterface;
use <PERSON><PERSON>al\node\NodeInterface;
use Dr<PERSON>al\commerce_order\Entity\Order;
use Dr<PERSON>al\node\Entity\NodeType;
use Drupal\commerce_price\Price;
use Drupal\Core\Url;
use Symfony\Component\HttpFoundation\RedirectResponse;

/**
 * Implements hook_help().
 */
function client_demandes_help($route_name, RouteMatchInterface $route_match)
{
  switch ($route_name) {
    case 'help.page.client_demandes':
      return '<h3>' . t('À propos') . '</h3><p>' . t('Module permettant aux clients de publier des demandes de service.') . '</p>';
  }
}

/**
 * Implements hook_form_alter().
 */
function client_demandes_form_alter(&$form, \Drupal\Core\Form\FormStateInterface $form_state, $form_id)
{
  // Modifier le formulaire de connexion pour ajouter une redirection personnalisée
  if ($form_id == 'user_login_form') {
    $form['#submit'][] = 'client_demandes_user_login_submit';
  }
}

/**
 * Custom submit handler for user login form.
 */
function client_demandes_user_login_submit(&$form, \Drupal\Core\Form\FormStateInterface $form_state)
{
  $account = \Drupal::currentUser();

  if ($account->isAuthenticated()) {
    $user = \Drupal\user\Entity\User::load($account->id());

    // Redirection pour les prestataires
    if ($user->hasRole('prestataire')) {
      $url = Url::fromRoute('client_demandes.prestataire_dashboard');
      $form_state->setRedirectUrl($url);
    }
    // Redirection pour les clients
    elseif ($user->hasRole('client')) {
      $url = Url::fromRoute('client_demandes.client_dashboard');
      $form_state->setRedirectUrl($url);
    }
  }
}

/**
 * Implements hook_theme().
 */
function client_demandes_theme()
{
  return [
    'client_dashboard' => [
      'variables' => ['title' => '', 'demandes' => [], 'devis_proposes' => [], 'messages' => []],
    ],
    'prestataire_dashboard' => [
      'variables' => ['demandes' => [], 'devis' => [], 'messages' => []],
    ],
    'demande_card' => [
      'variables' => ['demande' => NULL, 'can_propose_devis' => FALSE],
    ],
    'devis_card' => [
      'variables' => ['devis' => NULL, 'is_client' => FALSE],
    ],
    'paiement_options' => [
      'variables' => [
        'devis' => NULL,
        'token_complet' => NULL,
        'token_partage' => NULL,
      ],
      'template' => 'paiement-options',
    ],
    'devis_contact_info' => [
      'variables' => [
        'payment_completed' => FALSE,
        'show_prestataire_info' => FALSE,
        'show_client_info' => FALSE,
        'prestataire_name' => '',
        'prestataire_email' => '',
        'prestataire_phone' => '',
        'client_name' => '',
        'client_email' => '',
        'client_phone' => '',
        'payment_url' => '',
      ],
      'template' => 'devis-contact-info',
    ],
  ];
}

/**
 * Implements hook_preprocess_node().
 */
function client_demandes_preprocess_node(&$variables)
{
  $node = $variables['node'];
  if ($node instanceof NodeInterface) {
    $node_type = NodeType::load($node->getType());
    if (!$node_type) {
      $variables['display_submitted'] = FALSE;
    }
  }

  if ($node->getType() == 'demande') {
    $query = \Drupal::entityQuery('node')
      ->condition('type', 'devis')
      ->condition('field_demande_ref', $node->id())
      ->accessCheck(TRUE);
    $variables['devis_count'] = $query->count()->execute();

    $current_user = \Drupal::currentUser();
    $access_check = \Drupal\client_demandes\Access\DemandeAccessCheck::checkDevisAccess($current_user, $node);
    $variables['can_propose_devis'] = $access_check->isAllowed();
  }

  if ($node->getType() == 'devis') {
    if ($node->hasField('field_demande_ref') && !$node->get('field_demande_ref')->isEmpty()) {
      $demande_id = $node->get('field_demande_ref')->target_id;
      $variables['demande'] = \Drupal\node\Entity\Node::load($demande_id);
    }

    $variables['is_client'] = isset($variables['demande']) && $variables['demande']->getOwnerId() == \Drupal::currentUser()->id();
  }
}

/**
 * Implements hook_ENTITY_TYPE_insert().
 */
function client_demandes_node_insert(NodeInterface $node)
{
  if ($node->getType() == 'devis' && $node->isPublished()) {
    if ($node->hasField('field_status') && $node->get('field_status')->value == 'accepted') {
      _client_demandes_create_commerce_order($node);
    }
  }

  // Notifier les prestataires quand une nouvelle demande est publiée
  if ($node->getType() == 'demande' && $node->isPublished()) {
    _client_demandes_notify_prestataires_new_demande($node);
  }
}

/**
 * Implements hook_ENTITY_TYPE_update().
 */
function client_demandes_node_update(NodeInterface $node)
{
  if ($node->getType() == 'devis' && $node->isPublished()) {
    if (
      $node->hasField('field_status') &&
      $node->get('field_status')->value == 'accepted' &&
      $node->original->get('field_status')->value != 'accepted'
    ) {
      _client_demandes_create_commerce_order($node);

      if ($node->hasField('field_demande_ref') && !$node->get('field_demande_ref')->isEmpty()) {
        $demande_id = $node->get('field_demande_ref')->target_id;
        $demande = \Drupal\node\Entity\Node::load($demande_id);
        if ($demande && $demande->hasField('field_statut_demande')) {
          $demande->set('field_statut_demande', 'en_cours');
          $demande->save();
        }
      }
    }
  }
}

/**
 * Implements hook_commerce_order_update().
 */
function client_demandes_commerce_order_update(\Drupal\commerce_order\Entity\OrderInterface $order)
{
  // Vérifier si la commande vient d'être payée
  if (
    $order->getState()->getId() == 'completed' &&
    $order->original &&
    $order->original->getState()->getId() != 'completed'
  ) {

    _client_demandes_handle_payment_completed($order);
  }
}

/**
 * Crée une commande Commerce pour un devis accepté.
 */
function _client_demandes_create_commerce_order(NodeInterface $devis)
{
  if (!$devis->hasField('field_demande_ref') || $devis->get('field_demande_ref')->isEmpty()) {
    return;
  }

  $demande = \Drupal\node\Entity\Node::load($devis->get('field_demande_ref')->target_id);
  if (!$demande) {
    return;
  }

  // Vérification du prix
  $price_value = $devis->hasField('field_price') && !$devis->get('field_price')->isEmpty()
    ? $devis->get('field_price')->value
    : NULL;

  if ($price_value === NULL) {
    \Drupal::logger('client_demandes')->error('Le devis @id ne contient pas de prix. Création de commande interrompue.', ['@id' => $devis->id()]);
    return;
  }

  $product_variation = \Drupal\commerce_product\Entity\ProductVariation::create([
    'type' => 'default',
    'sku' => 'DEVIS-' . $devis->id(),
    'title' => 'Devis #' . $devis->id(),
    'price' => new Price($price_value, 'EUR'),
    'status' => 1,
  ]);
  $product_variation->save();

  $store = \Drupal::entityTypeManager()
    ->getStorage('commerce_store')
    ->loadDefault();

  $client = \Drupal::entityTypeManager()->getStorage('user')->load($demande->getOwnerId());

  $order = Order::create([
    'type' => 'default',
    'store_id' => $store->id(),
    'uid' => $client->id(),
    'mail' => $client->getEmail(),
    'ip_address' => \Drupal::request()->getClientIp(),
    'billing_profile' => NULL,
    'state' => 'draft',
    'placed' => \Drupal::time()->getRequestTime(),
    'completed' => NULL,
  ]);

  // Sauvegarder d'abord pour obtenir l'ID
  $order->save();

  // Mettre à jour avec des informations supplémentaires
  $order->set('order_number', 'CMD-' . str_pad($order->id(), 6, '0', STR_PAD_LEFT));
  $order->save();

  $order_item = \Drupal\commerce_order\Entity\OrderItem::create([
    'type' => 'default',
    'purchased_entity' => $product_variation->id(),
    'quantity' => 1,
    'unit_price' => $product_variation->getPrice(),
    'title' => 'Prestation : ' . $demande->getTitle(),
  ]);
  $order_item->save();

  $order->addItem($order_item);

  // Ajouter des métadonnées personnalisées
  $order->setData('devis_id', $devis->id());
  $order->setData('demande_id', $demande->id());
  $order->setData('prestataire_id', $devis->getOwnerId());
  $order->setData('client_id', $client->id());
  $order->setData('demande_title', $demande->getTitle());
  $order->setData('prestataire_name', $devis->getOwner()->getDisplayName());

  // Recalculer les totaux
  $order->recalculateTotalPrice();
  $order->save();

  // Sauvegarder l'ID de commande dans le devis
  if ($devis->hasField('field_order_id')) {
    $devis->set('field_order_id', $order->id());
    $devis->save();
  }

  // Log pour debug
  \Drupal::logger('client_demandes')->info('Commande créée: ID @order_id pour devis @devis_id', [
    '@order_id' => $order->id(),
    '@devis_id' => $devis->id(),
  ]);
}

/**
 * Implements hook_mail().
 */
function client_demandes_mail($key, &$message, $params)
{
  switch ($key) {
    case 'devis_accepted_notification':
      $message['subject'] = t('Votre devis a été accepté');
      $message['body'][] = t('Bonjour @name,', ['@name' => $params['name']]);
      $message['body'][] = t('Votre devis pour la demande "@title" a été accepté.', ['@title' => $params['demande_title']]);
      $message['body'][] = t('Voir la demande : @link', ['@link' => $params['demande_link']]);
      break;

    case 'devis_rejected_notification':
      $message['subject'] = t('Votre devis a été refusé');
      $message['body'][] = t('Bonjour @name,', ['@name' => $params['name']]);
      $message['body'][] = t('Votre devis pour la demande "@title" a été refusé.', ['@title' => $params['demande_title']]);
      $message['body'][] = t('Voir la demande : @link', ['@link' => $params['demande_link']]);
      break;

    case 'devis_accepted_confirmation':
      $message['subject'] = t('Confirmation : devis accepté');
      $message['body'][] = t('Bonjour @name,', ['@name' => $params['name']]);
      $message['body'][] = t('Vous avez accepté un devis pour votre demande "@title".', ['@title' => $params['demande_title']]);
      $message['body'][] = t('Voir le devis : @link', ['@link' => $params['devis_link']]);
      break;

    case 'devis_rejected_confirmation':
      $message['subject'] = t('Confirmation : devis refusé');
      $message['body'][] = t('Bonjour @name,', ['@name' => $params['name']]);
      $message['body'][] = t('Vous avez refusé un devis pour votre demande "@title".', ['@title' => $params['demande_title']]);
      $message['body'][] = t('Voir le devis : @link', ['@link' => $params['devis_link']]);
      break;

    case 'nouvelle_demande_prestataire':
      $message['subject'] = t('Nouvelle demande disponible : @title', ['@title' => $params['demande_title']]);
      $message['body'][] = t('Bonjour @name,', ['@name' => $params['prestataire_name']]);
      $message['body'][] = '';
      $message['body'][] = t('Une nouvelle demande correspondant à vos compétences a été publiée :');
      $message['body'][] = '';
      $message['body'][] = t('📋 Titre : @title', ['@title' => $params['demande_title']]);
      $message['body'][] = t('👤 Client : @client', ['@client' => $params['client_name']]);

      if (!empty($params['demande_budget'])) {
        $message['body'][] = t('💰 Budget : @budget €', ['@budget' => number_format($params['demande_budget'], 2, ',', ' ')]);
      }

      if (!empty($params['demande_localisation'])) {
        $message['body'][] = t('📍 Localisation : @location', ['@location' => $params['demande_localisation']]);
      }

      if (!empty($params['demande_description'])) {
        $description = strip_tags($params['demande_description']);
        $description = strlen($description) > 200 ? substr($description, 0, 200) . '...' : $description;
        $message['body'][] = '';
        $message['body'][] = t('📝 Description :');
        $message['body'][] = $description;
      }

      $message['body'][] = '';
      $message['body'][] = t('👀 Voir la demande complète et proposer un devis :');
      $message['body'][] = $params['demande_url'];
      $message['body'][] = '';
      $message['body'][] = t('Bonne journée !');
      break;

    case 'paiement_complete_client':
      $message['subject'] = t('Paiement confirmé - Coordonnées du prestataire');
      $message['body'][] = t('Bonjour @name,', ['@name' => $params['client_name']]);
      $message['body'][] = '';
      $message['body'][] = t('Votre paiement pour le devis "@devis_title" a été confirmé.', ['@devis_title' => $params['devis_title']]);
      $message['body'][] = '';
      $message['body'][] = t('📞 Coordonnées du prestataire :');
      $message['body'][] = t('Nom : @prestataire_name', ['@prestataire_name' => $params['prestataire_name']]);
      $message['body'][] = t('Email : @prestataire_email', ['@prestataire_email' => $params['prestataire_email']]);
      if (!empty($params['prestataire_phone'])) {
        $message['body'][] = t('Téléphone : @phone', ['@phone' => $params['prestataire_phone']]);
      }
      $message['body'][] = '';
      $message['body'][] = t('Vous pouvez maintenant contacter directement le prestataire pour organiser la prestation.');
      $message['body'][] = '';
      $message['body'][] = t('Merci pour votre confiance !');
      break;

    case 'paiement_complete_prestataire':
      $message['subject'] = t('Paiement reçu - Mission confirmée');
      $message['body'][] = t('Bonjour @name,', ['@name' => $params['prestataire_name']]);
      $message['body'][] = '';
      $message['body'][] = t('Excellente nouvelle ! Le client a confirmé le paiement pour votre devis "@devis_title".', ['@devis_title' => $params['devis_title']]);
      $message['body'][] = '';
      $message['body'][] = t('📞 Coordonnées du client :');
      $message['body'][] = t('Nom : @client_name', ['@client_name' => $params['client_name']]);
      $message['body'][] = t('Email : @client_email', ['@client_email' => $params['client_email']]);
      if (!empty($params['client_phone'])) {
        $message['body'][] = t('Téléphone : @phone', ['@phone' => $params['client_phone']]);
      }
      $message['body'][] = '';
      $message['body'][] = t('Vous pouvez maintenant contacter le client pour organiser la prestation.');
      $message['body'][] = t('Montant : @amount €', ['@amount' => $params['amount']]);
      $message['body'][] = '';
      $message['body'][] = t('Bonne mission !');
      break;
  }
}

/**
 * Gère le paiement complété d'une commande.
 */
function _client_demandes_handle_payment_completed(\Drupal\commerce_order\Entity\OrderInterface $order)
{
  // Récupérer le devis associé à cette commande
  $devis = _client_demandes_get_devis_from_order($order);
  if (!$devis) {
    return;
  }

  // Récupérer la demande associée
  $demande = null;
  if ($devis->hasField('field_demande_ref') && !$devis->get('field_demande_ref')->isEmpty()) {
    $demande_id = $devis->get('field_demande_ref')->target_id;
    $demande = \Drupal\node\Entity\Node::load($demande_id);
  }

  if (!$demande) {
    return;
  }

  // Récupérer les utilisateurs
  $client = $order->getCustomer();
  $prestataire = $devis->getOwner();

  // Créer les messages internes
  _client_demandes_create_payment_message($client, $prestataire, $devis, $demande, $order);

  // Envoyer les emails avec les coordonnées
  _client_demandes_send_payment_emails($client, $prestataire, $devis, $demande, $order);
}

/**
 * Récupère le devis associé à une commande.
 */
function _client_demandes_get_devis_from_order(\Drupal\commerce_order\Entity\OrderInterface $order)
{
  // Chercher un devis avec l'ID de commande
  $query = \Drupal::entityQuery('node')
    ->condition('type', 'devis')
    ->condition('field_order_id', $order->id())
    ->accessCheck(TRUE);

  $nids = $query->execute();
  if (!empty($nids)) {
    return \Drupal\node\Entity\Node::load(reset($nids));
  }

  return null;
}

/**
 * Notifie les prestataires compatibles d'une nouvelle demande.
 */
function _client_demandes_notify_prestataires_new_demande(NodeInterface $demande)
{
  // Récupérer la catégorie de la demande
  $demande_categories = [];
  if ($demande->hasField('field_categorie') && !$demande->get('field_categorie')->isEmpty()) {
    foreach ($demande->get('field_categorie') as $term_ref) {
      $demande_categories[] = $term_ref->target_id;
    }
  }

  if (empty($demande_categories)) {
    // Si pas de catégorie spécifique, notifier tous les prestataires
    $demande_categories = NULL;
  }

  // Trouver les prestataires avec des catégories compatibles
  $prestataires = _client_demandes_get_compatible_prestataires($demande_categories);

  foreach ($prestataires as $prestataire) {
    // Créer un message avec le module Message
    _client_demandes_create_message_new_demande($prestataire, $demande);

    // Envoyer un email
    _client_demandes_send_email_new_demande($prestataire, $demande);
  }
}

/**
 * Récupère les prestataires compatibles avec les catégories données.
 */
function _client_demandes_get_compatible_prestataires($categories_ids)
{
  $query = \Drupal::entityQuery('user')
    ->condition('status', 1)
    ->condition('roles', 'prestataire')
    ->accessCheck(TRUE);

  // Si le champ catégorie existe et qu'on a des catégories
  if (!empty($categories_ids)) {
    $query->condition('field_categorie', $categories_ids, 'IN');
  }

  $uids = $query->execute();
  return \Drupal::entityTypeManager()->getStorage('user')->loadMultiple($uids);
}

/**
 * Crée un message interne pour notifier un prestataire.
 */
function _client_demandes_create_message_new_demande($prestataire, $demande)
{
  // Vérifier si le module Message est disponible
  if (!\Drupal::moduleHandler()->moduleExists('message')) {
    return;
  }

  try {
    $message = \Drupal\message\Entity\Message::create([
      'template' => 'nouvelle_demande_prestataire',
      'uid' => $prestataire->id(),
    ]);

    // Ajouter les arguments du message
    $message->setArguments([
      '@demande_title' => $demande->getTitle(),
      '@demande_url' => $demande->toUrl('canonical', ['absolute' => TRUE])->toString(),
      '@client_name' => $demande->getOwner()->getDisplayName(),
    ]);

    $message->save();
  } catch (\Exception $e) {
    \Drupal::logger('client_demandes')->error('Erreur lors de la création du message: @error', ['@error' => $e->getMessage()]);
  }
}

/**
 * Envoie un email à un prestataire pour une nouvelle demande.
 */
function _client_demandes_send_email_new_demande($prestataire, $demande)
{
  $mail_manager = \Drupal::service('plugin.manager.mail');
  $module = 'client_demandes';
  $key = 'nouvelle_demande_prestataire';
  $to = $prestataire->getEmail();
  $langcode = $prestataire->getPreferredLangcode();

  $params = [
    'prestataire_name' => $prestataire->getDisplayName(),
    'demande_title' => $demande->getTitle(),
    'demande_url' => $demande->toUrl('canonical', ['absolute' => TRUE])->toString(),
    'client_name' => $demande->getOwner()->getDisplayName(),
    'demande_description' => $demande->hasField('field_description') ? $demande->get('field_description')->value : '',
    'demande_budget' => $demande->hasField('field_price') ? _client_demandes_get_price_value($demande->get('field_price')) : '',
    'demande_localisation' => $demande->hasField('field_localisation') ? $demande->get('field_localisation')->value : '',
  ];

  $result = $mail_manager->mail($module, $key, $to, $langcode, $params, NULL, TRUE);

  if ($result['result'] !== TRUE) {
    \Drupal::logger('client_demandes')->error('Erreur lors de l\'envoi de l\'email à @email', ['@email' => $to]);
  }
}

/**
 * Extrait la valeur numérique d'un champ Commerce Price.
 */
function _client_demandes_get_price_value($price_field)
{
  if ($price_field->isEmpty()) {
    return '';
  }

  // Essayer différentes façons d'accéder à la valeur
  if (isset($price_field[0]) && isset($price_field[0]->number)) {
    return $price_field[0]->number;
  }

  if (isset($price_field->value)) {
    return $price_field->value;
  }

  if (isset($price_field[0]) && isset($price_field[0]->value)) {
    return $price_field[0]->value;
  }

  return '';
}

/**
 * Crée les messages internes après paiement.
 */
function _client_demandes_create_payment_message($client, $prestataire, $devis, $demande, $order)
{
  if (!\Drupal::moduleHandler()->moduleExists('message')) {
    return;
  }

  try {
    // Message pour le client avec coordonnées du prestataire
    $client_message = \Drupal\message\Entity\Message::create([
      'template' => 'paiement_complete_client',
      'uid' => $client->id(),
    ]);

    $client_message->setArguments([
      '@devis_title' => $devis->getTitle(),
      '@prestataire_name' => $prestataire->getDisplayName(),
      '@prestataire_email' => $prestataire->getEmail(),
      '@prestataire_phone' => _client_demandes_get_user_phone($prestataire),
    ]);

    $client_message->save();

    // Message pour le prestataire avec coordonnées du client
    $prestataire_message = \Drupal\message\Entity\Message::create([
      'template' => 'paiement_complete_prestataire',
      'uid' => $prestataire->id(),
    ]);

    $prestataire_message->setArguments([
      '@devis_title' => $devis->getTitle(),
      '@client_name' => $client->getDisplayName(),
      '@client_email' => $client->getEmail(),
      '@client_phone' => _client_demandes_get_user_phone($client),
      '@amount' => $order->getTotalPrice()->getNumber(),
    ]);

    $prestataire_message->save();
  } catch (\Exception $e) {
    \Drupal::logger('client_demandes')->error('Erreur lors de la création des messages de paiement: @error', ['@error' => $e->getMessage()]);
  }
}

/**
 * Envoie les emails après paiement avec coordonnées.
 */
function _client_demandes_send_payment_emails($client, $prestataire, $devis, $demande, $order)
{
  $mail_manager = \Drupal::service('plugin.manager.mail');
  $module = 'client_demandes';

  // Email au client avec coordonnées du prestataire
  $client_params = [
    'client_name' => $client->getDisplayName(),
    'devis_title' => $devis->getTitle(),
    'prestataire_name' => $prestataire->getDisplayName(),
    'prestataire_email' => $prestataire->getEmail(),
    'prestataire_phone' => _client_demandes_get_user_phone($prestataire),
    'amount' => $order->getTotalPrice()->getNumber(),
  ];

  $mail_manager->mail(
    $module,
    'paiement_complete_client',
    $client->getEmail(),
    $client->getPreferredLangcode(),
    $client_params,
    NULL,
    TRUE
  );

  // Email au prestataire avec coordonnées du client
  $prestataire_params = [
    'prestataire_name' => $prestataire->getDisplayName(),
    'devis_title' => $devis->getTitle(),
    'client_name' => $client->getDisplayName(),
    'client_email' => $client->getEmail(),
    'client_phone' => _client_demandes_get_user_phone($client),
    'amount' => $order->getTotalPrice()->getNumber(),
  ];

  $mail_manager->mail(
    $module,
    'paiement_complete_prestataire',
    $prestataire->getEmail(),
    $prestataire->getPreferredLangcode(),
    $prestataire_params,
    NULL,
    TRUE
  );
}

/**
 * Récupère le numéro de téléphone d'un utilisateur.
 */
function _client_demandes_get_user_phone($user)
{
  // Essayer différents champs de téléphone possibles
  $phone_fields = ['field_telephone', 'field_phone', 'field_tel'];

  foreach ($phone_fields as $field_name) {
    if ($user->hasField($field_name) && !$user->get($field_name)->isEmpty()) {
      return $user->get($field_name)->value;
    }
  }

  return '';
}

/**
 * Vérifie si le paiement a été effectué pour un devis.
 */
function _client_demandes_is_payment_completed($devis)
{
  if (!$devis->hasField('field_order_id') || $devis->get('field_order_id')->isEmpty()) {
    return false;
  }

  $order_id = $devis->get('field_order_id')->value;
  $order = \Drupal\commerce_order\Entity\Order::load($order_id);

  if (!$order) {
    return false;
  }

  return $order->getState()->getId() == 'completed';
}



/**
 * Implements hook_node_view().
 */
function client_demandes_node_view(array &$build, \Drupal\Core\Entity\EntityInterface $entity, \Drupal\Core\Entity\Display\EntityViewDisplayInterface $display, $view_mode)
{
  if ($entity->getType() == 'devis' && $view_mode == 'full') {
    $current_user = \Drupal::currentUser();
    $payment_completed = _client_demandes_is_payment_completed($entity);

    // Déterminer qui peut voir quelles informations
    $show_prestataire_info = FALSE;
    $show_client_info = FALSE;

    if ($payment_completed) {
      // Si l'utilisateur actuel est le client, montrer les infos du prestataire
      if ($current_user->id() == $entity->get('field_demande_ref')->entity->getOwnerId()) {
        $show_prestataire_info = TRUE;
      }
      // Si l'utilisateur actuel est le prestataire, montrer les infos du client
      elseif ($current_user->id() == $entity->getOwnerId()) {
        $show_client_info = TRUE;
      }
    }

    $prestataire = $entity->getOwner();
    $client = $entity->get('field_demande_ref')->entity->getOwner();

    $build['contact_info'] = [
      '#theme' => 'devis_contact_info',
      '#payment_completed' => $payment_completed,
      '#show_prestataire_info' => $show_prestataire_info,
      '#show_client_info' => $show_client_info,
      '#prestataire_name' => $prestataire->getDisplayName(),
      '#prestataire_email' => $prestataire->getEmail(),
      '#prestataire_phone' => _client_demandes_get_user_phone($prestataire),
      '#client_name' => $client->getDisplayName(),
      '#client_email' => $client->getEmail(),
      '#client_phone' => _client_demandes_get_user_phone($client),
      '#payment_url' => $payment_completed ? '' : '/checkout/' . $entity->get('field_order_id')->value,
      '#attached' => [
        'library' => [
          'client_demandes/contact_info',
        ],
      ],
      '#weight' => 10,
    ];
  }
}

/**
 * Implements hook_entity_view().
 */
function client_demandes_entity_view(array &$build, \Drupal\Core\Entity\EntityInterface $entity, \Drupal\Core\Entity\Display\EntityViewDisplayInterface $display, $view_mode)
{
  if ($entity->getEntityTypeId() == 'commerce_order' && $view_mode == 'default') {
    // Améliorer l'affichage des commandes liées aux devis
    /** @var \Drupal\commerce_order\Entity\OrderInterface $order */
    $order = $entity;

    // Ajouter des informations sur le devis et la demande
    $order_data = $order->getData();
    if (!empty($order_data['devis_id'])) {
      $devis_id = $order_data['devis_id'];
      $demande_id = $order_data['demande_id'] ?? null;
      $demande_title = $order_data['demande_title'] ?? 'Demande inconnue';
      $prestataire_name = $order_data['prestataire_name'] ?? 'Prestataire inconnu';

      $build['client_demandes_info'] = [
        '#type' => 'container',
        '#attributes' => ['class' => ['order-demande-info']],
        '#weight' => -10,
      ];

      $build['client_demandes_info']['title'] = [
        '#markup' => '<h3>Informations sur la prestation</h3>',
      ];

      $placed_time = $order->getPlacedTime() ? date('d/m/Y H:i', $order->getPlacedTime()) : 'Non définie';
      $order_number = $order->getOrderNumber() ?: 'CMD-' . $order->id();

      $build['client_demandes_info']['details'] = [
        '#markup' => '
          <div class="order-details">
            <p><strong>Demande :</strong> ' . $demande_title . '</p>
            <p><strong>Prestataire :</strong> ' . $prestataire_name . '</p>
            <p><strong>Date de commande :</strong> ' . $placed_time . '</p>
            <p><strong>Numéro de commande :</strong> ' . $order_number . '</p>
            <p><strong>ID de commande :</strong> ' . $order->id() . '</p>
          </div>
        ',
      ];

      if ($devis_id) {
        $build['client_demandes_info']['links'] = [
          '#markup' => '
            <div class="order-links">
              <a href="/node/' . $devis_id . '" class="button">Voir le devis</a>
              <a href="/node/' . $demande_id . '" class="button">Voir la demande</a>
            </div>
          ',
        ];
      }

      $build['client_demandes_info']['#attached']['library'][] = 'client_demandes/contact_info';
    }
  }
}

/**
 * Implements hook_commerce_order_view().
 */
function client_demandes_commerce_order_view(array &$build, \Drupal\commerce_order\Entity\OrderInterface $order, \Drupal\Core\Entity\Display\EntityViewDisplayInterface $display, $view_mode)
{
  // Assurer que la date et l'ID sont bien affichés
  if (!isset($build['order_id'])) {
    $build['order_id'] = [
      '#type' => 'item',
      '#title' => t('Order ID'),
      '#markup' => $order->id(),
      '#weight' => -20,
    ];
  }

  if (!isset($build['placed']) && $order->getPlacedTime()) {
    $build['placed'] = [
      '#type' => 'item',
      '#title' => t('Order Date'),
      '#markup' => \Drupal::service('date.formatter')->format($order->getPlacedTime(), 'medium'),
      '#weight' => -19,
    ];
  }

  if (!isset($build['order_number']) && $order->getOrderNumber()) {
    $build['order_number'] = [
      '#type' => 'item',
      '#title' => t('Order Number'),
      '#markup' => $order->getOrderNumber(),
      '#weight' => -18,
    ];
  }
}
