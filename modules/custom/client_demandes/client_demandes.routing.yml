# Tableau de bord client
client_demandes.client_dashboard:
  path: '/client/dashboard'
  defaults:
    _controller: '\Drupal\client_demandes\Controller\ClientController::dashboard'
    _title: 'Tableau de bord client'
  requirements:
    _role: 'client'
  options:
    _admin_route: FALSE

# Formulaire de demande client
client_demandes.demande_form:
  path: '/client/demande/add'
  defaults:
    _form: '\Drupal\client_demandes\Form\DemandeForm'
    _title: 'Publier une demande'
  requirements:
    _role: 'client'
  options:
    _admin_route: FALSE

# Tableau de bord prestataire
client_demandes.prestataire_dashboard:
  path: '/prestataire/dashboard'
  defaults:
    _controller: '\Drupal\client_demandes\Controller\PrestataireController::dashboard'
    _title: 'Tableau de bord prestataire'
  requirements:
    _role: 'prestataire'
  options:
    _admin_route: FALSE

# Devis – Envoi (prestataire)
client_demandes.send_quote_form:
  path: '/prestataire/devis/add/{node}'
  defaults:
    _form: '\Drupal\client_demandes\Form\SendQuoteForm'
    _title: 'Proposer un devis'
  requirements:
    _role: 'prestataire'
    _entity_access: 'node.view'
  options:
    _admin_route: FALSE
    parameters:
      node:
        type: entity:node

# Devis – Acceptation / Refus (client)
client_demandes.accept_devis:
  path: '/client/demande/{devis}/accepter'
  defaults:
    _controller: '\Drupal\client_demandes\Controller\ClientDevisController::acceptDevis'
    _title: 'Accepter le devis'
  requirements:
    _permission: 'access content'
    devis: \d+
  options:
    parameters:
      devis:
        type: entity:node



client_demandes.refuse_devis:
  path: '/client/devis/{node}/refuse'
  defaults:
    _controller: '\Drupal\client_demandes\Controller\DevisActionController::refuse'
    _title: 'Refuser le devis'
  requirements:
    _permission: 'access content'
    node: \d+

# Propositions – Vue, Acceptation, Refus
client_demandes.proposal_view:
  path: '/proposition/{devis_nid}/choix'
  defaults:
    _controller: '\Drupal\client_demandes\Controller\ProposalActionController::viewProposal'
    _title: 'Choisir une action'
  requirements:
    _permission: 'access content'
    devis_nid: \d+

client_demandes.proposal_accept:
  path: '/proposition/{devis_nid}/accepter'
  defaults:
    _controller: '\Drupal\client_demandes\Controller\ProposalActionController::accept'
    _title: 'Choisir mode de paiement'
  requirements:
    _permission: 'access content'
    devis_nid: \d+

client_demandes.proposal_refuse:
  path: '/proposition/{devis_nid}/refuser'
  defaults:
    _controller: '\Drupal\client_demandes\Controller\ProposalActionController::refuse'
    _title: 'Refus du devis'
  requirements:
    _permission: 'access content'
    devis_nid: \d+

# Paiement – Choix et exécution
client_demandes.paiement_options:
  path: '/paiement/devis/{devis}'
  defaults:
    _controller: '\Drupal\client_demandes\Controller\PaiementController::optionsPaiement'
    _title: 'Choisissez votre mode de paiement'
  requirements:
    _permission: 'access content'
    devis: \d+

client_demandes.paiement_complet:
  path: '/checkout/paiement-complet'
  defaults:
    _controller: '\Drupal\client_demandes\Controller\PaiementController::checkoutComplet'
    _title: 'Paiement complet'
  requirements:
    _permission: 'access content'

client_demandes.paiement_partiel:
  path: '/checkout/paiement-partiel'
  defaults:
    _controller: '\Drupal\client_demandes\Controller\PaiementController::checkoutPartiel'
    _title: 'Paiement partiel'
  requirements:
    _permission: 'access content'

# Configuration Stripe (admin)
client_demandes.stripe_config:
  path: '/admin/config/client-demandes/stripe'
  defaults:
    _form: '\Drupal\client_demandes\Form\StripeSettingsForm'
    _title: 'Configuration Stripe'
  requirements:
    _permission: 'administer site configuration'

# Paiement Stripe direct
client_demandes.stripe_checkout:
  path: '/stripe/checkout/{commerce_order}/{mode}'
  defaults:
    _controller: '\Drupal\client_demandes\Controller\StripeController::checkout'
    _title: 'Paiement Stripe'
  requirements:
    _role: 'authenticated'
  options:
    parameters:
      commerce_order:
        type: entity:commerce_order

# Offres envoyées (prestataire)
client_demandes.my_sent_offers:
  path: '/mon-espace/mes-offres-envoyees'
  defaults:
    _controller: '\Drupal\client_demandes\Controller\PrestataireController::mySentOffers'
    _title: 'Mes offres envoyées'
  requirements:
    _role: 'prestataire'
  options:
    _admin_route: FALSE

# Demo prestataire dashboard
client_demandes.prestataire_dashboard_demo:
  path: '/prestataire/dashboard/demo'
  defaults:
    _controller: '\Drupal\client_demandes\Controller\PrestataireController::dashboardDemo'
    _title: 'Demo Tableau de bord prestataire'
  requirements:
    _permission: 'access content'
  options:
    _admin_route: FALSE

# Stripe Connect setup
client_demandes.stripe_connect_setup:
  path: '/prestataire/stripe/connect'
  defaults:
    _controller: '\Drupal\client_demandes\Controller\PrestataireController::stripeConnectSetup'
    _title: 'Connecter Stripe'
  requirements:
    _role: 'prestataire'
  options:
    _admin_route: FALSE

# Stripe Connect form
client_demandes.stripe_connect_form:
  path: '/prestataire/stripe/configure'
  defaults:
    _form: '\Drupal\client_demandes\Form\StripeConnectForm'
    _title: 'Configurer Stripe Connect'
  requirements:
    _role: 'prestataire'
  options:
    _admin_route: FALSE

# Callback Stripe Connect OAuth
client_demandes.stripe_connect_callback:
  path: '/prestataire/stripe/callback'
  defaults:
    _controller: '\Drupal\client_demandes\Controller\PrestataireController::stripeConnectCallback'
    _title: 'Callback Stripe Connect'
  requirements:
    _role: 'prestataire'
  options:
    _admin_route: FALSE

# Déconnexion Stripe Connect
client_demandes.stripe_disconnect:
  path: '/prestataire/stripe/disconnect'
  defaults:
    _controller: '\Drupal\client_demandes\Controller\PrestataireController::stripeDisconnect'
    _title: 'Déconnecter Stripe'
  requirements:
    _role: 'prestataire'
  options:
    _admin_route: FALSE

# Test Stripe Connect form
client_demandes.stripe_connect_test:
  path: '/test/stripe/configure'
  defaults:
    _controller: '\Drupal\client_demandes\Controller\PrestataireController::stripeConnectForm'
    _title: 'Test - Configurer Stripe Connect'
  requirements:
    _permission: 'access content'
  options:
    _admin_route: FALSE

# Demo client dashboard
client_demandes.client_dashboard_demo:
  path: '/client/dashboard/demo'
  defaults:
    _controller: '\Drupal\client_demandes\Controller\ClientController::dashboardDemo'
    _title: 'Demo - Tableau de bord client'
  requirements:
    _permission: 'access content'
  options:
    _admin_route: FALSE

# Test des notifications
client_demandes.notification_test:
  path: '/admin/client-demandes/test-notifications'
  defaults:
    _controller: '\Drupal\client_demandes\Controller\NotificationTestController::testPage'
    _title: 'Test des Notifications'
  requirements:
    _permission: 'administer site configuration'
  options:
    _admin_route: TRUE

# Créer une demande de test
client_demandes.create_test_demande:
  path: '/admin/client-demandes/create-test-demande'
  defaults:
    _controller: '\Drupal\client_demandes\Controller\NotificationTestController::createTestDemande'
    _title: 'Créer une demande de test'
  requirements:
    _permission: 'administer site configuration'
  options:
    _admin_route: TRUE

# Test des redirections de connexion
client_demandes.login_redirect_test:
  path: '/admin/client-demandes/test-login-redirect'
  defaults:
    _controller: '\Drupal\client_demandes\Controller\NotificationTestController::loginRedirectTest'
    _title: 'Test des Redirections de Connexion'
  requirements:
    _permission: 'administer site configuration'
  options:
    _admin_route: TRUE

# Test des commandes Commerce
client_demandes.commerce_order_test:
  path: '/admin/client-demandes/test-commerce-orders'
  defaults:
    _controller: '\Drupal\client_demandes\Controller\NotificationTestController::commerceOrderTest'
    _title: 'Test des Commandes Commerce'
  requirements:
    _permission: 'administer site configuration'
  options:
    _admin_route: TRUE

# Diagnostic des champs
client_demandes.field_diagnostic:
  path: '/admin/client-demandes/field-diagnostic'
  defaults:
    _controller: '\Drupal\client_demandes\Controller\NotificationTestController::fieldDiagnostic'
    _title: 'Diagnostic des Champs'
  requirements:
    _permission: 'administer site configuration'
  options:
    _admin_route: TRUE

# Créer le champ field_categorie automatiquement
client_demandes.create_user_categorie_field:
  path: '/admin/client-demandes/create-user-categorie-field'
  defaults:
    _controller: '\Drupal\client_demandes\Controller\NotificationTestController::createUserCategorieField'
    _title: 'Créer le champ Catégorie'
  requirements:
    _permission: 'administer site configuration'
  options:
    _admin_route: TRUE

# Créer les champs Stripe automatiquement
client_demandes.create_stripe_fields:
  path: '/admin/client-demandes/create-stripe-fields'
  defaults:
    _controller: '\Drupal\client_demandes\Controller\NotificationTestController::createStripeFields'
    _title: 'Créer les champs Stripe'
  requirements:
    _permission: 'administer site configuration'
  options:
    _admin_route: TRUE

# Test du filtrage par catégorie
client_demandes.category_filter_test:
  path: '/admin/client-demandes/test-category-filter'
  defaults:
    _controller: '\Drupal\client_demandes\Controller\NotificationTestController::categoryFilterTest'
    _title: 'Test du Filtrage par Catégorie'
  requirements:
    _permission: 'administer site configuration'
  options:
    _admin_route: TRUE

# Configuration Stripe Connect (nouvelle version)
client_demandes.stripe_connect_config:
  path: '/admin/config/services/client-demandes/stripe'
  defaults:
    _form: '\Drupal\client_demandes\Form\StripeConfigForm'
    _title: 'Configuration Stripe Connect'
  requirements:
    _permission: 'administer site configuration'
  options:
    _admin_route: TRUE

# Test de l'intégration Stripe Connect
client_demandes.stripe_integration_test:
  path: '/admin/client-demandes/test-stripe-connect'
  defaults:
    _controller: '\Drupal\client_demandes\Controller\NotificationTestController::stripeConnectTest'
    _title: 'Test Stripe Connect'
  requirements:
    _permission: 'administer site configuration'
  options:
    _admin_route: TRUE

# Test du checkout Commerce
client_demandes.checkout_test:
  path: '/admin/client-demandes/test-checkout'
  defaults:
    _controller: '\Drupal\client_demandes\Controller\NotificationTestController::checkoutTest'
    _title: 'Test du Checkout'
  requirements:
    _permission: 'administer site configuration'
  options:
    _admin_route: TRUE

# Créer une commande de test
client_demandes.create_test_order:
  path: '/admin/client-demandes/create-test-order'
  defaults:
    _controller: '\Drupal\client_demandes\Controller\NotificationTestController::createTestOrder'
    _title: 'Créer une commande de test'
  requirements:
    _permission: 'administer site configuration'
  options:
    _admin_route: TRUE

# Page pour accepter des devis réels
client_demandes.add_to_cart_test:
  path: '/admin/client-demandes/accept-devis'
  defaults:
    _controller: '\Drupal\client_demandes\Controller\NotificationTestController::addToCartTest'
    _title: 'Accepter des Devis'
  requirements:
    _permission: 'access content'
  options:
    _admin_route: FALSE

# Accepter un devis spécifique
client_demandes.accept_devis_action:
  path: '/admin/client-demandes/accept-devis/{devis_id}'
  defaults:
    _controller: '\Drupal\client_demandes\Controller\NotificationTestController::acceptDevis'
    _title: 'Accepter un Devis'
  requirements:
    _permission: 'access content'
    devis_id: \d+
  options:
    _admin_route: FALSE

# Créer des produits de test
client_demandes.create_test_products:
  path: '/admin/client-demandes/create-test-products'
  defaults:
    _controller: '\Drupal\client_demandes\Controller\NotificationTestController::createTestProducts'
    _title: 'Créer des produits de test'
  requirements:
    _permission: 'administer site configuration'
  options:
    _admin_route: TRUE

# Demo mes offres envoyées
client_demandes.my_sent_offers_demo:
  path: '/mon-espace/mes-offres-envoyees/demo'
  defaults:
    _controller: '\Drupal\client_demandes\Controller\PrestataireController::mySentOffersDemo'
    _title: 'Demo - Mes offres envoyées'
  requirements:
    _permission: 'access content'
  options:
    _admin_route: FALSE


