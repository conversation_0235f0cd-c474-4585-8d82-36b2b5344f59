/* Contact Info Styles */

.contact-info-section {
  background: #ffffff;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 25px;
  margin: 20px 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.contact-info-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 2px solid #f8f9fa;
}

.contact-info-header h3 {
  color: #2c3e50;
  margin: 0;
  font-size: 1.4em;
  font-weight: 600;
}

.payment-status {
  padding: 8px 15px;
  border-radius: 20px;
  font-size: 0.9em;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.payment-status.paid {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

/* Contact Cards */
.contact-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.contact-card {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
  transition: all 0.2s ease;
}

.contact-card:hover {
  background: #ffffff;
  border-color: #007bff;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 123, 255, 0.2);
}

.prestataire-card {
  border-left: 4px solid #28a745;
}

.client-card {
  border-left: 4px solid #007bff;
}

.contact-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.contact-card-header h4 {
  color: #2c3e50;
  margin: 0;
  font-size: 1.2em;
  font-weight: 600;
}

.contact-role {
  background: #e9ecef;
  color: #6c757d;
  padding: 4px 10px;
  border-radius: 12px;
  font-size: 0.8em;
  font-weight: 500;
}

/* Contact Details */
.contact-details {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.contact-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #e9ecef;
}

.contact-item:last-child {
  border-bottom: none;
}

.contact-label {
  font-weight: 600;
  color: #495057;
  min-width: 80px;
}

.contact-value {
  color: #2c3e50;
  font-weight: 500;
}

.contact-value a {
  color: #007bff;
  text-decoration: none;
  transition: color 0.2s ease;
}

.contact-value a:hover {
  color: #0056b3;
  text-decoration: underline;
}

/* Footer */
.contact-info-footer {
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px solid #e9ecef;
}

.contact-note {
  color: #6c757d;
  font-size: 0.9em;
  font-style: italic;
  margin: 0;
  text-align: center;
}

/* Locked State */
.contact-info-locked {
  background: #f8f9fa;
  border: 2px dashed #dee2e6;
  border-radius: 12px;
  padding: 40px 25px;
  margin: 20px 0;
  text-align: center;
}

.locked-message {
  max-width: 400px;
  margin: 0 auto;
}

.lock-icon {
  font-size: 3em;
  margin-bottom: 15px;
  opacity: 0.6;
}

.locked-message h3 {
  color: #6c757d;
  margin-bottom: 15px;
  font-size: 1.3em;
}

.locked-message p {
  color: #6c757d;
  margin-bottom: 20px;
  line-height: 1.5;
}

.payment-button {
  display: inline-block;
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  color: white;
  padding: 12px 25px;
  border-radius: 25px;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
}

.payment-button:hover {
  background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 123, 255, 0.4);
  text-decoration: none;
  color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
  .contact-cards {
    grid-template-columns: 1fr;
  }
  
  .contact-info-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .contact-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }
  
  .contact-label {
    min-width: auto;
  }
  
  .contact-info-section {
    padding: 20px;
  }
  
  .contact-info-locked {
    padding: 30px 20px;
  }
}

@media (max-width: 480px) {
  .contact-card {
    padding: 15px;
  }
  
  .contact-info-section {
    padding: 15px;
  }
  
  .contact-info-header h3 {
    font-size: 1.2em;
  }
  
  .payment-button {
    padding: 10px 20px;
    font-size: 0.9em;
  }
}
