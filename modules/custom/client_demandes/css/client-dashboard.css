/* Client Dashboard Styles */

.client-dashboard {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

/* Dashboard Action Region */
.dashboard-action-region {
  margin-bottom: 40px;
}

.action-hero {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 15px;
  padding: 30px;
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
  position: relative;
  overflow: hidden;
}

.action-hero::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
  pointer-events: none;
}

.action-hero::after {
  content: '💼';
  position: absolute;
  top: 20px;
  right: 30px;
  font-size: 3em;
  opacity: 0.2;
  z-index: 1;
}

.action-content {
  flex: 1;
  z-index: 2;
}

.action-content h2 {
  font-size: 2em;
  margin-bottom: 10px;
  font-weight: 600;
}

.action-content p {
  font-size: 1.1em;
  margin-bottom: 25px;
  opacity: 0.9;
  line-height: 1.5;
}

/* Create Demande Button */
.btn-create-demande {
  display: inline-flex;
  align-items: center;
  gap: 12px;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  padding: 15px 30px;
  border-radius: 50px;
  text-decoration: none;
  font-weight: 600;
  font-size: 1.1em;
  transition: all 0.3s ease;
  border: 2px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
}

.btn-create-demande:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  text-decoration: none;
  color: white;
}

.btn-icon {
  font-size: 1.3em;
  display: flex;
  align-items: center;
}

.btn-text {
  font-weight: 600;
}

/* Action Stats */
.action-stats {
  display: flex;
  gap: 30px;
  z-index: 2;
}

.action-stats .stat-item {
  text-align: center;
  background: rgba(255, 255, 255, 0.15);
  padding: 20px;
  border-radius: 10px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.action-stats .stat-number {
  display: block;
  font-size: 2.5em;
  font-weight: bold;
  margin-bottom: 5px;
}

.action-stats .stat-label {
  display: block;
  font-size: 0.9em;
  opacity: 0.8;
}

/* Dashboard Actions */
.dashboard-actions {
  margin-bottom: 30px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

/* Demandes List */
.client-demandes-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.client-demandes-list li {
  margin-bottom: 20px;
}

.demande-item {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.2s ease;
}

.demande-item:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.demande-item h3 {
  color: #2c3e50;
  margin-bottom: 15px;
  font-size: 1.3em;
}

.demande-item p {
  margin-bottom: 10px;
  color: #6c757d;
}

.demande-item strong {
  color: #495057;
}

/* Demande Actions */
.demande-actions {
  display: flex;
  gap: 10px;
  margin-top: 15px;
  flex-wrap: wrap;
}

.demande-actions .button {
  padding: 10px 15px;
  border-radius: 5px;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.2s ease;
  border: 1px solid transparent;
  display: inline-flex;
  align-items: center;
  gap: 5px;
}

.demande-actions .button:hover {
  transform: translateY(-1px);
  text-decoration: none;
}

/* Primary Button */
.demande-actions .button:not(.button--secondary) {
  background-color: #007bff;
  color: white;
  border-color: #007bff;
}

.demande-actions .button:not(.button--secondary):hover {
  background-color: #0056b3;
  border-color: #004085;
}

/* Secondary Button */
.demande-actions .button--secondary {
  background-color: #6c757d;
  color: white;
  border-color: #6c757d;
}

.demande-actions .button--secondary:hover {
  background-color: #5a6268;
  border-color: #545b62;
}

/* Empty State */
.client-dashboard p:last-child {
  text-align: center;
  padding: 40px 20px;
  background: #f8f9fa;
  border-radius: 8px;
  color: #6c757d;
  font-style: italic;
}

/* Responsive Design */
@media (max-width: 768px) {
  .action-hero {
    flex-direction: column;
    text-align: center;
    gap: 25px;
  }

  .action-stats {
    justify-content: center;
    gap: 20px;
  }

  .action-stats .stat-item {
    padding: 15px;
  }

  .action-content h2 {
    font-size: 1.6em;
  }

  .action-content p {
    font-size: 1em;
  }

  .btn-create-demande {
    padding: 12px 25px;
    font-size: 1em;
  }

  .demande-actions {
    flex-direction: column;
  }

  .demande-actions .button {
    text-align: center;
    justify-content: center;
  }

  .demande-item {
    padding: 15px;
  }

  .demande-item h3 {
    font-size: 1.1em;
  }
}

@media (max-width: 480px) {
  .client-dashboard {
    padding: 10px;
  }

  .action-hero {
    padding: 20px;
    margin: 0 -10px;
    border-radius: 10px;
  }

  .action-stats {
    flex-direction: column;
    gap: 15px;
  }

  .action-stats .stat-item {
    padding: 12px;
  }

  .action-stats .stat-number {
    font-size: 2em;
  }

  .action-content h2 {
    font-size: 1.4em;
  }

  .btn-create-demande {
    padding: 10px 20px;
    font-size: 0.95em;
    gap: 8px;
  }

  .demande-item {
    padding: 12px;
  }

  .demande-actions .button {
    padding: 8px 12px;
    font-size: 0.9em;
  }
}
