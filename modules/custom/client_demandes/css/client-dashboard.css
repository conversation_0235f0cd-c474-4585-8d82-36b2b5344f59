/* Client Dashboard Styles */

.client-dashboard {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

/* Dashboard Action Region */
.dashboard-action-region {
  margin-bottom: 40px;
}

.action-hero {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 15px;
  padding: 30px;
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
  position: relative;
  overflow: hidden;
}

.action-hero::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
  pointer-events: none;
}

.action-hero::after {
  content: '💼';
  position: absolute;
  top: 20px;
  right: 30px;
  font-size: 3em;
  opacity: 0.2;
  z-index: 1;
}

.action-content {
  flex: 1;
  z-index: 2;
}

.action-content h2 {
  font-size: 2em;
  margin-bottom: 10px;
  font-weight: 600;
}

.action-content p {
  font-size: 1.1em;
  margin-bottom: 25px;
  opacity: 0.9;
  line-height: 1.5;
}

/* Create Demande Button */
.btn-create-demande {
  display: inline-flex;
  align-items: center;
  gap: 12px;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  padding: 15px 30px;
  border-radius: 50px;
  text-decoration: none;
  font-weight: 600;
  font-size: 1.1em;
  transition: all 0.3s ease;
  border: 2px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
}

.btn-create-demande:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  text-decoration: none;
  color: white;
}

.btn-icon {
  font-size: 1.3em;
  display: flex;
  align-items: center;
}

.btn-text {
  font-weight: 600;
}

/* Action Stats */
.action-stats {
  display: flex;
  gap: 30px;
  z-index: 2;
}

.action-stats .stat-item {
  text-align: center;
  background: rgba(255, 255, 255, 0.15);
  padding: 20px;
  border-radius: 10px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.action-stats .stat-number {
  display: block;
  font-size: 2.5em;
  font-weight: bold;
  margin-bottom: 5px;
}

.action-stats .stat-label {
  display: block;
  font-size: 0.9em;
  opacity: 0.8;
}

/* Dashboard Actions */
.dashboard-actions {
  margin-bottom: 30px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

/* Dashboard Content Grid */
.dashboard-content-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
  margin-top: 30px;
}

/* Dashboard Sections */
.dashboard-section {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.2s ease;
}

.dashboard-section:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Section Headers */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 2px solid #f8f9fa;
}

.section-header h2 {
  color: #2c3e50;
  margin: 0;
  font-size: 1.4em;
  font-weight: 600;
}

.section-count {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 8px 15px;
  border-radius: 20px;
  font-weight: 600;
  font-size: 0.9em;
}

/* Devis List */
.devis-list,
.demandes-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.devis-item,
.demande-item {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
  transition: all 0.2s ease;
}

.devis-item:hover,
.demande-item:hover {
  background: #ffffff;
  border-color: #667eea;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(102, 126, 234, 0.2);
}

/* Item Headers */
.devis-header,
.demande-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.devis-header h3,
.demande-header h3 {
  color: #2c3e50;
  margin: 0;
  font-size: 1.2em;
  font-weight: 600;
}

/* Status Badges */
.badge {
  padding: 6px 12px;
  border-radius: 15px;
  font-size: 0.8em;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-accepted {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.status-refused {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.status-pending {
  background: #fff3cd;
  color: #856404;
  border: 1px solid #ffeaa7;
}

.status-active {
  background: #cce5ff;
  color: #004085;
  border: 1px solid #99d6ff;
}

/* Meta Information */
.devis-meta,
.demande-meta {
  margin-bottom: 15px;
}

.devis-meta p,
.demande-meta p {
  margin-bottom: 8px;
  color: #6c757d;
  font-size: 0.95em;
}

.devis-meta strong,
.demande-meta strong {
  color: #495057;
}

/* Price and Budget Styling */
.devis-price,
.demande-budget {
  background: #e8f5e8;
  border: 1px solid #c3e6cb;
  border-radius: 5px;
  padding: 8px 12px;
  margin: 8px 0;
  display: inline-block;
}

.price-amount,
.budget-amount {
  font-weight: bold;
  color: #28a745;
  font-size: 1.1em;
}

/* Actions */
.devis-actions,
.demande-actions {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.devis-actions .button,
.demande-actions .button {
  padding: 8px 15px;
  border-radius: 5px;
  text-decoration: none;
  font-weight: 500;
  font-size: 0.9em;
  transition: all 0.2s ease;
  border: 1px solid transparent;
  display: inline-flex;
  align-items: center;
  gap: 5px;
}

.devis-actions .button:hover,
.demande-actions .button:hover {
  transform: translateY(-1px);
  text-decoration: none;
}

/* Button Variants */
.button--success {
  background-color: #28a745;
  color: white;
  border-color: #28a745;
}

.button--success:hover {
  background-color: #218838;
  border-color: #1e7e34;
}

.button--danger {
  background-color: #dc3545;
  color: white;
  border-color: #dc3545;
}

.button--danger:hover {
  background-color: #c82333;
  border-color: #bd2130;
}

.button--info {
  background-color: #17a2b8;
  color: white;
  border-color: #17a2b8;
}

.button--info:hover {
  background-color: #138496;
  border-color: #117a8b;
}

/* Empty States */
.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #6c757d;
}

.empty-icon {
  font-size: 4em;
  margin-bottom: 15px;
  opacity: 0.5;
}

.empty-state p {
  margin-bottom: 10px;
  font-size: 1.1em;
}

.empty-subtitle {
  font-size: 0.95em;
  opacity: 0.8;
  font-style: italic;
}

/* Demandes List */
.client-demandes-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.client-demandes-list li {
  margin-bottom: 20px;
}

.demande-item {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.2s ease;
}

.demande-item:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.demande-item h3 {
  color: #2c3e50;
  margin-bottom: 15px;
  font-size: 1.3em;
}

.demande-item p {
  margin-bottom: 10px;
  color: #6c757d;
}

.demande-item strong {
  color: #495057;
}

/* Demande Actions */
.demande-actions {
  display: flex;
  gap: 10px;
  margin-top: 15px;
  flex-wrap: wrap;
}

.demande-actions .button {
  padding: 10px 15px;
  border-radius: 5px;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.2s ease;
  border: 1px solid transparent;
  display: inline-flex;
  align-items: center;
  gap: 5px;
}

.demande-actions .button:hover {
  transform: translateY(-1px);
  text-decoration: none;
}

/* Primary Button */
.demande-actions .button:not(.button--secondary) {
  background-color: #007bff;
  color: white;
  border-color: #007bff;
}

.demande-actions .button:not(.button--secondary):hover {
  background-color: #0056b3;
  border-color: #004085;
}

/* Secondary Button */
.demande-actions .button--secondary {
  background-color: #6c757d;
  color: white;
  border-color: #6c757d;
}

.demande-actions .button--secondary:hover {
  background-color: #5a6268;
  border-color: #545b62;
}

/* Empty State */
.client-dashboard p:last-child {
  text-align: center;
  padding: 40px 20px;
  background: #f8f9fa;
  border-radius: 8px;
  color: #6c757d;
  font-style: italic;
}

/* Responsive Design */
@media (max-width: 768px) {
  .action-hero {
    flex-direction: column;
    text-align: center;
    gap: 25px;
  }

  .action-stats {
    justify-content: center;
    gap: 20px;
  }

  .action-stats .stat-item {
    padding: 15px;
  }

  .action-content h2 {
    font-size: 1.6em;
  }

  .action-content p {
    font-size: 1em;
  }

  .btn-create-demande {
    padding: 12px 25px;
    font-size: 1em;
  }

  /* Dashboard Grid */
  .dashboard-content-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .dashboard-section {
    padding: 20px;
  }

  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .section-header h2 {
    font-size: 1.2em;
  }

  .devis-header,
  .demande-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .devis-actions,
  .demande-actions {
    flex-direction: column;
  }

  .devis-actions .button,
  .demande-actions .button {
    text-align: center;
    justify-content: center;
  }

  .devis-item,
  .demande-item {
    padding: 15px;
  }

  .devis-item h3,
  .demande-item h3 {
    font-size: 1.1em;
  }
}

@media (max-width: 480px) {
  .client-dashboard {
    padding: 10px;
  }

  .action-hero {
    padding: 20px;
    margin: 0 -10px;
    border-radius: 10px;
  }

  .action-stats {
    flex-direction: column;
    gap: 15px;
  }

  .action-stats .stat-item {
    padding: 12px;
  }

  .action-stats .stat-number {
    font-size: 2em;
  }

  .action-content h2 {
    font-size: 1.4em;
  }

  .btn-create-demande {
    padding: 10px 20px;
    font-size: 0.95em;
    gap: 8px;
  }

  .demande-item {
    padding: 12px;
  }

  .demande-actions .button {
    padding: 8px 12px;
    font-size: 0.9em;
  }
}
