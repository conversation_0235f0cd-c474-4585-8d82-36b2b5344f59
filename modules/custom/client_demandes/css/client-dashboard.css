/* Client Dashboard Styles */

.client-dashboard {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

/* Dashboard Actions */
.dashboard-actions {
  margin-bottom: 30px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

/* Demandes List */
.client-demandes-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.client-demandes-list li {
  margin-bottom: 20px;
}

.demande-item {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.2s ease;
}

.demande-item:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.demande-item h3 {
  color: #2c3e50;
  margin-bottom: 15px;
  font-size: 1.3em;
}

.demande-item p {
  margin-bottom: 10px;
  color: #6c757d;
}

.demande-item strong {
  color: #495057;
}

/* Demande Actions */
.demande-actions {
  display: flex;
  gap: 10px;
  margin-top: 15px;
  flex-wrap: wrap;
}

.demande-actions .button {
  padding: 10px 15px;
  border-radius: 5px;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.2s ease;
  border: 1px solid transparent;
  display: inline-flex;
  align-items: center;
  gap: 5px;
}

.demande-actions .button:hover {
  transform: translateY(-1px);
  text-decoration: none;
}

/* Primary Button */
.demande-actions .button:not(.button--secondary) {
  background-color: #007bff;
  color: white;
  border-color: #007bff;
}

.demande-actions .button:not(.button--secondary):hover {
  background-color: #0056b3;
  border-color: #004085;
}

/* Secondary Button */
.demande-actions .button--secondary {
  background-color: #6c757d;
  color: white;
  border-color: #6c757d;
}

.demande-actions .button--secondary:hover {
  background-color: #5a6268;
  border-color: #545b62;
}

/* Empty State */
.client-dashboard p:last-child {
  text-align: center;
  padding: 40px 20px;
  background: #f8f9fa;
  border-radius: 8px;
  color: #6c757d;
  font-style: italic;
}

/* Responsive Design */
@media (max-width: 768px) {
  .demande-actions {
    flex-direction: column;
  }
  
  .demande-actions .button {
    text-align: center;
    justify-content: center;
  }
  
  .demande-item {
    padding: 15px;
  }
  
  .demande-item h3 {
    font-size: 1.1em;
  }
}

@media (max-width: 480px) {
  .client-dashboard {
    padding: 10px;
  }
  
  .demande-item {
    padding: 12px;
  }
  
  .demande-actions .button {
    padding: 8px 12px;
    font-size: 0.9em;
  }
}
