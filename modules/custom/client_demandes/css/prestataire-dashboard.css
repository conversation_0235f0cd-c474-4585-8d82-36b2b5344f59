/* Prestataire Dashboard Styles */

.prestataire-dashboard {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

/* Stripe Connect Section */
.stripe-connect-section {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 30px;
}

.stripe-connect-section h2 {
  color: #635bff;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
}

.stripe-connect-section h2:before {
  content: "💳";
  margin-right: 10px;
}

.stripe-info p {
  margin-bottom: 10px;
}

/* Statistics Grid */
.quotes-stats-section {
  margin-bottom: 30px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

/* 3 Values Grid */
.stats-grid-three {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 25px;
  margin-top: 20px;
}

.stat-item {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px;
  border-radius: 10px;
  text-align: center;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
}

.stat-item:hover {
  transform: translateY(-2px);
}

.stat-number {
  display: block;
  font-size: 2.5em;
  font-weight: bold;
  margin-bottom: 5px;
}

.stat-label {
  display: block;
  font-size: 0.9em;
  opacity: 0.9;
}

.stat-detail {
  display: block;
  font-size: 0.8em;
  opacity: 0.8;
  margin-top: 5px;
}

/* Specific colors for the 3 key values */
.stat-earnings {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.stat-accepted {
  background: linear-gradient(135deg, #007bff 0%, #6610f2 100%);
}

.stat-success {
  background: linear-gradient(135deg, #fd7e14 0%, #e83e8c 100%);
}

/* Demandes List */
.demandes-list {
  display: grid;
  gap: 20px;
  margin-top: 20px;
}

.demande-card {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.2s ease;
}

.demande-card:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.demande-card h3 {
  color: #2c3e50;
  margin-bottom: 15px;
  font-size: 1.3em;
}

.demande-meta {
  margin-bottom: 20px;
}

.demande-meta p {
  margin-bottom: 8px;
  color: #6c757d;
}

.demande-meta strong {
  color: #495057;
}

.demande-actions {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.demande-actions .button {
  padding: 10px 15px;
  border-radius: 5px;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.2s ease;
}

.demande-actions .button:hover {
  transform: translateY(-1px);
}

/* No Demandes State */
.no-demandes {
  text-align: center;
  padding: 40px 20px;
  background: #f8f9fa;
  border-radius: 8px;
  color: #6c757d;
}

.no-demandes p {
  margin-bottom: 10px;
}

/* Recent Quotes Section */
.recent-quotes-section {
  background: #e8f4fd;
  border: 1px solid #b8daff;
  border-radius: 8px;
  padding: 20px;
  margin-top: 30px;
  text-align: center;
}

.recent-quotes-section h2 {
  color: #0056b3;
  margin-bottom: 10px;
}

.recent-quotes-section p {
  color: #495057;
  margin-bottom: 15px;
}

/* Dashboard Summary */
.dashboard-summary {
  margin-bottom: 30px;
}

.dashboard-summary .item-list {
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 20px;
}

.dashboard-summary .item-list h3 {
  color: #28a745;
  margin-bottom: 15px;
}

.dashboard-summary .item-list ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.dashboard-summary .item-list li {
  padding: 8px 0;
  border-bottom: 1px solid #f8f9fa;
  color: #495057;
}

.dashboard-summary .item-list li:last-child {
  border-bottom: none;
}

/* Responsive Design */
@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .stats-grid-three {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .demande-actions {
    flex-direction: column;
  }

  .demande-actions .button {
    text-align: center;
  }
}

@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }

  .stats-grid-three {
    grid-template-columns: 1fr;
  }

  .stat-number {
    font-size: 2em;
  }
}
