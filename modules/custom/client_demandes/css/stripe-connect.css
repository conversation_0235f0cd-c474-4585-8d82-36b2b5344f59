/* Stripe Connect Styles */

.stripe-connect-setup {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.stripe-connect-setup h2 {
  color: #32325d;
  font-size: 2em;
  font-weight: 600;
  margin-bottom: 20px;
  text-align: center;
}

.stripe-connect-setup h3 {
  color: #32325d;
  font-size: 1.3em;
  font-weight: 600;
  margin-bottom: 15px;
}

.stripe-connect-setup h4 {
  color: #525f7f;
  font-size: 1.1em;
  font-weight: 600;
  margin-bottom: 10px;
}

.stripe-connect-setup p {
  color: #525f7f;
  line-height: 1.6;
  margin-bottom: 15px;
}

/* Benefits Section */
.benefits {
  background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%);
  border: 1px solid #c3e6cb;
  border-radius: 12px;
  padding: 25px;
  margin: 25px 0;
  box-shadow: 0 2px 8px rgba(40, 167, 69, 0.1);
}

.benefits h3 {
  color: #155724;
  margin-bottom: 15px;
}

.benefits ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.benefits li {
  color: #155724;
  padding: 8px 0;
  position: relative;
  padding-left: 25px;
}

.benefits li:before {
  content: "✓";
  position: absolute;
  left: 0;
  color: #28a745;
  font-weight: bold;
  font-size: 1.1em;
}

/* Connect Action */
.connect-action {
  text-align: center;
  margin: 40px 0;
}

.stripe-connect-button {
  display: inline-block;
  background: linear-gradient(135deg, #635bff 0%, #5a52e8 100%);
  color: white;
  padding: 18px 35px;
  text-decoration: none;
  border-radius: 8px;
  font-weight: 600;
  font-size: 18px;
  box-shadow: 0 6px 16px rgba(99, 91, 255, 0.4);
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.stripe-connect-button:hover {
  background: linear-gradient(135deg, #5a52e8 0%, #4f46d1 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(99, 91, 255, 0.5);
  text-decoration: none;
  color: white;
}

.stripe-connect-button:active {
  transform: translateY(0);
  box-shadow: 0 4px 12px rgba(99, 91, 255, 0.4);
}

.stripe-connect-button:before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.stripe-connect-button:hover:before {
  left: 100%;
}

/* Instructions Section */
.instructions {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 25px;
  margin: 25px 0;
}

.instructions h3 {
  color: #495057;
  margin-bottom: 15px;
}

.instructions ol {
  color: #495057;
  padding-left: 20px;
}

.instructions li {
  margin-bottom: 8px;
  line-height: 1.5;
}

/* Help Section */
.help {
  background: linear-gradient(135deg, #cce5ff 0%, #e0f0ff 100%);
  border: 1px solid #b3d9ff;
  border-radius: 12px;
  padding: 20px;
  margin: 25px 0;
}

.help h4 {
  color: #0056b3;
  margin-bottom: 10px;
}

.help p {
  color: #0056b3;
  margin-bottom: 0;
}

.help a {
  color: #0056b3;
  text-decoration: underline;
}

.help a:hover {
  color: #004085;
}

/* Connected Status */
.connected-status {
  background: linear-gradient(135deg, #d4edda 0%, #e8f5e8 100%);
  border: 1px solid #c3e6cb;
  border-radius: 12px;
  padding: 25px;
  margin: 25px 0;
  box-shadow: 0 2px 8px rgba(40, 167, 69, 0.1);
}

.connected-status h3 {
  color: #155724;
  margin-bottom: 15px;
}

.connected-status p {
  color: #155724;
  margin-bottom: 10px;
}

.connected-status strong {
  font-weight: 600;
}

/* Actions */
.actions {
  text-align: center;
  margin: 30px 0;
}

.actions .button {
  display: inline-block;
  padding: 12px 24px;
  text-decoration: none;
  border-radius: 6px;
  font-weight: 600;
  font-size: 16px;
  margin: 0 8px;
  transition: all 0.2s ease;
  border: none;
  cursor: pointer;
}

.actions .button:hover {
  transform: translateY(-1px);
  text-decoration: none;
}

/* Next Steps */
.next-steps {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 25px;
  margin: 25px 0;
}

.next-steps h3 {
  color: #495057;
  margin-bottom: 15px;
}

.next-steps ul {
  color: #495057;
  padding-left: 20px;
}

.next-steps li {
  margin-bottom: 8px;
  line-height: 1.5;
}

/* Responsive Design */
@media (max-width: 768px) {
  .stripe-connect-setup {
    padding: 15px;
  }
  
  .stripe-connect-button {
    padding: 15px 25px;
    font-size: 16px;
  }
  
  .benefits,
  .instructions,
  .help,
  .connected-status,
  .next-steps {
    padding: 20px;
    margin: 20px 0;
  }
  
  .actions .button {
    display: block;
    margin: 10px 0;
    width: 100%;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .stripe-connect-setup h2 {
    font-size: 1.5em;
  }
  
  .stripe-connect-button {
    padding: 12px 20px;
    font-size: 14px;
  }
  
  .benefits,
  .instructions,
  .help,
  .connected-status,
  .next-steps {
    padding: 15px;
  }
}

/* Loading State */
.stripe-connect-button.loading {
  opacity: 0.7;
  cursor: not-allowed;
  pointer-events: none;
}

.stripe-connect-button.loading:after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
