services:
  client_demandes.stripe_mode:
    class: <PERSON><PERSON>al\client_demandes\Service\StripeModeService
    arguments: ['@config.factory']

  client_demandes.stripe_controller:
    class: <PERSON>upal\client_demandes\Controller\StripeController
    arguments: ['@client_demandes.stripe_mode']
    tags:
      - { name: controller.service_arguments }

  client_demandes.notification_service:
    class: Drupal\client_demandes\Service\NotificationService
    arguments: ['@entity_type.manager', '@plugin.manager.mail', '@language_manager', '@config.factory']

  client_demandes.client_controller:
    class: Drupal\client_demandes\Controller\ClientController
    arguments: ['@current_user']
    tags:
      - { name: controller.service_arguments }

  client_demandes.login_redirect_subscriber:
    class: <PERSON>upal\client_demandes\EventSubscriber\LoginRedirectSubscriber
    arguments: ['@current_user']
    tags:
      - { name: event_subscriber }
