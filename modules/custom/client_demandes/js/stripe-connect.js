/**
 * @file
 * JavaScript pour l'intégration Stripe Connect.
 */

(function ($, <PERSON><PERSON><PERSON>) {
  'use strict';

  /**
   * Comportement pour Stripe Connect.
   */
  Drupal.behaviors.stripeConnect = {
    attach: function (context, settings) {
      // Gérer le clic sur le bouton de connexion Stripe
      $('.stripe-connect-button', context).once('stripe-connect').on('click', function(e) {
        var $button = $(this);
        
        // Ajouter un état de chargement
        $button.addClass('loading');
        $button.text('Connexion en cours...');
        
        // Permettre la navigation normale vers Stripe
        // Le bouton redirigera vers l'URL OAuth de Stripe
      });
      
      // Gérer les messages de retour
      if (window.location.search.includes('stripe_success=1')) {
        Drupal.announce('Connexion Stripe réussie !', 'status');
      }
      
      if (window.location.search.includes('stripe_error=1')) {
        Drupal.announce('Erreur lors de la connexion Stripe.', 'error');
      }
      
      // Animation pour les sections
      $('.benefits, .instructions, .help, .connected-status, .next-steps', context)
        .once('stripe-animate')
        .each(function(index) {
          var $element = $(this);
          setTimeout(function() {
            $element.addClass('animate-in');
          }, index * 100);
        });
    }
  };

  /**
   * Utilitaires pour Stripe Connect.
   */
  Drupal.stripeConnect = {
    
    /**
     * Vérifie le statut de connexion Stripe.
     */
    checkConnectionStatus: function() {
      // Cette fonction pourrait faire un appel AJAX pour vérifier le statut
      // en temps réel si nécessaire
    },
    
    /**
     * Gère la déconnexion Stripe.
     */
    handleDisconnect: function() {
      if (confirm('Êtes-vous sûr de vouloir déconnecter votre compte Stripe ?')) {
        window.location.href = '/prestataire/stripe/disconnect';
      }
    },
    
    /**
     * Affiche une notification.
     */
    showNotification: function(message, type) {
      type = type || 'status';
      
      var $notification = $('<div class="stripe-notification stripe-notification--' + type + '">')
        .text(message)
        .appendTo('body');
      
      setTimeout(function() {
        $notification.addClass('stripe-notification--visible');
      }, 100);
      
      setTimeout(function() {
        $notification.removeClass('stripe-notification--visible');
        setTimeout(function() {
          $notification.remove();
        }, 300);
      }, 5000);
    }
  };

})(jQuery, Drupal);
