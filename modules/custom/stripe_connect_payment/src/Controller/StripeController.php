<?php

namespace Drupal\stripe_connect_payment\Controller;

use Drupal\Core\Controller\ControllerBase;
use Symfony\Component\HttpFoundation\RedirectResponse;

class StripeController extends ControllerBase
{

  public function redirectToStripe()
  {
    $nid = \Drupal::request()->query->get('nid');
    $mode = \Drupal::request()->query->get('mode');
    $amount = \Drupal::request()->query->get('amount');

    $node = \Drupal\node\Entity\Node::load($nid);
    $creator = \Drupal\user\Entity\User::load($node->getOwnerId());
    $stripe_user_id = $creator->get('field_stripe_user_id')->value;

    \Stripe\Stripe::setApiKey('sk_test_xxx'); // À remplacer par votre clé secrète

    $session_data = [
      'payment_method_types' => ['card'],
      'line_items' => [[
        'price_data' => [
          'currency' => 'eur',
          'product_data' => ['name' => $node->label()],
          'unit_amount' => intval($amount * 100),
        ],
        'quantity' => 1,
      ]],
      'mode' => 'payment',
      'success_url' => '/merci',
      'cancel_url' => '/annule',
    ];

    if ($mode === 'direct' && $stripe_user_id) {
      $session_data['payment_intent_data'] = [
        'application_fee_amount' => intval($amount * 0.05 * 100),
        'transfer_data' => [
          'destination' => $stripe_user_id,
        ],
      ];
    }

    $session = \Stripe\Checkout\Session::create($session_data);
    return new RedirectResponse($session->url);
  }
}
