/* @license GNU-GPL-2.0-or-later https://raw.githubusercontent.com/ckeditor/ckeditor5/v40.2.0/LICENSE.md */
/*!
 * @license Copyright (c) 2003-2023, CKSource Holding sp. z o.o. All rights reserved.
 * For licensing, see LICENSE.md.
 */(()=>{var e={73:(e,t,o)=>{"use strict";o.d(t,{Z:()=>n});var r=o(609),i=o.n(r)()((function(e){return e[1]}));i.push([e.id,".ck.ck-editor{position:relative}.ck.ck-editor .ck-editor__top .ck-sticky-panel .ck-toolbar{z-index:var(--ck-z-modal)}.ck.ck-editor__top .ck-sticky-panel .ck-toolbar{border-radius:0}.ck-rounded-corners .ck.ck-editor__top .ck-sticky-panel .ck-toolbar,.ck.ck-editor__top .ck-sticky-panel .ck-toolbar.ck-rounded-corners{border-radius:var(--ck-border-radius);border-bottom-left-radius:0;border-bottom-right-radius:0}.ck.ck-editor__top .ck-sticky-panel .ck-toolbar{border-bottom-width:0}.ck.ck-editor__top .ck-sticky-panel .ck-sticky-panel__content_sticky .ck-toolbar{border-bottom-width:1px;border-radius:0}.ck-rounded-corners .ck.ck-editor__top .ck-sticky-panel .ck-sticky-panel__content_sticky .ck-toolbar,.ck.ck-editor__top .ck-sticky-panel .ck-sticky-panel__content_sticky .ck-toolbar.ck-rounded-corners{border-radius:var(--ck-border-radius);border-radius:0}.ck.ck-editor__main>.ck-editor__editable{background:var(--ck-color-base-background);border-radius:0}.ck-rounded-corners .ck.ck-editor__main>.ck-editor__editable,.ck.ck-editor__main>.ck-editor__editable.ck-rounded-corners{border-radius:var(--ck-border-radius);border-top-left-radius:0;border-top-right-radius:0}.ck.ck-editor__main>.ck-editor__editable:not(.ck-focused){border-color:var(--ck-color-base-border)}",""]);const n=i},609:e=>{"use strict";e.exports=function(e){var t=[];return t.toString=function(){return this.map((function(t){var o=e(t);return t[2]?"@media ".concat(t[2]," {").concat(o,"}"):o})).join("")},t.i=function(e,o,r){"string"==typeof e&&(e=[[null,e,""]]);var i={};if(r)for(var n=0;n<this.length;n++){var c=this[n][0];null!=c&&(i[c]=!0)}for(var a=0;a<e.length;a++){var s=[].concat(e[a]);r&&i[s[0]]||(o&&(s[2]?s[2]="".concat(o," and ").concat(s[2]):s[2]=o),t.push(s))}},t}},62:(e,t,o)=>{"use strict";var r,i=function(){return void 0===r&&(r=Boolean(window&&document&&document.all&&!window.atob)),r},n=function(){var e={};return function(t){if(void 0===e[t]){var o=document.querySelector(t);if(window.HTMLIFrameElement&&o instanceof window.HTMLIFrameElement)try{o=o.contentDocument.head}catch(e){o=null}e[t]=o}return e[t]}}(),c=[];function a(e){for(var t=-1,o=0;o<c.length;o++)if(c[o].identifier===e){t=o;break}return t}function s(e,t){for(var o={},r=[],i=0;i<e.length;i++){var n=e[i],s=t.base?n[0]+t.base:n[0],l=o[s]||0,d="".concat(s," ").concat(l);o[s]=l+1;var u=a(d),h={css:n[1],media:n[2],sourceMap:n[3]};-1!==u?(c[u].references++,c[u].updater(h)):c.push({identifier:d,updater:k(h,t),references:1}),r.push(d)}return r}function l(e){var t=document.createElement("style"),r=e.attributes||{};if(void 0===r.nonce){var i=o.nc;i&&(r.nonce=i)}if(Object.keys(r).forEach((function(e){t.setAttribute(e,r[e])})),"function"==typeof e.insert)e.insert(t);else{var c=n(e.insert||"head");if(!c)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");c.appendChild(t)}return t}var d,u=(d=[],function(e,t){return d[e]=t,d.filter(Boolean).join("\n")});function h(e,t,o,r){var i=o?"":r.media?"@media ".concat(r.media," {").concat(r.css,"}"):r.css;if(e.styleSheet)e.styleSheet.cssText=u(t,i);else{var n=document.createTextNode(i),c=e.childNodes;c[t]&&e.removeChild(c[t]),c.length?e.insertBefore(n,c[t]):e.appendChild(n)}}function p(e,t,o){var r=o.css,i=o.media,n=o.sourceMap;if(i?e.setAttribute("media",i):e.removeAttribute("media"),n&&"undefined"!=typeof btoa&&(r+="\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(encodeURIComponent(JSON.stringify(n))))," */")),e.styleSheet)e.styleSheet.cssText=r;else{for(;e.firstChild;)e.removeChild(e.firstChild);e.appendChild(document.createTextNode(r))}}var f=null,b=0;function k(e,t){var o,r,i;if(t.singleton){var n=b++;o=f||(f=l(t)),r=h.bind(null,o,n,!1),i=h.bind(null,o,n,!0)}else o=l(t),r=p.bind(null,o,t),i=function(){!function(e){if(null===e.parentNode)return!1;e.parentNode.removeChild(e)}(o)};return r(e),function(t){if(t){if(t.css===e.css&&t.media===e.media&&t.sourceMap===e.sourceMap)return;r(e=t)}else i()}}e.exports=function(e,t){(t=t||{}).singleton||"boolean"==typeof t.singleton||(t.singleton=i());var o=s(e=e||[],t);return function(e){if(e=e||[],"[object Array]"===Object.prototype.toString.call(e)){for(var r=0;r<o.length;r++){var i=a(o[r]);c[i].references--}for(var n=s(e,t),l=0;l<o.length;l++){var d=a(o[l]);0===c[d].references&&(c[d].updater(),c.splice(d,1))}o=n}}}},704:(e,t,o)=>{e.exports=o(79)("./src/core.js")},492:(e,t,o)=>{e.exports=o(79)("./src/engine.js")},273:(e,t,o)=>{e.exports=o(79)("./src/ui.js")},209:(e,t,o)=>{e.exports=o(79)("./src/utils.js")},434:(e,t,o)=>{e.exports=o(79)("./src/watchdog.js")},79:e=>{"use strict";e.exports=CKEditor5.dll}},t={};function o(r){var i=t[r];if(void 0!==i)return i.exports;var n=t[r]={id:r,exports:{}};return e[r](n,n.exports,o),n.exports}o.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return o.d(t,{a:t}),t},o.d=(e,t)=>{for(var r in t)o.o(t,r)&&!o.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},o.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),o.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},o.nc=void 0;var r={};(()=>{"use strict";o.r(r),o.d(r,{ClassicEditor:()=>D});var e=o(273),t=o(492),i=o(209);class n extends e.EditorUI{constructor(t,o){super(t),this.view=o,this._toolbarConfig=(0,e.normalizeToolbarConfig)(t.config.get("toolbar")),this._elementReplacer=new i.ElementReplacer,this.listenTo(t.editing.view,"scrollToTheSelection",this._handleScrollToTheSelectionWithStickyPanel.bind(this))}get element(){return this.view.element}init(e){const t=this.editor,o=this.view,r=t.editing.view,i=o.editable,n=r.document.getRoot();i.name=n.rootName,o.render();const c=i.element;this.setEditableElement(i.name,c),o.editable.bind("isFocused").to(this.focusTracker),r.attachDomRoot(c),e&&this._elementReplacer.replace(e,this.element),this._initPlaceholder(),this._initToolbar(),this.fire("ready")}destroy(){super.destroy();const e=this.view,t=this.editor.editing.view;this._elementReplacer.restore(),t.detachDomRoot(e.editable.name),e.destroy()}_initToolbar(){const e=this.view;e.stickyPanel.bind("isActive").to(this.focusTracker,"isFocused"),e.stickyPanel.limiterElement=e.element,e.stickyPanel.bind("viewportTopOffset").to(this,"viewportOffset",(({top:e})=>e||0)),e.toolbar.fillFromConfig(this._toolbarConfig,this.componentFactory),this.addToolbar(e.toolbar)}_initPlaceholder(){const e=this.editor,o=e.editing.view,r=o.document.getRoot(),i=e.sourceElement;let n;const c=e.config.get("placeholder");c&&(n="string"==typeof c?c:c[this.view.editable.name]),!n&&i&&"textarea"===i.tagName.toLowerCase()&&(n=i.getAttribute("placeholder")),n&&(r.placeholder=n),(0,t.enablePlaceholder)({view:o,element:r,isDirectHost:!1,keepOnFocus:!0})}_handleScrollToTheSelectionWithStickyPanel(e,t,o){const r=this.view.stickyPanel;if(r.isSticky){const e=new i.Rect(r.element).height;t.viewportOffset.top+=e}else{const e=()=>{this.editor.editing.view.scrollToTheSelection(o)};this.listenTo(r,"change:isSticky",e),setTimeout((()=>{this.stopListening(r,"change:isSticky",e)}),20)}}}var c=o(62),a=o.n(c),s=o(73),l={injectType:"singletonStyleTag",attributes:{"data-cke":!0},insert:"head",singleton:!0};a()(s.Z,l);s.Z.locals;class d extends e.BoxedEditorUIView{constructor(t,o,r={}){super(t),this.stickyPanel=new e.StickyPanelView(t),this.toolbar=new e.ToolbarView(t,{shouldGroupWhenFull:r.shouldToolbarGroupWhenFull}),this.editable=new e.InlineEditableUIView(t,o)}render(){super.render(),this.stickyPanel.content.add(this.toolbar),this.top.add(this.stickyPanel),this.main.add(this.editable)}}var u=o(704),h=o(434);const p=function(e){return null!=e&&"object"==typeof e};const f="object"==typeof global&&global&&global.Object===Object&&global;var b="object"==typeof self&&self&&self.Object===Object&&self;const k=(f||b||Function("return this")()).Symbol;var v=Object.prototype,m=v.hasOwnProperty,g=v.toString,y=k?k.toStringTag:void 0;const _=function(e){var t=m.call(e,y),o=e[y];try{e[y]=void 0;var r=!0}catch(e){}var i=g.call(e);return r&&(t?e[y]=o:delete e[y]),i};var w=Object.prototype.toString;const T=function(e){return w.call(e)};var j=k?k.toStringTag:void 0;const S=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":j&&j in Object(e)?_(e):T(e)};const x=function(e,t){return function(o){return e(t(o))}}(Object.getPrototypeOf,Object);var E=Function.prototype,O=Object.prototype,C=E.toString,P=O.hasOwnProperty,F=C.call(Object);const M=function(e){if(!p(e)||"[object Object]"!=S(e))return!1;var t=x(e);if(null===t)return!0;var o=P.call(t,"constructor")&&t.constructor;return"function"==typeof o&&o instanceof o&&C.call(o)==F};const R=function(e){return p(e)&&1===e.nodeType&&!M(e)};class D extends((0,u.DataApiMixin)((0,u.ElementApiMixin)(u.Editor))){constructor(e,t={}){if(!N(e)&&void 0!==t.initialData)throw new i.CKEditorError("editor-create-initial-data",null);super(t),void 0===this.config.get("initialData")&&this.config.set("initialData",function(e){return N(e)?(0,i.getDataFromElement)(e):e}(e)),N(e)&&(this.sourceElement=e),this.model.document.createRoot();const o=!this.config.get("toolbar.shouldNotGroupWhenFull"),r=new d(this.locale,this.editing.view,{shouldToolbarGroupWhenFull:o});this.ui=new n(this,r),(0,u.attachToForm)(this)}destroy(){return this.sourceElement&&this.updateSourceElement(),this.ui.destroy(),super.destroy()}static create(e,t={}){return new Promise((o=>{const r=new this(e,t);o(r.initPlugins().then((()=>r.ui.init(N(e)?e:null))).then((()=>r.data.init(r.config.get("initialData")))).then((()=>r.fire("ready"))).then((()=>r)))}))}}function N(e){return R(e)}D.Context=u.Context,D.EditorWatchdog=h.EditorWatchdog,D.ContextWatchdog=h.ContextWatchdog})(),(window.CKEditor5=window.CKEditor5||{}).editorClassic=r})();;
!function(t){const e=t.en=t.en||{};e.dictionary=Object.assign(e.dictionary||{},{"HTML object":"HTML object"})}(window.CKEDITOR_TRANSLATIONS||(window.CKEDITOR_TRANSLATIONS={})),
/*!
 * @license Copyright (c) 2003-2023, CKSource Holding sp. z o.o. All rights reserved.
 * For licensing, see LICENSE.md.
 */(()=>{var t={90:(t,e,r)=>{"use strict";r.d(e,{Z:()=>i});var o=r(609),n=r.n(o)()((function(t){return t[1]}));n.push([t.id,":root{--ck-html-object-embed-unfocused-outline-width:1px}.ck-widget.html-object-embed{background-color:var(--ck-color-base-foreground);font-size:var(--ck-font-size-base);min-width:calc(76px + var(--ck-spacing-standard));padding:var(--ck-spacing-small);padding-top:calc(var(--ck-font-size-tiny) + var(--ck-spacing-large))}.ck-widget.html-object-embed:not(.ck-widget_selected):not(:hover){outline:var(--ck-html-object-embed-unfocused-outline-width) dashed var(--ck-color-widget-blurred-border)}.ck-widget.html-object-embed:before{background:#999;border-radius:0 0 var(--ck-border-radius) var(--ck-border-radius);color:var(--ck-color-base-background);content:attr(data-html-object-embed-label);font-family:var(--ck-font-face);font-size:var(--ck-font-size-tiny);font-style:normal;font-weight:400;left:var(--ck-spacing-standard);padding:calc(var(--ck-spacing-tiny) + var(--ck-html-object-embed-unfocused-outline-width)) var(--ck-spacing-small) var(--ck-spacing-tiny);position:absolute;top:0;transition:background var(--ck-widget-handler-animation-duration) var(--ck-widget-handler-animation-curve)}.ck-widget.html-object-embed .ck-widget__type-around .ck-widget__type-around__button.ck-widget__type-around__button_before{margin-left:50px}.ck-widget.html-object-embed .html-object-embed__content{pointer-events:none}div.ck-widget.html-object-embed{margin:1em auto}span.ck-widget.html-object-embed{display:inline-block}",""]);const i=n},609:t=>{"use strict";t.exports=function(t){var e=[];return e.toString=function(){return this.map((function(e){var r=t(e);return e[2]?"@media ".concat(e[2]," {").concat(r,"}"):r})).join("")},e.i=function(t,r,o){"string"==typeof t&&(t=[[null,t,""]]);var n={};if(o)for(var i=0;i<this.length;i++){var s=this[i][0];null!=s&&(n[s]=!0)}for(var l=0;l<t.length;l++){var a=[].concat(t[l]);o&&n[a[0]]||(r&&(a[2]?a[2]="".concat(r," and ").concat(a[2]):a[2]=r),e.push(a))}},e}},62:(t,e,r)=>{"use strict";var o,n=function(){return void 0===o&&(o=Boolean(window&&document&&document.all&&!window.atob)),o},i=function(){var t={};return function(e){if(void 0===t[e]){var r=document.querySelector(e);if(window.HTMLIFrameElement&&r instanceof window.HTMLIFrameElement)try{r=r.contentDocument.head}catch(t){r=null}t[e]=r}return t[e]}}(),s=[];function l(t){for(var e=-1,r=0;r<s.length;r++)if(s[r].identifier===t){e=r;break}return e}function a(t,e){for(var r={},o=[],n=0;n<t.length;n++){var i=t[n],a=e.base?i[0]+e.base:i[0],c=r[a]||0,u="".concat(a," ").concat(c);r[a]=c+1;var m=l(u),d={css:i[1],media:i[2],sourceMap:i[3]};-1!==m?(s[m].references++,s[m].updater(d)):s.push({identifier:u,updater:p(d,e),references:1}),o.push(u)}return o}function c(t){var e=document.createElement("style"),o=t.attributes||{};if(void 0===o.nonce){var n=r.nc;n&&(o.nonce=n)}if(Object.keys(o).forEach((function(t){e.setAttribute(t,o[t])})),"function"==typeof t.insert)t.insert(e);else{var s=i(t.insert||"head");if(!s)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");s.appendChild(e)}return e}var u,m=(u=[],function(t,e){return u[t]=e,u.filter(Boolean).join("\n")});function d(t,e,r,o){var n=r?"":o.media?"@media ".concat(o.media," {").concat(o.css,"}"):o.css;if(t.styleSheet)t.styleSheet.cssText=m(e,n);else{var i=document.createTextNode(n),s=t.childNodes;s[e]&&t.removeChild(s[e]),s.length?t.insertBefore(i,s[e]):t.appendChild(i)}}function f(t,e,r){var o=r.css,n=r.media,i=r.sourceMap;if(n?t.setAttribute("media",n):t.removeAttribute("media"),i&&"undefined"!=typeof btoa&&(o+="\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(encodeURIComponent(JSON.stringify(i))))," */")),t.styleSheet)t.styleSheet.cssText=o;else{for(;t.firstChild;)t.removeChild(t.firstChild);t.appendChild(document.createTextNode(o))}}var h=null,b=0;function p(t,e){var r,o,n;if(e.singleton){var i=b++;r=h||(h=c(e)),o=d.bind(null,r,i,!1),n=d.bind(null,r,i,!0)}else r=c(e),o=f.bind(null,r,e),n=function(){!function(t){if(null===t.parentNode)return!1;t.parentNode.removeChild(t)}(r)};return o(t),function(e){if(e){if(e.css===t.css&&e.media===t.media&&e.sourceMap===t.sourceMap)return;o(t=e)}else n()}}t.exports=function(t,e){(e=e||{}).singleton||"boolean"==typeof e.singleton||(e.singleton=n());var r=a(t=t||[],e);return function(t){if(t=t||[],"[object Array]"===Object.prototype.toString.call(t)){for(var o=0;o<r.length;o++){var n=l(r[o]);s[n].references--}for(var i=a(t,e),c=0;c<r.length;c++){var u=l(r[c]);0===s[u].references&&(s[u].updater(),s.splice(u,1))}r=i}}}},704:(t,e,r)=>{t.exports=r(79)("./src/core.js")},492:(t,e,r)=>{t.exports=r(79)("./src/engine.js")},331:(t,e,r)=>{t.exports=r(79)("./src/enter.js")},209:(t,e,r)=>{t.exports=r(79)("./src/utils.js")},995:(t,e,r)=>{t.exports=r(79)("./src/widget.js")},79:t=>{"use strict";t.exports=CKEditor5.dll}},e={};function r(o){var n=e[o];if(void 0!==n)return n.exports;var i=e[o]={id:o,exports:{}};return t[o](i,i.exports,r),i.exports}r.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return r.d(e,{a:e}),e},r.d=(t,e)=>{for(var o in e)r.o(e,o)&&!r.o(t,o)&&Object.defineProperty(t,o,{enumerable:!0,get:e[o]})},r.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),r.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},r.nc=void 0;var o={};(()=>{"use strict";r.r(o),r.d(o,{DataFilter:()=>On,DataSchema:()=>hn,FullPage:()=>vi,GeneralHtmlSupport:()=>hi,HtmlComment:()=>pi,HtmlPageDataProcessor:()=>gi});var t=r(704),e=r(209),n=r(492),i=r(995);const s=function(){this.__data__=[],this.size=0};const l=function(t,e){return t===e||t!=t&&e!=e};const a=function(t,e){for(var r=t.length;r--;)if(l(t[r][0],e))return r;return-1};var c=Array.prototype.splice;const u=function(t){var e=this.__data__,r=a(e,t);return!(r<0)&&(r==e.length-1?e.pop():c.call(e,r,1),--this.size,!0)};const m=function(t){var e=this.__data__,r=a(e,t);return r<0?void 0:e[r][1]};const d=function(t){return a(this.__data__,t)>-1};const f=function(t,e){var r=this.__data__,o=a(r,t);return o<0?(++this.size,r.push([t,e])):r[o][1]=e,this};function h(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var o=t[e];this.set(o[0],o[1])}}h.prototype.clear=s,h.prototype.delete=u,h.prototype.get=m,h.prototype.has=d,h.prototype.set=f;const b=h;const p=function(){this.__data__=new b,this.size=0};const g=function(t){var e=this.__data__,r=e.delete(t);return this.size=e.size,r};const v=function(t){return this.__data__.get(t)};const w=function(t){return this.__data__.has(t)};const y="object"==typeof global&&global&&global.Object===Object&&global;var A="object"==typeof self&&self&&self.Object===Object&&self;const j=y||A||Function("return this")();const E=j.Symbol;var _=Object.prototype,S=_.hasOwnProperty,k=_.toString,O=E?E.toStringTag:void 0;const C=function(t){var e=S.call(t,O),r=t[O];try{t[O]=void 0;var o=!0}catch(t){}var n=k.call(t);return o&&(e?t[O]=r:delete t[O]),n};var x=Object.prototype.toString;const F=function(t){return x.call(t)};var I=E?E.toStringTag:void 0;const P=function(t){return null==t?void 0===t?"[object Undefined]":"[object Null]":I&&I in Object(t)?C(t):F(t)};const $=function(t){var e=typeof t;return null!=t&&("object"==e||"function"==e)};const T=function(t){if(!$(t))return!1;var e=P(t);return"[object Function]"==e||"[object GeneratorFunction]"==e||"[object AsyncFunction]"==e||"[object Proxy]"==e};const D=j["__core-js_shared__"];var B,R=(B=/[^.]+$/.exec(D&&D.keys&&D.keys.IE_PROTO||""))?"Symbol(src)_1."+B:"";const N=function(t){return!!R&&R in t};var M=Function.prototype.toString;const L=function(t){if(null!=t){try{return M.call(t)}catch(t){}try{return t+""}catch(t){}}return""};var V=/^\[object .+?Constructor\]$/,H=Function.prototype,z=Object.prototype,U=H.toString,W=z.hasOwnProperty,q=RegExp("^"+U.call(W).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");const G=function(t){return!(!$(t)||N(t))&&(T(t)?q:V).test(L(t))};const Z=function(t,e){return null==t?void 0:t[e]};const K=function(t,e){var r=Z(t,e);return G(r)?r:void 0};const Y=K(j,"Map");const J=K(Object,"create");const X=function(){this.__data__=J?J(null):{},this.size=0};const Q=function(t){var e=this.has(t)&&delete this.__data__[t];return this.size-=e?1:0,e};var tt=Object.prototype.hasOwnProperty;const et=function(t){var e=this.__data__;if(J){var r=e[t];return"__lodash_hash_undefined__"===r?void 0:r}return tt.call(e,t)?e[t]:void 0};var rt=Object.prototype.hasOwnProperty;const ot=function(t){var e=this.__data__;return J?void 0!==e[t]:rt.call(e,t)};const nt=function(t,e){var r=this.__data__;return this.size+=this.has(t)?0:1,r[t]=J&&void 0===e?"__lodash_hash_undefined__":e,this};function it(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var o=t[e];this.set(o[0],o[1])}}it.prototype.clear=X,it.prototype.delete=Q,it.prototype.get=et,it.prototype.has=ot,it.prototype.set=nt;const st=it;const lt=function(){this.size=0,this.__data__={hash:new st,map:new(Y||b),string:new st}};const at=function(t){var e=typeof t;return"string"==e||"number"==e||"symbol"==e||"boolean"==e?"__proto__"!==t:null===t};const ct=function(t,e){var r=t.__data__;return at(e)?r["string"==typeof e?"string":"hash"]:r.map};const ut=function(t){var e=ct(this,t).delete(t);return this.size-=e?1:0,e};const mt=function(t){return ct(this,t).get(t)};const dt=function(t){return ct(this,t).has(t)};const ft=function(t,e){var r=ct(this,t),o=r.size;return r.set(t,e),this.size+=r.size==o?0:1,this};function ht(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var o=t[e];this.set(o[0],o[1])}}ht.prototype.clear=lt,ht.prototype.delete=ut,ht.prototype.get=mt,ht.prototype.has=dt,ht.prototype.set=ft;const bt=ht;const pt=function(t,e){var r=this.__data__;if(r instanceof b){var o=r.__data__;if(!Y||o.length<199)return o.push([t,e]),this.size=++r.size,this;r=this.__data__=new bt(o)}return r.set(t,e),this.size=r.size,this};function gt(t){var e=this.__data__=new b(t);this.size=e.size}gt.prototype.clear=p,gt.prototype.delete=g,gt.prototype.get=v,gt.prototype.has=w,gt.prototype.set=pt;const vt=gt;const wt=function(t,e){for(var r=-1,o=null==t?0:t.length;++r<o&&!1!==e(t[r],r,t););return t};const yt=function(){try{var t=K(Object,"defineProperty");return t({},"",{}),t}catch(t){}}();const At=function(t,e,r){"__proto__"==e&&yt?yt(t,e,{configurable:!0,enumerable:!0,value:r,writable:!0}):t[e]=r};var jt=Object.prototype.hasOwnProperty;const Et=function(t,e,r){var o=t[e];jt.call(t,e)&&l(o,r)&&(void 0!==r||e in t)||At(t,e,r)};const _t=function(t,e,r,o){var n=!r;r||(r={});for(var i=-1,s=e.length;++i<s;){var l=e[i],a=o?o(r[l],t[l],l,r,t):void 0;void 0===a&&(a=t[l]),n?At(r,l,a):Et(r,l,a)}return r};const St=function(t,e){for(var r=-1,o=Array(t);++r<t;)o[r]=e(r);return o};const kt=function(t){return null!=t&&"object"==typeof t};const Ot=function(t){return kt(t)&&"[object Arguments]"==P(t)};var Ct=Object.prototype,xt=Ct.hasOwnProperty,Ft=Ct.propertyIsEnumerable;const It=Ot(function(){return arguments}())?Ot:function(t){return kt(t)&&xt.call(t,"callee")&&!Ft.call(t,"callee")};const Pt=Array.isArray;const $t=function(){return!1};var Tt="object"==typeof exports&&exports&&!exports.nodeType&&exports,Dt=Tt&&"object"==typeof module&&module&&!module.nodeType&&module,Bt=Dt&&Dt.exports===Tt?j.Buffer:void 0;const Rt=(Bt?Bt.isBuffer:void 0)||$t;var Nt=/^(?:0|[1-9]\d*)$/;const Mt=function(t,e){var r=typeof t;return!!(e=null==e?9007199254740991:e)&&("number"==r||"symbol"!=r&&Nt.test(t))&&t>-1&&t%1==0&&t<e};const Lt=function(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=9007199254740991};var Vt={};Vt["[object Float32Array]"]=Vt["[object Float64Array]"]=Vt["[object Int8Array]"]=Vt["[object Int16Array]"]=Vt["[object Int32Array]"]=Vt["[object Uint8Array]"]=Vt["[object Uint8ClampedArray]"]=Vt["[object Uint16Array]"]=Vt["[object Uint32Array]"]=!0,Vt["[object Arguments]"]=Vt["[object Array]"]=Vt["[object ArrayBuffer]"]=Vt["[object Boolean]"]=Vt["[object DataView]"]=Vt["[object Date]"]=Vt["[object Error]"]=Vt["[object Function]"]=Vt["[object Map]"]=Vt["[object Number]"]=Vt["[object Object]"]=Vt["[object RegExp]"]=Vt["[object Set]"]=Vt["[object String]"]=Vt["[object WeakMap]"]=!1;const Ht=function(t){return kt(t)&&Lt(t.length)&&!!Vt[P(t)]};const zt=function(t){return function(e){return t(e)}};var Ut="object"==typeof exports&&exports&&!exports.nodeType&&exports,Wt=Ut&&"object"==typeof module&&module&&!module.nodeType&&module,qt=Wt&&Wt.exports===Ut&&y.process;const Gt=function(){try{var t=Wt&&Wt.require&&Wt.require("util").types;return t||qt&&qt.binding&&qt.binding("util")}catch(t){}}();var Zt=Gt&&Gt.isTypedArray;const Kt=Zt?zt(Zt):Ht;var Yt=Object.prototype.hasOwnProperty;const Jt=function(t,e){var r=Pt(t),o=!r&&It(t),n=!r&&!o&&Rt(t),i=!r&&!o&&!n&&Kt(t),s=r||o||n||i,l=s?St(t.length,String):[],a=l.length;for(var c in t)!e&&!Yt.call(t,c)||s&&("length"==c||n&&("offset"==c||"parent"==c)||i&&("buffer"==c||"byteLength"==c||"byteOffset"==c)||Mt(c,a))||l.push(c);return l};var Xt=Object.prototype;const Qt=function(t){var e=t&&t.constructor;return t===("function"==typeof e&&e.prototype||Xt)};const te=function(t,e){return function(r){return t(e(r))}};const ee=te(Object.keys,Object);var re=Object.prototype.hasOwnProperty;const oe=function(t){if(!Qt(t))return ee(t);var e=[];for(var r in Object(t))re.call(t,r)&&"constructor"!=r&&e.push(r);return e};const ne=function(t){return null!=t&&Lt(t.length)&&!T(t)};const ie=function(t){return ne(t)?Jt(t):oe(t)};const se=function(t,e){return t&&_t(e,ie(e),t)};const le=function(t){var e=[];if(null!=t)for(var r in Object(t))e.push(r);return e};var ae=Object.prototype.hasOwnProperty;const ce=function(t){if(!$(t))return le(t);var e=Qt(t),r=[];for(var o in t)("constructor"!=o||!e&&ae.call(t,o))&&r.push(o);return r};const ue=function(t){return ne(t)?Jt(t,!0):ce(t)};const me=function(t,e){return t&&_t(e,ue(e),t)};var de="object"==typeof exports&&exports&&!exports.nodeType&&exports,fe=de&&"object"==typeof module&&module&&!module.nodeType&&module,he=fe&&fe.exports===de?j.Buffer:void 0,be=he?he.allocUnsafe:void 0;const pe=function(t,e){if(e)return t.slice();var r=t.length,o=be?be(r):new t.constructor(r);return t.copy(o),o};const ge=function(t,e){var r=-1,o=t.length;for(e||(e=Array(o));++r<o;)e[r]=t[r];return e};const ve=function(t,e){for(var r=-1,o=null==t?0:t.length,n=0,i=[];++r<o;){var s=t[r];e(s,r,t)&&(i[n++]=s)}return i};const we=function(){return[]};var ye=Object.prototype.propertyIsEnumerable,Ae=Object.getOwnPropertySymbols;const je=Ae?function(t){return null==t?[]:(t=Object(t),ve(Ae(t),(function(e){return ye.call(t,e)})))}:we;const Ee=function(t,e){return _t(t,je(t),e)};const _e=function(t,e){for(var r=-1,o=e.length,n=t.length;++r<o;)t[n+r]=e[r];return t};const Se=te(Object.getPrototypeOf,Object);const ke=Object.getOwnPropertySymbols?function(t){for(var e=[];t;)_e(e,je(t)),t=Se(t);return e}:we;const Oe=function(t,e){return _t(t,ke(t),e)};const Ce=function(t,e,r){var o=e(t);return Pt(t)?o:_e(o,r(t))};const xe=function(t){return Ce(t,ie,je)};const Fe=function(t){return Ce(t,ue,ke)};const Ie=K(j,"DataView");const Pe=K(j,"Promise");const $e=K(j,"Set");const Te=K(j,"WeakMap");var De="[object Map]",Be="[object Promise]",Re="[object Set]",Ne="[object WeakMap]",Me="[object DataView]",Le=L(Ie),Ve=L(Y),He=L(Pe),ze=L($e),Ue=L(Te),We=P;(Ie&&We(new Ie(new ArrayBuffer(1)))!=Me||Y&&We(new Y)!=De||Pe&&We(Pe.resolve())!=Be||$e&&We(new $e)!=Re||Te&&We(new Te)!=Ne)&&(We=function(t){var e=P(t),r="[object Object]"==e?t.constructor:void 0,o=r?L(r):"";if(o)switch(o){case Le:return Me;case Ve:return De;case He:return Be;case ze:return Re;case Ue:return Ne}return e});const qe=We;var Ge=Object.prototype.hasOwnProperty;const Ze=function(t){var e=t.length,r=new t.constructor(e);return e&&"string"==typeof t[0]&&Ge.call(t,"index")&&(r.index=t.index,r.input=t.input),r};const Ke=j.Uint8Array;const Ye=function(t){var e=new t.constructor(t.byteLength);return new Ke(e).set(new Ke(t)),e};const Je=function(t,e){var r=e?Ye(t.buffer):t.buffer;return new t.constructor(r,t.byteOffset,t.byteLength)};var Xe=/\w*$/;const Qe=function(t){var e=new t.constructor(t.source,Xe.exec(t));return e.lastIndex=t.lastIndex,e};var tr=E?E.prototype:void 0,er=tr?tr.valueOf:void 0;const rr=function(t){return er?Object(er.call(t)):{}};const or=function(t,e){var r=e?Ye(t.buffer):t.buffer;return new t.constructor(r,t.byteOffset,t.length)};const nr=function(t,e,r){var o=t.constructor;switch(e){case"[object ArrayBuffer]":return Ye(t);case"[object Boolean]":case"[object Date]":return new o(+t);case"[object DataView]":return Je(t,r);case"[object Float32Array]":case"[object Float64Array]":case"[object Int8Array]":case"[object Int16Array]":case"[object Int32Array]":case"[object Uint8Array]":case"[object Uint8ClampedArray]":case"[object Uint16Array]":case"[object Uint32Array]":return or(t,r);case"[object Map]":case"[object Set]":return new o;case"[object Number]":case"[object String]":return new o(t);case"[object RegExp]":return Qe(t);case"[object Symbol]":return rr(t)}};var ir=Object.create;const sr=function(){function t(){}return function(e){if(!$(e))return{};if(ir)return ir(e);t.prototype=e;var r=new t;return t.prototype=void 0,r}}();const lr=function(t){return"function"!=typeof t.constructor||Qt(t)?{}:sr(Se(t))};const ar=function(t){return kt(t)&&"[object Map]"==qe(t)};var cr=Gt&&Gt.isMap;const ur=cr?zt(cr):ar;const mr=function(t){return kt(t)&&"[object Set]"==qe(t)};var dr=Gt&&Gt.isSet;const fr=dr?zt(dr):mr;var hr="[object Arguments]",br="[object Function]",pr="[object Object]",gr={};gr[hr]=gr["[object Array]"]=gr["[object ArrayBuffer]"]=gr["[object DataView]"]=gr["[object Boolean]"]=gr["[object Date]"]=gr["[object Float32Array]"]=gr["[object Float64Array]"]=gr["[object Int8Array]"]=gr["[object Int16Array]"]=gr["[object Int32Array]"]=gr["[object Map]"]=gr["[object Number]"]=gr[pr]=gr["[object RegExp]"]=gr["[object Set]"]=gr["[object String]"]=gr["[object Symbol]"]=gr["[object Uint8Array]"]=gr["[object Uint8ClampedArray]"]=gr["[object Uint16Array]"]=gr["[object Uint32Array]"]=!0,gr["[object Error]"]=gr[br]=gr["[object WeakMap]"]=!1;const vr=function t(e,r,o,n,i,s){var l,a=1&r,c=2&r,u=4&r;if(o&&(l=i?o(e,n,i,s):o(e)),void 0!==l)return l;if(!$(e))return e;var m=Pt(e);if(m){if(l=Ze(e),!a)return ge(e,l)}else{var d=qe(e),f=d==br||"[object GeneratorFunction]"==d;if(Rt(e))return pe(e,a);if(d==pr||d==hr||f&&!i){if(l=c||f?{}:lr(e),!a)return c?Oe(e,me(l,e)):Ee(e,se(l,e))}else{if(!gr[d])return i?e:{};l=nr(e,d,a)}}s||(s=new vt);var h=s.get(e);if(h)return h;s.set(e,l),fr(e)?e.forEach((function(n){l.add(t(n,r,o,n,e,s))})):ur(e)&&e.forEach((function(n,i){l.set(i,t(n,r,o,i,e,s))}));var b=m?void 0:(u?c?Fe:xe:c?ue:ie)(e);return wt(b||e,(function(n,i){b&&(n=e[i=n]),Et(l,i,t(n,r,o,i,e,s))})),l};const wr=function(t){return vr(t,5)};const yr=function(t,e,r,o){var n=-1,i=null==t?0:t.length;for(o&&i&&(r=t[++n]);++n<i;)r=e(r,t[n],n,t);return r};const Ar=function(t){return function(e){return null==t?void 0:t[e]}}({À:"A",Á:"A",Â:"A",Ã:"A",Ä:"A",Å:"A",à:"a",á:"a",â:"a",ã:"a",ä:"a",å:"a",Ç:"C",ç:"c",Ð:"D",ð:"d",È:"E",É:"E",Ê:"E",Ë:"E",è:"e",é:"e",ê:"e",ë:"e",Ì:"I",Í:"I",Î:"I",Ï:"I",ì:"i",í:"i",î:"i",ï:"i",Ñ:"N",ñ:"n",Ò:"O",Ó:"O",Ô:"O",Õ:"O",Ö:"O",Ø:"O",ò:"o",ó:"o",ô:"o",õ:"o",ö:"o",ø:"o",Ù:"U",Ú:"U",Û:"U",Ü:"U",ù:"u",ú:"u",û:"u",ü:"u",Ý:"Y",ý:"y",ÿ:"y",Æ:"Ae",æ:"ae",Þ:"Th",þ:"th",ß:"ss",Ā:"A",Ă:"A",Ą:"A",ā:"a",ă:"a",ą:"a",Ć:"C",Ĉ:"C",Ċ:"C",Č:"C",ć:"c",ĉ:"c",ċ:"c",č:"c",Ď:"D",Đ:"D",ď:"d",đ:"d",Ē:"E",Ĕ:"E",Ė:"E",Ę:"E",Ě:"E",ē:"e",ĕ:"e",ė:"e",ę:"e",ě:"e",Ĝ:"G",Ğ:"G",Ġ:"G",Ģ:"G",ĝ:"g",ğ:"g",ġ:"g",ģ:"g",Ĥ:"H",Ħ:"H",ĥ:"h",ħ:"h",Ĩ:"I",Ī:"I",Ĭ:"I",Į:"I",İ:"I",ĩ:"i",ī:"i",ĭ:"i",į:"i",ı:"i",Ĵ:"J",ĵ:"j",Ķ:"K",ķ:"k",ĸ:"k",Ĺ:"L",Ļ:"L",Ľ:"L",Ŀ:"L",Ł:"L",ĺ:"l",ļ:"l",ľ:"l",ŀ:"l",ł:"l",Ń:"N",Ņ:"N",Ň:"N",Ŋ:"N",ń:"n",ņ:"n",ň:"n",ŋ:"n",Ō:"O",Ŏ:"O",Ő:"O",ō:"o",ŏ:"o",ő:"o",Ŕ:"R",Ŗ:"R",Ř:"R",ŕ:"r",ŗ:"r",ř:"r",Ś:"S",Ŝ:"S",Ş:"S",Š:"S",ś:"s",ŝ:"s",ş:"s",š:"s",Ţ:"T",Ť:"T",Ŧ:"T",ţ:"t",ť:"t",ŧ:"t",Ũ:"U",Ū:"U",Ŭ:"U",Ů:"U",Ű:"U",Ų:"U",ũ:"u",ū:"u",ŭ:"u",ů:"u",ű:"u",ų:"u",Ŵ:"W",ŵ:"w",Ŷ:"Y",ŷ:"y",Ÿ:"Y",Ź:"Z",Ż:"Z",Ž:"Z",ź:"z",ż:"z",ž:"z",Ĳ:"IJ",ĳ:"ij",Œ:"Oe",œ:"oe",ŉ:"'n",ſ:"s"});const jr=function(t,e){for(var r=-1,o=null==t?0:t.length,n=Array(o);++r<o;)n[r]=e(t[r],r,t);return n};const Er=function(t){return"symbol"==typeof t||kt(t)&&"[object Symbol]"==P(t)};var _r=E?E.prototype:void 0,Sr=_r?_r.toString:void 0;const kr=function t(e){if("string"==typeof e)return e;if(Pt(e))return jr(e,t)+"";if(Er(e))return Sr?Sr.call(e):"";var r=e+"";return"0"==r&&1/e==-Infinity?"-0":r};const Or=function(t){return null==t?"":kr(t)};var Cr=/[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g,xr=RegExp("[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]","g");const Fr=function(t){return(t=Or(t))&&t.replace(Cr,Ar).replace(xr,"")};var Ir=/[^\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\x7f]+/g;const Pr=function(t){return t.match(Ir)||[]};var $r=/[a-z][A-Z]|[A-Z]{2}[a-z]|[0-9][a-zA-Z]|[a-zA-Z][0-9]|[^a-zA-Z0-9 ]/;const Tr=function(t){return $r.test(t)};var Dr="\\ud800-\\udfff",Br="\\u2700-\\u27bf",Rr="a-z\\xdf-\\xf6\\xf8-\\xff",Nr="A-Z\\xc0-\\xd6\\xd8-\\xde",Mr="\\xac\\xb1\\xd7\\xf7\\x00-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\xbf\\u2000-\\u206f \\t\\x0b\\f\\xa0\\ufeff\\n\\r\\u2028\\u2029\\u1680\\u180e\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200a\\u202f\\u205f\\u3000",Lr="["+Mr+"]",Vr="\\d+",Hr="["+Br+"]",zr="["+Rr+"]",Ur="[^"+Dr+Mr+Vr+Br+Rr+Nr+"]",Wr="(?:\\ud83c[\\udde6-\\uddff]){2}",qr="[\\ud800-\\udbff][\\udc00-\\udfff]",Gr="["+Nr+"]",Zr="(?:"+zr+"|"+Ur+")",Kr="(?:"+Gr+"|"+Ur+")",Yr="(?:['’](?:d|ll|m|re|s|t|ve))?",Jr="(?:['’](?:D|LL|M|RE|S|T|VE))?",Xr="(?:[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]|\\ud83c[\\udffb-\\udfff])?",Qr="[\\ufe0e\\ufe0f]?",to=Qr+Xr+("(?:\\u200d(?:"+["[^"+Dr+"]",Wr,qr].join("|")+")"+Qr+Xr+")*"),eo="(?:"+[Hr,Wr,qr].join("|")+")"+to,ro=RegExp([Gr+"?"+zr+"+"+Yr+"(?="+[Lr,Gr,"$"].join("|")+")",Kr+"+"+Jr+"(?="+[Lr,Gr+Zr,"$"].join("|")+")",Gr+"?"+Zr+"+"+Yr,Gr+"+"+Jr,"\\d*(?:1ST|2ND|3RD|(?![123])\\dTH)(?=\\b|[a-z_])","\\d*(?:1st|2nd|3rd|(?![123])\\dth)(?=\\b|[A-Z_])",Vr,eo].join("|"),"g");const oo=function(t){return t.match(ro)||[]};const no=function(t,e,r){return t=Or(t),void 0===(e=r?void 0:e)?Tr(t)?oo(t):Pr(t):t.match(e)||[]};var io=RegExp("['’]","g");const so=function(t){return function(e){return yr(no(Fr(e).replace(io,"")),t,"")}};const lo=function(t,e,r){var o=-1,n=t.length;e<0&&(e=-e>n?0:n+e),(r=r>n?n:r)<0&&(r+=n),n=e>r?0:r-e>>>0,e>>>=0;for(var i=Array(n);++o<n;)i[o]=t[o+e];return i};const ao=function(t,e,r){var o=t.length;return r=void 0===r?o:r,!e&&r>=o?t:lo(t,e,r)};var co=RegExp("[\\u200d\\ud800-\\udfff\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff\\ufe0e\\ufe0f]");const uo=function(t){return co.test(t)};const mo=function(t){return t.split("")};var fo="\\ud800-\\udfff",ho="["+fo+"]",bo="[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]",po="\\ud83c[\\udffb-\\udfff]",go="[^"+fo+"]",vo="(?:\\ud83c[\\udde6-\\uddff]){2}",wo="[\\ud800-\\udbff][\\udc00-\\udfff]",yo="(?:"+bo+"|"+po+")"+"?",Ao="[\\ufe0e\\ufe0f]?",jo=Ao+yo+("(?:\\u200d(?:"+[go,vo,wo].join("|")+")"+Ao+yo+")*"),Eo="(?:"+[go+bo+"?",bo,vo,wo,ho].join("|")+")",_o=RegExp(po+"(?="+po+")|"+Eo+jo,"g");const So=function(t){return t.match(_o)||[]};const ko=function(t){return uo(t)?So(t):mo(t)};const Oo=function(t){return function(e){e=Or(e);var r=uo(e)?ko(e):void 0,o=r?r[0]:e.charAt(0),n=r?ao(r,1).join(""):e.slice(1);return o[t]()+n}}("toUpperCase");const Co=so((function(t,e,r){return t+(r?" ":"")+Oo(e)}));function xo(t,e,r,o){e&&function(t,e,r){if(e.attributes)for(const[o]of Object.entries(e.attributes))t.removeAttribute(o,r);if(e.styles)for(const o of Object.keys(e.styles))t.removeStyle(o,r);e.classes&&t.removeClass(e.classes,r)}(t,e,o),r&&Fo(t,r,o)}function Fo(t,e,r){if(e.attributes)for(const[o,n]of Object.entries(e.attributes))t.setAttribute(o,n,r);e.styles&&t.setStyle(e.styles,r),e.classes&&t.addClass(e.classes,r)}function Io(t,e,r,o,n){const i=e.getAttribute(r),s={};for(const t of["attributes","styles","classes"]){if(t!=o){i&&i[t]&&(s[t]=i[t]);continue}if("classes"==o){const e=new Set(i&&i.classes||[]);n(e),e.size&&(s[t]=Array.from(e));continue}const e=new Map(Object.entries(i&&i[t]||{}));n(e),e.size&&(s[t]=Object.fromEntries(e))}Object.keys(s).length?e.is("documentSelection")?t.setSelectionAttribute(r,s):t.setAttribute(r,s,e):i&&(e.is("documentSelection")?t.removeSelectionAttribute(r):t.removeAttribute(r,e))}function Po(t){return`html${e=t,Co(e).replace(/ /g,"")}Attributes`;var e}function $o({model:t}){return(e,r)=>r.writer.createElement(t,{htmlContent:e.getCustomProperty("$rawContent")})}function To(t,{view:e,isInline:r}){const o=t.t;return(t,{writer:n})=>{const s=o("HTML object"),l=Do(e,t,n),a=t.getAttribute(Po(e));n.addClass("html-object-embed__content",l),a&&Fo(n,a,l);const c=n.createContainerElement(r?"span":"div",{class:"html-object-embed","data-html-object-embed-label":s},l);return(0,i.toWidget)(c,n,{label:s})}}function Do(t,e,r){return r.createRawElement(t,null,((t,r)=>{r.setContentOf(t,e.getAttribute("htmlContent"))}))}function Bo({view:t,model:e,allowEmpty:r},o){return e=>{e.on(`element:${t}`,((t,e,i)=>{let s=o.processViewAttributes(e.viewItem,i);if(s||i.consumable.test(e.viewItem,{name:!0})){if(s=s||{},i.consumable.consume(e.viewItem,{name:!0}),e.modelRange||(e=Object.assign(e,i.convertChildren(e.viewItem,e.modelCursor))),r&&e.modelRange.isCollapsed&&Object.keys(s).length){const t=i.writer.createElement("htmlEmptyElement");if(!i.safeInsert(t,e.modelCursor))return;const r=i.getSplitParts(t);return e.modelRange=i.writer.createRange(e.modelRange.start,i.writer.createPositionAfter(r[r.length-1])),i.updateConversionResult(t,e),void n(t,s,i)}for(const t of e.modelRange.getItems())n(t,s,i)}}),{priority:"low"})};function n(t,r,o){if(o.schema.checkAttribute(t,e)){const n=function(t,e){const r=wr(t);let o="attributes";for(o in e)r[o]="classes"==o?Array.from(new Set([...t[o]||[],...e[o]])):{...t[o],...e[o]};return r}(r,t.getAttribute(e)||{});o.writer.setAttribute(e,n,t)}}}function Ro({model:t,view:e},r){return(o,{writer:n,consumable:s})=>{if(!o.hasAttribute(t))return null;const l=n.createContainerElement(e),a=o.getAttribute(t);return s.consume(o,`attribute:${t}`),Fo(n,a,l),l.getFillerOffset=()=>null,r?(0,i.toWidget)(l,n):l}}function No({priority:t,view:e}){return(r,o)=>{if(!r)return;const{writer:n}=o,i=n.createAttributeElement(e,null,{priority:t});return Fo(n,r,i),i}}function Mo({view:t},e){return r=>{r.on(`element:${t}`,((t,r,o)=>{if(!r.modelRange||r.modelRange.isCollapsed)return;const n=e.processViewAttributes(r.viewItem,o);n&&o.writer.setAttribute(Po(r.viewItem.name),n,r.modelRange)}),{priority:"low"})}}function Lo({view:t,model:e}){return r=>{r.on(`attribute:${Po(t)}:${e}`,((t,e,r)=>{if(!r.consumable.consume(e.item,t.name))return;const{attributeOldValue:o,attributeNewValue:n}=e;xo(r.writer,o,n,r.mapper.toViewElement(e.item))}))}}const Vo=[{model:"codeBlock",view:"pre"},{model:"paragraph",view:"p"},{model:"blockQuote",view:"blockquote"},{model:"listItem",view:"li"},{model:"pageBreak",view:"div"},{model:"rawHtml",view:"div"},{model:"table",view:"table"},{model:"tableRow",view:"tr"},{model:"tableCell",view:"td"},{model:"tableCell",view:"th"},{model:"tableColumnGroup",view:"colgroup"},{model:"tableColumn",view:"col"},{model:"caption",view:"caption"},{model:"caption",view:"figcaption"},{model:"imageBlock",view:"img"},{model:"imageInline",view:"img"},{model:"htmlP",view:"p",modelSchema:{inheritAllFrom:"$block"}},{model:"htmlBlockquote",view:"blockquote",modelSchema:{inheritAllFrom:"$container"}},{model:"htmlTable",view:"table",modelSchema:{allowWhere:"$block",isBlock:!0}},{model:"htmlTbody",view:"tbody",modelSchema:{allowIn:"htmlTable",isBlock:!1}},{model:"htmlThead",view:"thead",modelSchema:{allowIn:"htmlTable",isBlock:!1}},{model:"htmlTfoot",view:"tfoot",modelSchema:{allowIn:"htmlTable",isBlock:!1}},{model:"htmlCaption",view:"caption",modelSchema:{allowIn:"htmlTable",allowChildren:"$text",isBlock:!1}},{model:"htmlColgroup",view:"colgroup",modelSchema:{allowIn:"htmlTable",allowChildren:"col",isBlock:!1}},{model:"htmlCol",view:"col",modelSchema:{allowIn:"htmlColgroup",isBlock:!1}},{model:"htmlTr",view:"tr",modelSchema:{allowIn:["htmlTable","htmlThead","htmlTbody"],isLimit:!0}},{model:"htmlTd",view:"td",modelSchema:{allowIn:"htmlTr",allowContentOf:"$container",isLimit:!0,isBlock:!1}},{model:"htmlTh",view:"th",modelSchema:{allowIn:"htmlTr",allowContentOf:"$container",isLimit:!0,isBlock:!1}},{model:"htmlFigure",view:"figure",modelSchema:{inheritAllFrom:"$container",isBlock:!1}},{model:"htmlFigcaption",view:"figcaption",modelSchema:{allowIn:"htmlFigure",allowChildren:"$text",isBlock:!1}},{model:"htmlAddress",view:"address",modelSchema:{inheritAllFrom:"$container",isBlock:!1}},{model:"htmlAside",view:"aside",modelSchema:{inheritAllFrom:"$container",isBlock:!1}},{model:"htmlMain",view:"main",modelSchema:{inheritAllFrom:"$container",isBlock:!1}},{model:"htmlDetails",view:"details",modelSchema:{inheritAllFrom:"$container",isBlock:!1}},{model:"htmlSummary",view:"summary",modelSchema:{allowChildren:"$text",allowIn:"htmlDetails",isBlock:!1}},{model:"htmlDiv",view:"div",paragraphLikeModel:"htmlDivParagraph",modelSchema:{inheritAllFrom:"$container"}},{model:"htmlFieldset",view:"fieldset",modelSchema:{inheritAllFrom:"$container",isBlock:!1}},{model:"htmlLegend",view:"legend",modelSchema:{allowIn:"htmlFieldset",allowChildren:"$text"}},{model:"htmlHeader",view:"header",modelSchema:{inheritAllFrom:"$container",isBlock:!1}},{model:"htmlFooter",view:"footer",modelSchema:{inheritAllFrom:"$container",isBlock:!1}},{model:"htmlForm",view:"form",modelSchema:{inheritAllFrom:"$container",isBlock:!0}},{model:"htmlHgroup",view:"hgroup",modelSchema:{allowChildren:["htmlH1","htmlH2","htmlH3","htmlH4","htmlH5","htmlH6"],isBlock:!1}},{model:"htmlH1",view:"h1",modelSchema:{inheritAllFrom:"$block"}},{model:"htmlH2",view:"h2",modelSchema:{inheritAllFrom:"$block"}},{model:"htmlH3",view:"h3",modelSchema:{inheritAllFrom:"$block"}},{model:"htmlH4",view:"h4",modelSchema:{inheritAllFrom:"$block"}},{model:"htmlH5",view:"h5",modelSchema:{inheritAllFrom:"$block"}},{model:"htmlH6",view:"h6",modelSchema:{inheritAllFrom:"$block"}},{model:"$htmlList",modelSchema:{allowWhere:"$container",allowChildren:["$htmlList","htmlLi"],isBlock:!1}},{model:"htmlDir",view:"dir",modelSchema:{inheritAllFrom:"$htmlList"}},{model:"htmlMenu",view:"menu",modelSchema:{inheritAllFrom:"$htmlList"}},{model:"htmlUl",view:"ul",modelSchema:{inheritAllFrom:"$htmlList"}},{model:"htmlOl",view:"ol",modelSchema:{inheritAllFrom:"$htmlList"}},{model:"htmlLi",view:"li",modelSchema:{allowIn:"$htmlList",allowChildren:"$text",isBlock:!1}},{model:"htmlPre",view:"pre",modelSchema:{inheritAllFrom:"$block"}},{model:"htmlArticle",view:"article",modelSchema:{inheritAllFrom:"$container",isBlock:!1}},{model:"htmlSection",view:"section",modelSchema:{inheritAllFrom:"$container",isBlock:!1}},{model:"htmlNav",view:"nav",modelSchema:{inheritAllFrom:"$container",isBlock:!1}},{model:"htmlDivDl",view:"div",modelSchema:{allowChildren:["htmlDt","htmlDd"],allowIn:"htmlDl"}},{model:"htmlDl",view:"dl",modelSchema:{allowWhere:"$container",allowChildren:["htmlDt","htmlDd","htmlDivDl"],isBlock:!1}},{model:"htmlDt",view:"dt",modelSchema:{allowChildren:"$block",isBlock:!1}},{model:"htmlDd",view:"dd",modelSchema:{allowChildren:"$block",isBlock:!1}},{model:"htmlCenter",view:"center",modelSchema:{inheritAllFrom:"$container",isBlock:!1}}],Ho=[{model:"htmlLiAttributes",view:"li",appliesToBlock:!0,coupledAttribute:"listItemId"},{model:"htmlOlAttributes",view:"ol",appliesToBlock:!0,coupledAttribute:"listItemId"},{model:"htmlUlAttributes",view:"ul",appliesToBlock:!0,coupledAttribute:"listItemId"},{model:"htmlFigureAttributes",view:"figure",appliesToBlock:"table"},{model:"htmlTheadAttributes",view:"thead",appliesToBlock:"table"},{model:"htmlTbodyAttributes",view:"tbody",appliesToBlock:"table"},{model:"htmlFigureAttributes",view:"figure",appliesToBlock:"imageBlock"},{model:"htmlAcronym",view:"acronym",attributeProperties:{copyOnEnter:!0,isFormatting:!0}},{model:"htmlTt",view:"tt",attributeProperties:{copyOnEnter:!0,isFormatting:!0}},{model:"htmlFont",view:"font",attributeProperties:{copyOnEnter:!0,isFormatting:!0}},{model:"htmlTime",view:"time",attributeProperties:{copyOnEnter:!0,isFormatting:!0}},{model:"htmlVar",view:"var",attributeProperties:{copyOnEnter:!0,isFormatting:!0}},{model:"htmlBig",view:"big",attributeProperties:{copyOnEnter:!0,isFormatting:!0}},{model:"htmlSmall",view:"small",attributeProperties:{copyOnEnter:!0,isFormatting:!0}},{model:"htmlSamp",view:"samp",attributeProperties:{copyOnEnter:!0,isFormatting:!0}},{model:"htmlQ",view:"q",attributeProperties:{copyOnEnter:!0,isFormatting:!0}},{model:"htmlOutput",view:"output",attributeProperties:{copyOnEnter:!0,isFormatting:!0}},{model:"htmlKbd",view:"kbd",attributeProperties:{copyOnEnter:!0,isFormatting:!0}},{model:"htmlBdi",view:"bdi",attributeProperties:{copyOnEnter:!0,isFormatting:!0}},{model:"htmlBdo",view:"bdo",attributeProperties:{copyOnEnter:!0,isFormatting:!0}},{model:"htmlAbbr",view:"abbr",attributeProperties:{copyOnEnter:!0,isFormatting:!0}},{model:"htmlA",view:"a",priority:5,coupledAttribute:"linkHref"},{model:"htmlStrong",view:"strong",coupledAttribute:"bold",attributeProperties:{copyOnEnter:!0,isFormatting:!0}},{model:"htmlB",view:"b",coupledAttribute:"bold",attributeProperties:{copyOnEnter:!0,isFormatting:!0}},{model:"htmlI",view:"i",coupledAttribute:"italic",attributeProperties:{copyOnEnter:!0,isFormatting:!0}},{model:"htmlEm",view:"em",coupledAttribute:"italic",attributeProperties:{copyOnEnter:!0,isFormatting:!0}},{model:"htmlS",view:"s",coupledAttribute:"strikethrough",attributeProperties:{copyOnEnter:!0,isFormatting:!0}},{model:"htmlDel",view:"del",coupledAttribute:"strikethrough",attributeProperties:{copyOnEnter:!0,isFormatting:!0}},{model:"htmlIns",view:"ins",attributeProperties:{copyOnEnter:!0,isFormatting:!0}},{model:"htmlU",view:"u",coupledAttribute:"underline",attributeProperties:{copyOnEnter:!0,isFormatting:!0}},{model:"htmlSub",view:"sub",coupledAttribute:"subscript",attributeProperties:{copyOnEnter:!0,isFormatting:!0}},{model:"htmlSup",view:"sup",coupledAttribute:"superscript",attributeProperties:{copyOnEnter:!0,isFormatting:!0}},{model:"htmlCode",view:"code",coupledAttribute:"code",attributeProperties:{copyOnEnter:!0,isFormatting:!0}},{model:"htmlMark",view:"mark",attributeProperties:{copyOnEnter:!0,isFormatting:!0}},{model:"htmlSpan",view:"span",attributeProperties:{copyOnEnter:!0,isFormatting:!0}},{model:"htmlCite",view:"cite",attributeProperties:{copyOnEnter:!0,isFormatting:!0}},{model:"htmlLabel",view:"label",attributeProperties:{copyOnEnter:!0,isFormatting:!0}},{model:"htmlDfn",view:"dfn",attributeProperties:{copyOnEnter:!0,isFormatting:!0}},{model:"htmlObject",view:"object",isObject:!0,modelSchema:{inheritAllFrom:"$inlineObject"}},{model:"htmlIframe",view:"iframe",isObject:!0,modelSchema:{inheritAllFrom:"$inlineObject"}},{model:"htmlInput",view:"input",isObject:!0,modelSchema:{inheritAllFrom:"$inlineObject"}},{model:"htmlButton",view:"button",isObject:!0,modelSchema:{inheritAllFrom:"$inlineObject"}},{model:"htmlTextarea",view:"textarea",isObject:!0,modelSchema:{inheritAllFrom:"$inlineObject"}},{model:"htmlSelect",view:"select",isObject:!0,modelSchema:{inheritAllFrom:"$inlineObject"}},{model:"htmlVideo",view:"video",isObject:!0,modelSchema:{inheritAllFrom:"$inlineObject"}},{model:"htmlEmbed",view:"embed",isObject:!0,modelSchema:{inheritAllFrom:"$inlineObject"}},{model:"htmlOembed",view:"oembed",isObject:!0,modelSchema:{inheritAllFrom:"$inlineObject"}},{model:"htmlAudio",view:"audio",isObject:!0,modelSchema:{inheritAllFrom:"$inlineObject"}},{model:"htmlImg",view:"img",isObject:!0,modelSchema:{inheritAllFrom:"$inlineObject"}},{model:"htmlCanvas",view:"canvas",isObject:!0,modelSchema:{inheritAllFrom:"$inlineObject"}},{model:"htmlMeter",view:"meter",isObject:!0,modelSchema:{inheritAllFrom:"$inlineObject"}},{model:"htmlProgress",view:"progress",isObject:!0,modelSchema:{inheritAllFrom:"$inlineObject"}},{model:"htmlScript",view:"script",modelSchema:{allowWhere:["$text","$block"],isInline:!0}},{model:"htmlStyle",view:"style",modelSchema:{allowWhere:["$text","$block"],isInline:!0}},{model:"htmlCustomElement",view:"$customElement",modelSchema:{allowWhere:["$text","$block"],allowAttributesOf:"$inlineObject",isInline:!0}}];const zo=function(t,e,r){(void 0!==r&&!l(t[e],r)||void 0===r&&!(e in t))&&At(t,e,r)};const Uo=function(t){return function(e,r,o){for(var n=-1,i=Object(e),s=o(e),l=s.length;l--;){var a=s[t?l:++n];if(!1===r(i[a],a,i))break}return e}}();const Wo=function(t){return kt(t)&&ne(t)};var qo=Function.prototype,Go=Object.prototype,Zo=qo.toString,Ko=Go.hasOwnProperty,Yo=Zo.call(Object);const Jo=function(t){if(!kt(t)||"[object Object]"!=P(t))return!1;var e=Se(t);if(null===e)return!0;var r=Ko.call(e,"constructor")&&e.constructor;return"function"==typeof r&&r instanceof r&&Zo.call(r)==Yo};const Xo=function(t,e){if(("constructor"!==e||"function"!=typeof t[e])&&"__proto__"!=e)return t[e]};const Qo=function(t){return _t(t,ue(t))};const tn=function(t,e,r,o,n,i,s){var l=Xo(t,r),a=Xo(e,r),c=s.get(a);if(c)zo(t,r,c);else{var u=i?i(l,a,r+"",t,e,s):void 0,m=void 0===u;if(m){var d=Pt(a),f=!d&&Rt(a),h=!d&&!f&&Kt(a);u=a,d||f||h?Pt(l)?u=l:Wo(l)?u=ge(l):f?(m=!1,u=pe(a,!0)):h?(m=!1,u=or(a,!0)):u=[]:Jo(a)||It(a)?(u=l,It(l)?u=Qo(l):$(l)&&!T(l)||(u=lr(a))):m=!1}m&&(s.set(a,u),n(u,a,o,i,s),s.delete(a)),zo(t,r,u)}};const en=function t(e,r,o,n,i){e!==r&&Uo(r,(function(s,l){if(i||(i=new vt),$(s))tn(e,r,l,o,t,n,i);else{var a=n?n(Xo(e,l),s,l+"",e,r,i):void 0;void 0===a&&(a=s),zo(e,l,a)}}),ue)};const rn=function(t){return t};const on=function(t,e,r){switch(r.length){case 0:return t.call(e);case 1:return t.call(e,r[0]);case 2:return t.call(e,r[0],r[1]);case 3:return t.call(e,r[0],r[1],r[2])}return t.apply(e,r)};var nn=Math.max;const sn=function(t,e,r){return e=nn(void 0===e?t.length-1:e,0),function(){for(var o=arguments,n=-1,i=nn(o.length-e,0),s=Array(i);++n<i;)s[n]=o[e+n];n=-1;for(var l=Array(e+1);++n<e;)l[n]=o[n];return l[e]=r(s),on(t,this,l)}};const ln=function(t){return function(){return t}};const an=yt?function(t,e){return yt(t,"toString",{configurable:!0,enumerable:!1,value:ln(e),writable:!0})}:rn;var cn=Date.now;const un=function(t){var e=0,r=0;return function(){var o=cn(),n=16-(o-r);if(r=o,n>0){if(++e>=800)return arguments[0]}else e=0;return t.apply(void 0,arguments)}}(an);const mn=function(t,e){return un(sn(t,e,rn),t+"")};const dn=function(t,e,r){if(!$(r))return!1;var o=typeof e;return!!("number"==o?ne(r)&&Mt(e,r.length):"string"==o&&e in r)&&l(r[e],t)};const fn=function(t){return mn((function(e,r){var o=-1,n=r.length,i=n>1?r[n-1]:void 0,s=n>2?r[2]:void 0;for(i=t.length>3&&"function"==typeof i?(n--,i):void 0,s&&dn(r[0],r[1],s)&&(i=n<3?void 0:i,n=1),e=Object(e);++o<n;){var l=r[o];l&&t(e,l,o,i)}return e}))}((function(t,e,r,o){en(t,e,r,o)}));class hn extends t.Plugin{constructor(){super(...arguments),this._definitions=[]}static get pluginName(){return"DataSchema"}init(){for(const t of Vo)this.registerBlockElement(t);for(const t of Ho)this.registerInlineElement(t)}registerBlockElement(t){this._definitions.push({...t,isBlock:!0})}registerInlineElement(t){this._definitions.push({...t,isInline:!0})}extendBlockElement(t){this._extendDefinition({...t,isBlock:!0})}extendInlineElement(t){this._extendDefinition({...t,isInline:!0})}getDefinitionsForView(t,e=!1){const r=new Set;for(const o of this._getMatchingViewDefinitions(t)){if(e)for(const t of this._getReferences(o.model))r.add(t);r.add(o)}return r}getDefinitionsForModel(t){return this._definitions.filter((e=>e.model==t))}_getMatchingViewDefinitions(t){return this._definitions.filter((e=>e.view&&function(t,e){if("string"==typeof t)return t===e;if(t instanceof RegExp)return t.test(e);return!1}(t,e.view)))}*_getReferences(t){const r=["inheritAllFrom","inheritTypesFrom","allowWhere","allowContentOf","allowAttributesOf"],o=this._definitions.filter((e=>e.model==t));for(const{modelSchema:n}of o)if(n)for(const o of r)for(const r of(0,e.toArray)(n[o]||[])){const e=this._definitions.filter((t=>t.model==r));for(const o of e)r!==t&&(yield*this._getReferences(o.model),yield o)}}_extendDefinition(t){const e=Array.from(this._definitions.entries()).filter((([,e])=>e.model==t.model));if(0!=e.length)for(const[r,o]of e)this._definitions[r]=fn({},o,t,((t,e)=>Array.isArray(t)?t.concat(e):void 0));else this._definitions.push(t)}}const bn=function(t,e,r,o){for(var n=t.length,i=r+(o?1:-1);o?i--:++i<n;)if(e(t[i],i,t))return i;return-1};const pn=function(t){return t!=t};const gn=function(t,e,r){for(var o=r-1,n=t.length;++o<n;)if(t[o]===e)return o;return-1};const vn=function(t,e,r){return e==e?gn(t,e,r):bn(t,pn,r)};const wn=function(t,e,r,o){for(var n=r-1,i=t.length;++n<i;)if(o(t[n],e))return n;return-1};var yn=Array.prototype.splice;const An=function(t,e,r,o){var n=o?wn:vn,i=-1,s=e.length,l=t;for(t===e&&(e=ge(e)),r&&(l=jr(t,zt(r)));++i<s;)for(var a=0,c=e[i],u=r?r(c):c;(a=n(l,u,a,o))>-1;)l!==t&&yn.call(l,a,1),yn.call(t,a,1);return t};const jn=mn((function(t,e){return t&&t.length&&e&&e.length?An(t,e):t}));var En=r(62),_n=r.n(En),Sn=r(90),kn={injectType:"singletonStyleTag",attributes:{"data-cke":!0},insert:"head",singleton:!0};_n()(Sn.Z,kn);Sn.Z.locals;class On extends t.Plugin{constructor(t){super(t),this._dataSchema=t.plugins.get("DataSchema"),this._allowedAttributes=new n.Matcher,this._disallowedAttributes=new n.Matcher,this._allowedElements=new Set,this._disallowedElements=new Set,this._dataInitialized=!1,this._coupledAttributes=null,this._registerElementsAfterInit(),this._registerElementHandlers(),this._registerCoupledAttributesPostFixer(),this._registerAssociatedHtmlAttributesPostFixer()}static get pluginName(){return"DataFilter"}static get requires(){return[hn,i.Widget]}loadAllowedConfig(t){for(const e of t){const t=e.name||/[\s\S]+/,r=Pn(e);this.allowElement(t),r.forEach((t=>this.allowAttributes(t)))}}loadDisallowedConfig(t){for(const e of t){const t=e.name||/[\s\S]+/,r=Pn(e);0==r.length?this.disallowElement(t):r.forEach((t=>this.disallowAttributes(t)))}}loadAllowedEmptyElementsConfig(t){for(const e of t)this.allowEmptyElement(e)}allowElement(t){for(const e of this._dataSchema.getDefinitionsForView(t,!0))this._addAllowedElement(e),this._coupledAttributes=null}disallowElement(t){for(const e of this._dataSchema.getDefinitionsForView(t,!1))this._disallowedElements.add(e.view)}allowEmptyElement(t){for(const e of this._dataSchema.getDefinitionsForView(t,!0))e.isInline&&this._dataSchema.extendInlineElement({...e,allowEmpty:!0})}allowAttributes(t){this._allowedAttributes.add(t)}disallowAttributes(t){this._disallowedAttributes.add(t)}processViewAttributes(t,e){return Cn(t,e,this._disallowedAttributes),Cn(t,e,this._allowedAttributes)}_addAllowedElement(t){if(!this._allowedElements.has(t)){if(this._allowedElements.add(t),"appliesToBlock"in t&&"string"==typeof t.appliesToBlock)for(const e of this._dataSchema.getDefinitionsForModel(t.appliesToBlock))e.isBlock&&this._addAllowedElement(e);this._dataInitialized&&this.editor.data.once("set",(()=>{this._fireRegisterEvent(t)}),{priority:e.priorities.highest+1})}}_registerElementsAfterInit(){this.editor.data.on("init",(()=>{this._dataInitialized=!0;for(const t of this._allowedElements)this._fireRegisterEvent(t)}),{priority:e.priorities.highest+1})}_registerElementHandlers(){this.on("register",((t,r)=>{const o=this.editor.model.schema;if(r.isObject&&!o.isRegistered(r.model))this._registerObjectElement(r);else if(r.isBlock)this._registerBlockElement(r);else{if(!r.isInline)throw new e.CKEditorError("data-filter-invalid-definition",null,r);this._registerInlineElement(r)}t.stop()}),{priority:"lowest"})}_registerCoupledAttributesPostFixer(){const t=this.editor.model,e=t.document.selection;t.document.registerPostFixer((e=>{const r=t.document.differ.getChanges();let o=!1;const n=this._getCoupledAttributesMap();for(const t of r){if("attribute"!=t.type||null!==t.attributeNewValue)continue;const r=n.get(t.attributeKey);if(r)for(const{item:n}of t.range.getWalker({shallow:!0}))for(const t of r)n.hasAttribute(t)&&(e.removeAttribute(t,n),o=!0)}return o})),this.listenTo(e,"change:attribute",((r,{attributeKeys:o})=>{const n=new Set,i=this._getCoupledAttributesMap();for(const t of o){if(e.hasAttribute(t))continue;const r=i.get(t);if(r)for(const t of r)e.hasAttribute(t)&&n.add(t)}0!=n.size&&t.change((t=>{for(const e of n)t.removeSelectionAttribute(e)}))}))}_registerAssociatedHtmlAttributesPostFixer(){const t=this.editor.model;t.document.registerPostFixer((e=>{const r=t.document.differ.getChanges();let o=!1;for(const n of r)if("insert"===n.type&&"$text"!==n.name)for(const r of n.attributes.keys())r.startsWith("html")&&r.endsWith("Attributes")&&(t.schema.checkAttribute(n.name,r)||(e.removeAttribute(r,n.position.nodeAfter),o=!0));return o}))}_getCoupledAttributesMap(){if(this._coupledAttributes)return this._coupledAttributes;this._coupledAttributes=new Map;for(const t of this._allowedElements)if(t.coupledAttribute&&t.model){const e=this._coupledAttributes.get(t.coupledAttribute);e?e.push(t.model):this._coupledAttributes.set(t.coupledAttribute,[t.model])}return this._coupledAttributes}_fireRegisterEvent(t){t.view&&this._disallowedElements.has(t.view)||this.fire(t.view?`register:${t.view}`:"register",t)}_registerObjectElement(t){const r=this.editor,o=r.model.schema,n=r.conversion,{view:i,model:s}=t;o.register(s,t.modelSchema),i&&(o.extend(t.model,{allowAttributes:[Po(i),"htmlContent"]}),r.data.registerRawContentMatcher({name:i}),n.for("upcast").elementToElement({view:i,model:$o(t),converterPriority:e.priorities.low+2}),n.for("upcast").add(Mo(t,this)),n.for("editingDowncast").elementToStructure({model:{name:s,attributes:[Po(i)]},view:To(r,t)}),n.for("dataDowncast").elementToElement({model:s,view:(t,{writer:e})=>Do(i,t,e)}),n.for("dataDowncast").add(Lo(t)))}_registerBlockElement(t){const r=this.editor,o=r.model.schema,n=r.conversion,{view:i,model:s}=t;if(!o.isRegistered(t.model)){if(o.register(t.model,t.modelSchema),!i)return;n.for("upcast").elementToElement({model:s,view:i,converterPriority:e.priorities.low+2}),n.for("downcast").elementToElement({model:s,view:i})}i&&(o.extend(t.model,{allowAttributes:Po(i)}),n.for("upcast").add(Mo(t,this)),n.for("downcast").add(Lo(t)))}_registerInlineElement(t){const e=this.editor,r=e.model.schema,o=e.conversion,n=t.model;t.appliesToBlock||(r.extend("$text",{allowAttributes:n}),t.attributeProperties&&r.setAttributeProperties(n,t.attributeProperties),o.for("upcast").add(Bo(t,this)),o.for("downcast").attributeToElement({model:n,view:No(t)}),t.allowEmpty&&(r.setAttributeProperties(n,{copyFromObject:!1}),r.isRegistered("htmlEmptyElement")||r.register("htmlEmptyElement",{inheritAllFrom:"$inlineObject"}),e.data.htmlProcessor.domConverter.registerInlineObjectMatcher((e=>e.name==t.view&&e.isEmpty&&Array.from(e.getAttributeKeys()).length?{name:!0}:null)),o.for("editingDowncast").elementToElement({model:"htmlEmptyElement",view:Ro(t,!0)}),o.for("dataDowncast").elementToElement({model:"htmlEmptyElement",view:Ro(t)})))}}function Cn(t,r,o){const n=function(t,{consumable:e},r){const o=r.matchAll(t)||[],n=[];for(const r of o)xn(e,t,r),delete r.match.name,e.consume(t,r.match),n.push(r);return n}(t,r,o),{attributes:i,styles:s,classes:l}=function(t){const e={attributes:new Set,classes:new Set,styles:new Set};for(const r of t)for(const t in e){(r.match[t]||[]).forEach((r=>e[t].add(r)))}return e}(n),a={};if(i.size)for(const t of i)(0,e.isValidAttributeName)(t)||i.delete(t);return i.size&&(a.attributes=Fn(i,(e=>t.getAttribute(e)))),s.size&&(a.styles=Fn(s,(e=>t.getStyle(e)))),l.size&&(a.classes=Array.from(l)),Object.keys(a).length?a:null}function xn(t,e,r){for(const o of["attributes","classes","styles"]){const n=r.match[o];if(n)for(const r of Array.from(n))t.test(e,{[o]:[r]})||jn(n,r)}}function Fn(t,e){const r={};for(const o of t){void 0!==e(o)&&(r[o]=e(o))}return r}function In(t,e){const{name:r}=t,o=t[e];return Jo(o)?Object.entries(o).map((([t,o])=>({name:r,[e]:{[t]:o}}))):Array.isArray(o)?o.map((t=>({name:r,[e]:[t]}))):[t]}function Pn(t){const{name:e,attributes:r,classes:o,styles:n}=t,i=[];return r&&i.push(...In({name:e,attributes:r},"attributes")),o&&i.push(...In({name:e,classes:o},"classes")),n&&i.push(...In({name:e,styles:n},"styles")),i}class $n extends t.Plugin{static get requires(){return[On]}static get pluginName(){return"CodeBlockElementSupport"}init(){if(!this.editor.plugins.has("CodeBlockEditing"))return;const t=this.editor.plugins.get(On);t.on("register:pre",((e,r)=>{if("codeBlock"!==r.model)return;const o=this.editor,n=o.model.schema,i=o.conversion;n.extend("codeBlock",{allowAttributes:["htmlPreAttributes","htmlContentAttributes"]}),i.for("upcast").add(function(t){return e=>{e.on("element:code",((e,r,o)=>{const n=r.viewItem,i=n.parent;function s(e,n){const i=t.processViewAttributes(e,o);i&&o.writer.setAttribute(n,i,r.modelRange)}i&&i.is("element","pre")&&(s(i,"htmlPreAttributes"),s(n,"htmlContentAttributes"))}),{priority:"low"})}}(t)),i.for("downcast").add((t=>{t.on("attribute:htmlPreAttributes:codeBlock",((t,e,r)=>{if(!r.consumable.consume(e.item,t.name))return;const{attributeOldValue:o,attributeNewValue:n}=e,i=r.mapper.toViewElement(e.item).parent;xo(r.writer,o,n,i)})),t.on("attribute:htmlContentAttributes:codeBlock",((t,e,r)=>{if(!r.consumable.consume(e.item,t.name))return;const{attributeOldValue:o,attributeNewValue:n}=e,i=r.mapper.toViewElement(e.item);xo(r.writer,o,n,i)}))})),e.stop()}))}}class Tn extends t.Plugin{static get requires(){return[On]}static get pluginName(){return"DualContentModelElementSupport"}init(){this.editor.plugins.get(On).on("register",((t,r)=>{const o=r,n=this.editor,i=n.model.schema,s=n.conversion;if(!o.paragraphLikeModel)return;if(i.isRegistered(o.model)||i.isRegistered(o.paragraphLikeModel))return;const l={model:o.paragraphLikeModel,view:o.view};i.register(o.model,o.modelSchema),i.register(l.model,{inheritAllFrom:"$block"}),s.for("upcast").elementToElement({view:o.view,model:(t,{writer:e})=>this._hasBlockContent(t)?e.createElement(o.model):e.createElement(l.model),converterPriority:e.priorities.low+.5}),s.for("downcast").elementToElement({view:o.view,model:o.model}),this._addAttributeConversion(o),s.for("downcast").elementToElement({view:l.view,model:l.model}),this._addAttributeConversion(l),t.stop()}))}_hasBlockContent(t){const e=this.editor.editing.view,r=e.domConverter.blockElements;for(const o of e.createRangeIn(t).getItems())if(o.is("element")&&r.includes(o.name))return!0;return!1}_addAttributeConversion(t){const e=this.editor,r=e.conversion,o=e.plugins.get(On);e.model.schema.extend(t.model,{allowAttributes:Po(t.view)}),r.for("upcast").add(Mo(t,o)),r.for("downcast").add(Lo(t))}}var Dn=r(331);class Bn extends t.Plugin{static get requires(){return[hn,Dn.Enter]}static get pluginName(){return"HeadingElementSupport"}init(){const t=this.editor;if(!t.plugins.has("HeadingEditing"))return;const e=t.config.get("heading.options");this.registerHeadingElements(t,e)}registerHeadingElements(t,e){const r=t.plugins.get(hn),o=[];for(const t of e)"model"in t&&"view"in t&&(r.registerBlockElement({view:t.view,model:t.model}),o.push(t.model));r.extendBlockElement({model:"htmlHgroup",modelSchema:{allowChildren:o}})}}function Rn(t,e,r){const o=t.createRangeOn(e);for(const{item:t}of o.getWalker())if(t.is("element",r))return t}class Nn extends t.Plugin{static get requires(){return[On]}static get pluginName(){return"ImageElementSupport"}init(){const t=this.editor;if(!t.plugins.has("ImageInlineEditing")&&!t.plugins.has("ImageBlockEditing"))return;const e=t.model.schema,r=t.conversion,o=t.plugins.get(On);o.on("register:figure",(()=>{r.for("upcast").add(function(t){return e=>{e.on("element:figure",((e,r,o)=>{const n=r.viewItem;if(!r.modelRange||!n.hasClass("image"))return;const i=t.processViewAttributes(n,o);i&&o.writer.setAttribute("htmlFigureAttributes",i,r.modelRange)}),{priority:"low"})}}(o))})),o.on("register:img",((n,i)=>{"imageBlock"!==i.model&&"imageInline"!==i.model||(e.isRegistered("imageBlock")&&e.extend("imageBlock",{allowAttributes:["htmlImgAttributes","htmlFigureAttributes","htmlLinkAttributes"]}),e.isRegistered("imageInline")&&e.extend("imageInline",{allowAttributes:["htmlA","htmlImgAttributes"]}),r.for("upcast").add(function(t){return e=>{e.on("element:img",((e,r,o)=>{if(!r.modelRange)return;const n=r.viewItem,i=t.processViewAttributes(n,o);i&&o.writer.setAttribute("htmlImgAttributes",i,r.modelRange)}),{priority:"low"})}}(o)),r.for("downcast").add((t=>{function e(e){t.on(`attribute:${e}:imageInline`,((t,e,r)=>{if(!r.consumable.consume(e.item,t.name))return;const{attributeOldValue:o,attributeNewValue:n}=e,i=r.mapper.toViewElement(e.item);xo(r.writer,o,n,i)}),{priority:"low"})}function r(e,r){t.on(`attribute:${r}:imageBlock`,((t,r,o)=>{if(!o.consumable.test(r.item,t.name))return;const{attributeOldValue:n,attributeNewValue:i}=r,s=o.mapper.toViewElement(r.item),l=Rn(o.writer,s,e);l&&(xo(o.writer,n,i,l),o.consumable.consume(r.item,t.name))}),{priority:"low"}),"a"===e&&t.on("attribute:linkHref:imageBlock",((t,e,r)=>{if(!r.consumable.consume(e.item,"attribute:htmlLinkAttributes:imageBlock"))return;const o=r.mapper.toViewElement(e.item),n=Rn(r.writer,o,"a");Fo(r.writer,e.item.getAttribute("htmlLinkAttributes"),n)}),{priority:"low"})}e("htmlImgAttributes"),r("img","htmlImgAttributes"),r("figure","htmlFigureAttributes"),r("a","htmlLinkAttributes")})),t.plugins.has("LinkImage")&&r.for("upcast").add(function(t,e){const r=e.plugins.get("ImageUtils");return e=>{e.on("element:a",((e,o,n)=>{const i=o.viewItem;if(!r.findViewImgElement(i))return;const s=o.modelCursor.parent;if(!s.is("element","imageBlock"))return;const l=t.processViewAttributes(i,n);l&&n.writer.setAttribute("htmlLinkAttributes",l,s)}),{priority:"low"})}}(o,t)),n.stop())}))}}class Mn extends t.Plugin{static get requires(){return[On]}static get pluginName(){return"MediaEmbedElementSupport"}init(){const t=this.editor;if(!t.plugins.has("MediaEmbed")||t.config.get("mediaEmbed.previewsInData"))return;const e=t.model.schema,r=t.conversion,o=this.editor.plugins.get(On),n=this.editor.plugins.get(hn),i=t.config.get("mediaEmbed.elementName");n.registerBlockElement({model:"media",view:i}),o.on("register:figure",(()=>{r.for("upcast").add(function(t){return e=>{e.on("element:figure",((e,r,o)=>{const n=r.viewItem;if(!r.modelRange||!n.hasClass("media"))return;const i=t.processViewAttributes(n,o);i&&o.writer.setAttribute("htmlFigureAttributes",i,r.modelRange)}),{priority:"low"})}}(o))})),o.on(`register:${i}`,((t,n)=>{"media"===n.model&&(e.extend("media",{allowAttributes:[Po(i),"htmlFigureAttributes"]}),r.for("upcast").add(function(t,e){const r=(r,o,n)=>{function i(e,r){const i=t.processViewAttributes(e,n);i&&n.writer.setAttribute(r,i,o.modelRange)}i(o.viewItem,Po(e))};return t=>{t.on(`element:${e}`,r,{priority:"low"})}}(o,i)),r.for("dataDowncast").add(function(t){return e=>{function r(t,r){e.on(`attribute:${r}:media`,((e,r,o)=>{if(!o.consumable.consume(r.item,e.name))return;const{attributeOldValue:n,attributeNewValue:i}=r,s=o.mapper.toViewElement(r.item),l=Rn(o.writer,s,t);xo(o.writer,n,i,l)}))}r(t,Po(t)),r("figure","htmlFigureAttributes")}}(i)),t.stop())}))}}class Ln extends t.Plugin{static get requires(){return[On]}static get pluginName(){return"ScriptElementSupport"}init(){const t=this.editor.plugins.get(On);t.on("register:script",((e,r)=>{const o=this.editor,n=o.model.schema,i=o.conversion;n.register("htmlScript",r.modelSchema),n.extend("htmlScript",{allowAttributes:["htmlScriptAttributes","htmlContent"],isContent:!0}),o.data.registerRawContentMatcher({name:"script"}),i.for("upcast").elementToElement({view:"script",model:$o(r)}),i.for("upcast").add(Mo(r,t)),i.for("downcast").elementToElement({model:"htmlScript",view:(t,{writer:e})=>Do("script",t,e)}),i.for("downcast").add(Lo(r)),e.stop()}))}}class Vn extends t.Plugin{static get requires(){return[On]}static get pluginName(){return"TableElementSupport"}init(){const t=this.editor;if(!t.plugins.has("TableEditing"))return;const e=t.model.schema,r=t.conversion,o=t.plugins.get(On),n=t.plugins.get("TableUtils");o.on("register:figure",(()=>{r.for("upcast").add(function(t){return e=>{e.on("element:figure",((e,r,o)=>{const n=r.viewItem;if(!r.modelRange||!n.hasClass("table"))return;const i=t.processViewAttributes(n,o);i&&o.writer.setAttribute("htmlFigureAttributes",i,r.modelRange)}),{priority:"low"})}}(o))})),o.on("register:table",((i,s)=>{"table"===s.model&&(e.extend("table",{allowAttributes:["htmlTableAttributes","htmlFigureAttributes","htmlTheadAttributes","htmlTbodyAttributes"]}),r.for("upcast").add(function(t){return e=>{e.on("element:table",((e,r,o)=>{if(!r.modelRange)return;const n=r.viewItem;i(n,"htmlTableAttributes");for(const t of n.getChildren())t.is("element","thead")&&i(t,"htmlTheadAttributes"),t.is("element","tbody")&&i(t,"htmlTbodyAttributes");function i(e,n){const i=t.processViewAttributes(e,o);i&&o.writer.setAttribute(n,i,r.modelRange)}}),{priority:"low"})}}(o)),r.for("downcast").add((t=>{function e(e,r){t.on(`attribute:${r}:table`,((t,r,o)=>{if(!o.consumable.test(r.item,t.name))return;const n=o.mapper.toViewElement(r.item),i=Rn(o.writer,n,e);i&&(o.consumable.consume(r.item,t.name),xo(o.writer,r.attributeOldValue,r.attributeNewValue,i))}))}e("table","htmlTableAttributes"),e("figure","htmlFigureAttributes"),e("thead","htmlTheadAttributes"),e("tbody","htmlTbodyAttributes")})),t.model.document.registerPostFixer(function(t,e){return r=>{const o=t.document.differ.getChanges();let n=!1;for(const t of o){if("attribute"!=t.type||"headingRows"!=t.attributeKey)continue;const o=t.range.start.nodeAfter,i=o.getAttribute("htmlTheadAttributes"),s=o.getAttribute("htmlTbodyAttributes");i&&!t.attributeNewValue?(r.removeAttribute("htmlTheadAttributes",o),n=!0):s&&t.attributeNewValue==e.getRows(o)&&(r.removeAttribute("htmlTbodyAttributes",o),n=!0)}return n}}(t.model,n)),i.stop())}))}}class Hn extends t.Plugin{static get requires(){return[On]}static get pluginName(){return"StyleElementSupport"}init(){const t=this.editor.plugins.get(On);t.on("register:style",((e,r)=>{const o=this.editor,n=o.model.schema,i=o.conversion;n.register("htmlStyle",r.modelSchema),n.extend("htmlStyle",{allowAttributes:["htmlStyleAttributes","htmlContent"],isContent:!0}),o.data.registerRawContentMatcher({name:"style"}),i.for("upcast").elementToElement({view:"style",model:$o(r)}),i.for("upcast").add(Mo(r,t)),i.for("downcast").elementToElement({model:"htmlStyle",view:(t,{writer:e})=>Do("style",t,e)}),i.for("downcast").add(Lo(r)),e.stop()}))}}const zn=function(t){return this.__data__.set(t,"__lodash_hash_undefined__"),this};const Un=function(t){return this.__data__.has(t)};function Wn(t){var e=-1,r=null==t?0:t.length;for(this.__data__=new bt;++e<r;)this.add(t[e])}Wn.prototype.add=Wn.prototype.push=zn,Wn.prototype.has=Un;const qn=Wn;const Gn=function(t,e){for(var r=-1,o=null==t?0:t.length;++r<o;)if(e(t[r],r,t))return!0;return!1};const Zn=function(t,e){return t.has(e)};const Kn=function(t,e,r,o,n,i){var s=1&r,l=t.length,a=e.length;if(l!=a&&!(s&&a>l))return!1;var c=i.get(t),u=i.get(e);if(c&&u)return c==e&&u==t;var m=-1,d=!0,f=2&r?new qn:void 0;for(i.set(t,e),i.set(e,t);++m<l;){var h=t[m],b=e[m];if(o)var p=s?o(b,h,m,e,t,i):o(h,b,m,t,e,i);if(void 0!==p){if(p)continue;d=!1;break}if(f){if(!Gn(e,(function(t,e){if(!Zn(f,e)&&(h===t||n(h,t,r,o,i)))return f.push(e)}))){d=!1;break}}else if(h!==b&&!n(h,b,r,o,i)){d=!1;break}}return i.delete(t),i.delete(e),d};const Yn=function(t){var e=-1,r=Array(t.size);return t.forEach((function(t,o){r[++e]=[o,t]})),r};const Jn=function(t){var e=-1,r=Array(t.size);return t.forEach((function(t){r[++e]=t})),r};var Xn=E?E.prototype:void 0,Qn=Xn?Xn.valueOf:void 0;const ti=function(t,e,r,o,n,i,s){switch(r){case"[object DataView]":if(t.byteLength!=e.byteLength||t.byteOffset!=e.byteOffset)return!1;t=t.buffer,e=e.buffer;case"[object ArrayBuffer]":return!(t.byteLength!=e.byteLength||!i(new Ke(t),new Ke(e)));case"[object Boolean]":case"[object Date]":case"[object Number]":return l(+t,+e);case"[object Error]":return t.name==e.name&&t.message==e.message;case"[object RegExp]":case"[object String]":return t==e+"";case"[object Map]":var a=Yn;case"[object Set]":var c=1&o;if(a||(a=Jn),t.size!=e.size&&!c)return!1;var u=s.get(t);if(u)return u==e;o|=2,s.set(t,e);var m=Kn(a(t),a(e),o,n,i,s);return s.delete(t),m;case"[object Symbol]":if(Qn)return Qn.call(t)==Qn.call(e)}return!1};var ei=Object.prototype.hasOwnProperty;const ri=function(t,e,r,o,n,i){var s=1&r,l=xe(t),a=l.length;if(a!=xe(e).length&&!s)return!1;for(var c=a;c--;){var u=l[c];if(!(s?u in e:ei.call(e,u)))return!1}var m=i.get(t),d=i.get(e);if(m&&d)return m==e&&d==t;var f=!0;i.set(t,e),i.set(e,t);for(var h=s;++c<a;){var b=t[u=l[c]],p=e[u];if(o)var g=s?o(p,b,u,e,t,i):o(b,p,u,t,e,i);if(!(void 0===g?b===p||n(b,p,r,o,i):g)){f=!1;break}h||(h="constructor"==u)}if(f&&!h){var v=t.constructor,w=e.constructor;v==w||!("constructor"in t)||!("constructor"in e)||"function"==typeof v&&v instanceof v&&"function"==typeof w&&w instanceof w||(f=!1)}return i.delete(t),i.delete(e),f};var oi="[object Arguments]",ni="[object Array]",ii="[object Object]",si=Object.prototype.hasOwnProperty;const li=function(t,e,r,o,n,i){var s=Pt(t),l=Pt(e),a=s?ni:qe(t),c=l?ni:qe(e),u=(a=a==oi?ii:a)==ii,m=(c=c==oi?ii:c)==ii,d=a==c;if(d&&Rt(t)){if(!Rt(e))return!1;s=!0,u=!1}if(d&&!u)return i||(i=new vt),s||Kt(t)?Kn(t,e,r,o,n,i):ti(t,e,a,r,o,n,i);if(!(1&r)){var f=u&&si.call(t,"__wrapped__"),h=m&&si.call(e,"__wrapped__");if(f||h){var b=f?t.value():t,p=h?e.value():e;return i||(i=new vt),n(b,p,r,o,i)}}return!!d&&(i||(i=new vt),ri(t,e,r,o,n,i))};const ai=function t(e,r,o,n,i){return e===r||(null==e||null==r||!kt(e)&&!kt(r)?e!=e&&r!=r:li(e,r,o,n,t,i))};const ci=function(t,e){return ai(t,e)};class ui extends t.Plugin{static get requires(){return[On]}static get pluginName(){return"DocumentListElementSupport"}init(){const t=this.editor;if(!t.plugins.has("DocumentListEditing"))return;const e=t.model.schema,r=t.conversion,o=t.plugins.get(On),n=t.plugins.get("DocumentListEditing"),i=["ul","ol","li"];n.registerDowncastStrategy({scope:"item",attributeName:"htmlLiAttributes",setAttributeOnDowncast:Fo}),n.registerDowncastStrategy({scope:"list",attributeName:"htmlUlAttributes",setAttributeOnDowncast:Fo}),n.registerDowncastStrategy({scope:"list",attributeName:"htmlOlAttributes",setAttributeOnDowncast:Fo}),o.on("register",((t,n)=>{if(!i.includes(n.view))return;if(t.stop(),e.checkAttribute("$block","htmlLiAttributes"))return;const s=i.map((t=>Po(t)));e.extend("$listItem",{allowAttributes:s}),r.for("upcast").add((t=>{t.on("element:ul",mi("htmlUlAttributes",o),{priority:"low"}),t.on("element:ol",mi("htmlOlAttributes",o),{priority:"low"}),t.on("element:li",mi("htmlLiAttributes",o),{priority:"low"})}))})),n.on("postFixer",((t,{listNodes:e,writer:r})=>{for(const{node:o,previousNodeInList:n}of e)if(n){if(n.getAttribute("listType")==o.getAttribute("listType")){const e=di(n.getAttribute("listType")),i=n.getAttribute(e);!ci(o.getAttribute(e),i)&&r.model.schema.checkAttribute(o,e)&&(r.setAttribute(e,i,o),t.return=!0)}if(n.getAttribute("listItemId")==o.getAttribute("listItemId")){const e=n.getAttribute("htmlLiAttributes");!ci(o.getAttribute("htmlLiAttributes"),e)&&r.model.schema.checkAttribute(o,"htmlLiAttributes")&&(r.setAttribute("htmlLiAttributes",e,o),t.return=!0)}}})),n.on("postFixer",((t,{listNodes:e,writer:r})=>{for(const{node:o}of e){const e=o.getAttribute("listType");"numbered"!==e&&o.getAttribute("htmlOlAttributes")&&(r.removeAttribute("htmlOlAttributes",o),t.return=!0),"numbered"===e&&o.getAttribute("htmlUlAttributes")&&(r.removeAttribute("htmlUlAttributes",o),t.return=!0)}}))}afterInit(){const t=this.editor;if(!t.commands.get("indentList"))return;const e=t.commands.get("indentList");this.listenTo(e,"afterExecute",((e,r)=>{t.model.change((e=>{for(const o of r){const r=di(o.getAttribute("listType"));t.model.schema.checkAttribute(o,r)&&e.setAttribute(r,{},o)}}))}))}}function mi(t,e){return(r,o,n)=>{const i=o.viewItem;o.modelRange||Object.assign(o,n.convertChildren(o.viewItem,o.modelCursor));const s=e.processViewAttributes(i,n);for(const e of o.modelRange.getItems({shallow:!0}))e.hasAttribute("listItemId")&&(e.hasAttribute(t)||n.writer.model.schema.checkAttribute(e,t)&&n.writer.setAttribute(t,s||{},e))}}function di(t){return"numbered"===t?"htmlOlAttributes":"htmlUlAttributes"}class fi extends t.Plugin{static get requires(){return[On,hn]}static get pluginName(){return"CustomElementSupport"}init(){const t=this.editor.plugins.get(On),e=this.editor.plugins.get(hn);t.on("register:$customElement",((r,o)=>{r.stop();const i=this.editor,s=i.model.schema,l=i.conversion,a=i.editing.view.domConverter.unsafeElements,c=i.data.htmlProcessor.domConverter.preElements;s.register(o.model,o.modelSchema),s.extend(o.model,{allowAttributes:["htmlElementName","htmlCustomElementAttributes","htmlContent"],isContent:!0}),i.data.htmlProcessor.domConverter.registerRawContentMatcher({name:"template"}),l.for("upcast").elementToElement({view:/.*/,model:(r,s)=>{if("$comment"==r.name)return null;if(!function(t){try{document.createElement(t)}catch(t){return!1}return!0}(r.name))return null;if(e.getDefinitionsForView(r.name).size)return null;a.includes(r.name)||a.push(r.name),c.includes(r.name)||c.push(r.name);const l=s.writer.createElement(o.model,{htmlElementName:r.name}),u=t.processViewAttributes(r,s);let m;if(u&&s.writer.setAttribute("htmlCustomElementAttributes",u,l),r.is("element","template")&&r.getCustomProperty("$rawContent"))m=r.getCustomProperty("$rawContent");else{const t=new n.UpcastWriter(r.document).createDocumentFragment(r),e=i.data.htmlProcessor.domConverter.viewToDom(t),o=e.firstChild;for(;o.firstChild;)e.appendChild(o.firstChild);o.remove(),m=i.data.htmlProcessor.htmlWriter.getHtml(e)}s.writer.setAttribute("htmlContent",m,l);for(const{item:t}of i.editing.view.createRangeIn(r))s.consumable.consume(t,{name:!0});return l},converterPriority:"low"}),l.for("editingDowncast").elementToElement({model:{name:o.model,attributes:["htmlElementName","htmlCustomElementAttributes","htmlContent"]},view:(t,{writer:e})=>{const r=t.getAttribute("htmlElementName"),o=e.createRawElement(r);return t.hasAttribute("htmlCustomElementAttributes")&&Fo(e,t.getAttribute("htmlCustomElementAttributes"),o),o}}),l.for("dataDowncast").elementToElement({model:{name:o.model,attributes:["htmlElementName","htmlCustomElementAttributes","htmlContent"]},view:(t,{writer:e})=>{const r=t.getAttribute("htmlElementName"),o=t.getAttribute("htmlContent"),n=e.createRawElement(r,null,((t,e)=>{e.setContentOf(t,o)}));return t.hasAttribute("htmlCustomElementAttributes")&&Fo(e,t.getAttribute("htmlCustomElementAttributes"),n),n}})}))}}class hi extends t.Plugin{static get pluginName(){return"GeneralHtmlSupport"}static get requires(){return[On,$n,Tn,Bn,Nn,Mn,Ln,Vn,Hn,ui,fi]}init(){const t=this.editor,e=t.plugins.get(On);e.loadAllowedEmptyElementsConfig(t.config.get("htmlSupport.allowEmpty")||[]),e.loadAllowedConfig(t.config.get("htmlSupport.allow")||[]),e.loadDisallowedConfig(t.config.get("htmlSupport.disallow")||[])}getGhsAttributeNameForElement(t){const e=this.editor.plugins.get("DataSchema"),r=Array.from(e.getDefinitionsForView(t,!1)),o=r.find((t=>t.isInline&&!r[0].isObject));return o?o.model:Po(t)}addModelHtmlClass(t,r,o){const n=this.editor.model,i=this.getGhsAttributeNameForElement(t);n.change((t=>{for(const s of bi(n,o,i))Io(t,s,i,"classes",(t=>{for(const o of(0,e.toArray)(r))t.add(o)}))}))}removeModelHtmlClass(t,r,o){const n=this.editor.model,i=this.getGhsAttributeNameForElement(t);n.change((t=>{for(const s of bi(n,o,i))Io(t,s,i,"classes",(t=>{for(const o of(0,e.toArray)(r))t.delete(o)}))}))}setModelHtmlAttributes(t,e,r){const o=this.editor.model,n=this.getGhsAttributeNameForElement(t);o.change((t=>{for(const i of bi(o,r,n))Io(t,i,n,"attributes",(t=>{for(const[r,o]of Object.entries(e))t.set(r,o)}))}))}removeModelHtmlAttributes(t,r,o){const n=this.editor.model,i=this.getGhsAttributeNameForElement(t);n.change((t=>{for(const s of bi(n,o,i))Io(t,s,i,"attributes",(t=>{for(const o of(0,e.toArray)(r))t.delete(o)}))}))}setModelHtmlStyles(t,e,r){const o=this.editor.model,n=this.getGhsAttributeNameForElement(t);o.change((t=>{for(const i of bi(o,r,n))Io(t,i,n,"styles",(t=>{for(const[r,o]of Object.entries(e))t.set(r,o)}))}))}removeModelHtmlStyles(t,r,o){const n=this.editor.model,i=this.getGhsAttributeNameForElement(t);n.change((t=>{for(const s of bi(n,o,i))Io(t,s,i,"styles",(t=>{for(const o of(0,e.toArray)(r))t.delete(o)}))}))}}function*bi(t,e,r){if(e)if(!(Symbol.iterator in e)&&e.is("documentSelection")&&e.isCollapsed)t.schema.checkAttributeInSelection(e,r)&&(yield e);else for(const o of function(t,e,r){return!(Symbol.iterator in e)&&(e.is("node")||e.is("$text")||e.is("$textProxy"))?t.schema.checkAttribute(e,r)?[t.createRangeOn(e)]:[]:t.schema.getValidRanges(t.createSelection(e).getRanges(),r)}(t,e,r))yield*o.getItems({shallow:!0})}class pi extends t.Plugin{static get pluginName(){return"HtmlComment"}init(){const t=this.editor,r=new Map;t.data.processor.skipComments=!1,t.model.schema.addAttributeCheck(((t,e)=>{if(t.endsWith("$root")&&e.startsWith("$comment"))return!0})),t.conversion.for("upcast").elementToMarker({view:"$comment",model:t=>{const o=`$comment:${(0,e.uid)()}`,n=t.getCustomProperty("$rawContent");return r.set(o,n),o}}),t.conversion.for("dataDowncast").markerToElement({model:"$comment",view:(t,{writer:e})=>{let r;for(const e of this.editor.model.document.getRootNames())if(r=this.editor.model.document.getRoot(e),r.hasAttribute(t.markerName))break;const o=t.markerName,n=r.getAttribute(o),i=e.createUIElement("$comment");return e.setCustomProperty("$rawContent",n,i),i}}),t.model.document.registerPostFixer((e=>{let o=!1;const n=t.model.document.differ.getChangedMarkers().filter((t=>t.name.startsWith("$comment:")));for(const t of n){const{oldRange:n,newRange:i}=t.data;if(!n||!i||n.root!=i.root){if(n){const r=n.root;r.hasAttribute(t.name)&&(e.removeAttribute(t.name,r),o=!0)}if(i){const n=i.root;"$graveyard"==n.rootName?(e.removeMarker(t.name),o=!0):n.hasAttribute(t.name)||(e.setAttribute(t.name,r.get(t.name)||"",n),o=!0)}}}return o})),t.data.on("set",(()=>{for(const e of t.model.markers.getMarkersGroup("$comment"))this.removeHtmlComment(e.name)}),{priority:"high"}),t.model.on("deleteContent",((e,[r])=>{for(const e of r.getRanges()){const r=t.model.schema.getLimitElement(e),o=t.model.createPositionAt(r,0),n=t.model.createPositionAt(r,"end");let i;i=o.isTouching(e.start)&&n.isTouching(e.end)?this.getHtmlCommentsInRange(t.model.createRange(o,n)):this.getHtmlCommentsInRange(e,{skipBoundaries:!0});for(const t of i)this.removeHtmlComment(t)}}),{priority:"high"})}createHtmlComment(t,r){const o=(0,e.uid)(),n=this.editor.model,i=n.document.getRoot(t.root.rootName),s=`$comment:${o}`;return n.change((e=>{const o=e.createRange(t);return e.addMarker(s,{usingOperation:!0,affectsData:!0,range:o}),e.setAttribute(s,r,i),s}))}removeHtmlComment(t){const e=this.editor,r=e.model.markers.get(t);return!!r&&(e.model.change((t=>{t.removeMarker(r)})),!0)}getHtmlCommentData(t){const e=this.editor.model.markers.get(t);if(!e)return null;let r="";for(const e of this.editor.model.document.getRoots())if(e.hasAttribute(t)){r=e.getAttribute(t);break}return{content:r,position:e.getStart()}}getHtmlCommentsInRange(t,{skipBoundaries:e=!1}={}){const r=!e;return Array.from(this.editor.model.markers.getMarkersGroup("$comment")).filter((e=>function(t,e){const o=t.getRange().start;return(o.isAfter(e.start)||r&&o.isEqual(e.start))&&(o.isBefore(e.end)||r&&o.isEqual(e.end))}(e,t))).map((t=>t.name))}}class gi extends n.HtmlDataProcessor{toView(t){if(!t.match(/<(?:html|body|head|meta)(?:\s[^>]*)?>/i))return super.toView(t);let e="",r="";t=(t=t.replace(/<!DOCTYPE[^>]*>/i,(t=>(e=t,"")))).replace(/<\?xml\s[^?]*\?>/i,(t=>(r=t,"")));const o=this._toDom(t),i=this.domConverter.domToView(o,{skipComments:this.skipComments}),s=new n.UpcastWriter(i.document);return s.setCustomProperty("$fullPageDocument",o.ownerDocument.documentElement.outerHTML,i),e&&s.setCustomProperty("$fullPageDocType",e,i),r&&s.setCustomProperty("$fullPageXmlDeclaration",r,i),i}toData(t){let e=super.toData(t);const r=t.getCustomProperty("$fullPageDocument"),o=t.getCustomProperty("$fullPageDocType"),n=t.getCustomProperty("$fullPageXmlDeclaration");return r&&(e=r.replace(/<\/body\s*>/,e+"$&"),o&&(e=o+"\n"+e),n&&(e=n+"\n"+e)),e}}class vi extends t.Plugin{static get pluginName(){return"FullPage"}init(){const t=this.editor,e=["$fullPageDocument","$fullPageDocType","$fullPageXmlDeclaration"];t.data.processor=new gi(t.data.viewDocument),t.model.schema.extend("$root",{allowAttributes:e}),t.data.on("toModel",((r,[o])=>{const n=t.model.document.getRoot();t.model.change((t=>{for(const r of e){const e=o.getCustomProperty(r);e&&t.setAttribute(r,e,n)}}))}),{priority:"low"}),t.data.on("toView",((t,[r])=>{if(!r.is("rootElement"))return;const o=r,i=t.return;if(!o.hasAttribute("$fullPageDocument"))return;const s=new n.UpcastWriter(i.document);for(const t of e){const e=o.getAttribute(t);e&&s.setCustomProperty(t,e,i)}}),{priority:"low"}),t.data.on("set",(()=>{const r=t.model.document.getRoot();t.model.change((t=>{for(const o of e)r.hasAttribute(o)&&t.removeAttribute(o,r)}))}),{priority:"high"}),t.data.on("get",((t,e)=>{e[0]||(e[0]={}),e[0].trim=!1}),{priority:"high"})}}})(),(window.CKEditor5=window.CKEditor5||{}).htmlSupport=o})();;
/*!
 * @license Copyright (c) 2003-2023, CKSource Holding sp. z o.o. All rights reserved.
 * For licensing, see LICENSE.md.
 */(()=>{var t={704:(t,e,o)=>{t.exports=o(79)("./src/core.js")},492:(t,e,o)=>{t.exports=o(79)("./src/engine.js")},273:(t,e,o)=>{t.exports=o(79)("./src/ui.js")},209:(t,e,o)=>{t.exports=o(79)("./src/utils.js")},434:(t,e,o)=>{t.exports=o(79)("./src/watchdog.js")},79:t=>{"use strict";t.exports=CKEditor5.dll}},e={};function o(r){var i=e[r];if(void 0!==i)return i.exports;var n=e[r]={exports:{}};return t[r](n,n.exports,o),n.exports}o.d=(t,e)=>{for(var r in e)o.o(e,r)&&!o.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:e[r]})},o.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),o.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})};var r={};(()=>{"use strict";o.r(r),o.d(r,{DecoupledEditor:()=>F});var t=o(704),e=o(209),i=o(434),n=o(273),s=o(492);class c extends n.EditorUI{constructor(t,e){super(t),this.view=e}init(){const t=this.editor,e=this.view,o=t.editing.view,r=e.editable,i=o.document.getRoot();r.name=i.rootName,e.render();const n=r.element;this.setEditableElement(r.name,n),e.editable.bind("isFocused").to(this.focusTracker),o.attachDomRoot(n),this._initPlaceholder(),this._initToolbar(),this.fire("ready")}destroy(){super.destroy();const t=this.view;this.editor.editing.view.detachDomRoot(t.editable.name),t.destroy()}_initToolbar(){const t=this.editor,e=this.view;e.toolbar.fillFromConfig(t.config.get("toolbar"),this.componentFactory),this.addToolbar(e.toolbar)}_initPlaceholder(){const t=this.editor,e=t.editing.view,o=e.document.getRoot(),r=t.config.get("placeholder");if(r){const t="string"==typeof r?r:r[o.rootName];t&&(o.placeholder=t)}(0,s.enablePlaceholder)({view:e,element:o,isDirectHost:!1,keepOnFocus:!0})}}class l extends n.EditorUIView{constructor(t,e,o={}){super(t);const r=t.t;this.toolbar=new n.ToolbarView(t,{shouldGroupWhenFull:o.shouldToolbarGroupWhenFull}),this.editable=new n.InlineEditableUIView(t,e,o.editableElement,{label:t=>r("Rich Text Editor. Editing area: %0",t.name)}),this.toolbar.extendTemplate({attributes:{class:["ck-reset_all","ck-rounded-corners"],dir:t.uiLanguageDirection}})}render(){super.render(),this.registerChild([this.toolbar,this.editable])}}const a=function(t){return null!=t&&"object"==typeof t};const d="object"==typeof global&&global&&global.Object===Object&&global;var u="object"==typeof self&&self&&self.Object===Object&&self;const h=(d||u||Function("return this")()).Symbol;var b=Object.prototype,p=b.hasOwnProperty,g=b.toString,f=h?h.toStringTag:void 0;const m=function(t){var e=p.call(t,f),o=t[f];try{t[f]=void 0;var r=!0}catch(t){}var i=g.call(t);return r&&(e?t[f]=o:delete t[f]),i};var v=Object.prototype.toString;const y=function(t){return v.call(t)};var w=h?h.toStringTag:void 0;const E=function(t){return null==t?void 0===t?"[object Undefined]":"[object Null]":w&&w in Object(t)?m(t):y(t)};const j=function(t,e){return function(o){return t(e(o))}}(Object.getPrototypeOf,Object);var x=Function.prototype,O=Object.prototype,T=x.toString,D=O.hasOwnProperty,S=T.call(Object);const P=function(t){if(!a(t)||"[object Object]"!=E(t))return!1;var e=j(t);if(null===e)return!0;var o=D.call(e,"constructor")&&e.constructor;return"function"==typeof o&&o instanceof o&&T.call(o)==S};const C=function(t){return a(t)&&1===t.nodeType&&!P(t)};class F extends((0,t.DataApiMixin)((0,t.ElementApiMixin)(t.Editor))){constructor(o,r={}){if(!W(o)&&void 0!==r.initialData)throw new e.CKEditorError("editor-create-initial-data",null);super(r),void 0===this.config.get("initialData")&&this.config.set("initialData",function(t){return W(t)?(0,e.getDataFromElement)(t):t}(o)),W(o)&&(this.sourceElement=o,(0,t.secureSourceElement)(this,o)),this.model.document.createRoot();const i=!this.config.get("toolbar.shouldNotGroupWhenFull"),n=new l(this.locale,this.editing.view,{editableElement:this.sourceElement,shouldToolbarGroupWhenFull:i});this.ui=new c(this,n)}destroy(){const t=this.getData();return this.ui.destroy(),super.destroy().then((()=>{this.sourceElement&&this.updateSourceElement(t)}))}static create(t,o={}){return new Promise((r=>{if(W(t)&&"TEXTAREA"===t.tagName)throw new e.CKEditorError("editor-wrong-element",null);const i=new this(t,o);r(i.initPlugins().then((()=>i.ui.init())).then((()=>i.data.init(i.config.get("initialData")))).then((()=>i.fire("ready"))).then((()=>i)))}))}}function W(t){return C(t)}F.Context=t.Context,F.EditorWatchdog=i.EditorWatchdog,F.ContextWatchdog=i.ContextWatchdog})(),(window.CKEditor5=window.CKEditor5||{}).editorDecoupled=r})();;
/* @license GNU-GPL-2.0-or-later https://www.drupal.org/licensing/faq */
(function($,Drupal,debounce){const cache={right:0,left:0,bottom:0,top:0};const cssVarPrefix='--drupal-displace-offset';const documentStyle=document.documentElement.style;const offsetKeys=Object.keys(cache);const offsetProps={};offsetKeys.forEach((edge)=>{offsetProps[edge]={enumerable:true,get(){return cache[edge];},set(value){if(value!==cache[edge])documentStyle.setProperty(`${cssVarPrefix}-${edge}`,`${value}px`);cache[edge]=value;}};});const offsets=Object.seal(Object.defineProperties({},offsetProps));function getRawOffset(el,edge){const $el=$(el);const documentElement=document.documentElement;let displacement=0;const horizontal=edge==='left'||edge==='right';let placement=$el.offset()[horizontal?'left':'top'];placement-=window[`scroll${horizontal?'X':'Y'}`]||document.documentElement[`scroll${horizontal?'Left':'Top'}`]||0;switch(edge){case 'top':displacement=placement+$el.outerHeight();break;case 'left':displacement=placement+$el.outerWidth();break;case 'bottom':displacement=documentElement.clientHeight-placement;break;case 'right':displacement=documentElement.clientWidth-placement;break;default:displacement=0;}return displacement;}function calculateOffset(edge){let edgeOffset=0;const displacingElements=document.querySelectorAll(`[data-offset-${edge}]`);const n=displacingElements.length;for(let i=0;i<n;i++){const el=displacingElements[i];if(el.style.display==='none')continue;let displacement=parseInt(el.getAttribute(`data-offset-${edge}`),10);if(isNaN(displacement))displacement=getRawOffset(el,edge);edgeOffset=Math.max(edgeOffset,displacement);}return edgeOffset;}function displace(broadcast=true){const newOffsets={};offsetKeys.forEach((edge)=>{newOffsets[edge]=calculateOffset(edge);});offsetKeys.forEach((edge)=>{offsets[edge]=newOffsets[edge];});if(broadcast)$(document).trigger('drupalViewportOffsetChange',offsets);return offsets;}Drupal.behaviors.drupalDisplace={attach(){if(this.displaceProcessed)return;this.displaceProcessed=true;$(window).on('resize.drupalDisplace',debounce(displace,200));}};Drupal.displace=displace;Object.defineProperty(Drupal.displace,'offsets',{value:offsets,writable:false});Drupal.displace.calculateOffset=calculateOffset;})(jQuery,Drupal,Drupal.debounce);;
(($,Drupal,{isTabbable})=>{$.extend($.expr[':'],{tabbable(element){Drupal.deprecationError({message:'The :tabbable selector is deprecated in Drupal 9.2.0 and will be removed in Drupal 11.0.0. Use the core/tabbable library instead. See https://www.drupal.org/node/3183730'});return isTabbable(element);}});})(jQuery,Drupal,window.tabbable);;
(($)=>{let cachedScrollbarWidth=null;const {max,abs}=Math;const regexHorizontal=/left|center|right/;const regexVertical=/top|center|bottom/;const regexOffset=/[+-]\d+(\.[\d]+)?%?/;const regexPosition=/^\w+/;const regexPercent=/%$/;const _position=$.fn.position;function getOffsets(offsets,width,height){return [parseFloat(offsets[0])*(regexPercent.test(offsets[0])?width/100:1),parseFloat(offsets[1])*(regexPercent.test(offsets[1])?height/100:1)];}function parseCss(element,property){return parseInt(window.getComputedStyle(element)[property],10)||0;}function getDimensions(elem){const raw=elem[0];if(raw.nodeType===9)return {width:elem.width(),height:elem.height(),offset:{top:0,left:0}};if($.isWindow(raw))return {width:elem.width(),height:elem.height(),offset:{top:elem.scrollTop(),left:elem.scrollLeft()}};if(raw.preventDefault)return {width:0,height:0,offset:{top:raw.pageY,left:raw.pageX}};return {width:elem.outerWidth(),height:elem.outerHeight(),offset:elem.offset()};}const collisions={fit:{left(position,data){const {within}=data;const withinOffset=within.isWindow?within.scrollLeft:within.offset.left;const outerWidth=within.width;const collisionPosLeft=position.left-data.collisionPosition.marginLeft;const overLeft=withinOffset-collisionPosLeft;const overRight=collisionPosLeft+data.collisionWidth-outerWidth-withinOffset;let newOverRight;if(data.collisionWidth>outerWidth)if(overLeft>0&&overRight<=0){newOverRight=position.left+overLeft+data.collisionWidth-outerWidth-withinOffset;position.left+=overLeft-newOverRight;}else if(overRight>0&&overLeft<=0)position.left=withinOffset;else if(overLeft>overRight)position.left=withinOffset+outerWidth-data.collisionWidth;else position.left=withinOffset;else if(overLeft>0)position.left+=overLeft;else if(overRight>0)position.left-=overRight;else position.left=max(position.left-collisionPosLeft,position.left);},top(position,data){const {within}=data;const withinOffset=within.isWindow?within.scrollTop:within.offset.top;const outerHeight=data.within.height;const collisionPosTop=position.top-data.collisionPosition.marginTop;const overTop=withinOffset-collisionPosTop;const overBottom=collisionPosTop+data.collisionHeight-outerHeight-withinOffset;let newOverBottom;if(data.collisionHeight>outerHeight)if(overTop>0&&overBottom<=0){newOverBottom=position.top+overTop+data.collisionHeight-outerHeight-withinOffset;position.top+=overTop-newOverBottom;}else if(overBottom>0&&overTop<=0)position.top=withinOffset;else if(overTop>overBottom)position.top=withinOffset+outerHeight-data.collisionHeight;else position.top=withinOffset;else if(overTop>0)position.top+=overTop;else if(overBottom>0)position.top-=overBottom;else position.top=max(position.top-collisionPosTop,position.top);}},flip:{left(position,data){const {within}=data;const withinOffset=within.offset.left+within.scrollLeft;const outerWidth=within.width;const offsetLeft=within.isWindow?within.scrollLeft:within.offset.left;const collisionPosLeft=position.left-data.collisionPosition.marginLeft;const overLeft=collisionPosLeft-offsetLeft;const overRight=collisionPosLeft+data.collisionWidth-outerWidth-offsetLeft;const myOffset=data.my[0]==='left'?-data.elemWidth:data.my[0]==='right'?data.elemWidth:0;const atOffset=data.at[0]==='left'?data.targetWidth:data.at[0]==='right'?-data.targetWidth:0;const offset=-2*data.offset[0];let newOverRight;let newOverLeft;if(overLeft<0){newOverRight=position.left+myOffset+atOffset+offset+data.collisionWidth-outerWidth-withinOffset;if(newOverRight<0||newOverRight<abs(overLeft))position.left+=myOffset+atOffset+offset;}else{if(overRight>0){newOverLeft=position.left-data.collisionPosition.marginLeft+myOffset+atOffset+offset-offsetLeft;if(newOverLeft>0||abs(newOverLeft)<overRight)position.left+=myOffset+atOffset+offset;}}},top(position,data){const {within}=data;const withinOffset=within.offset.top+within.scrollTop;const outerHeight=within.height;const offsetTop=within.isWindow?within.scrollTop:within.offset.top;const collisionPosTop=position.top-data.collisionPosition.marginTop;const overTop=collisionPosTop-offsetTop;const overBottom=collisionPosTop+data.collisionHeight-outerHeight-offsetTop;const top=data.my[1]==='top';const myOffset=top?-data.elemHeight:data.my[1]==='bottom'?data.elemHeight:0;const atOffset=data.at[1]==='top'?data.targetHeight:data.at[1]==='bottom'?-data.targetHeight:0;const offset=-2*data.offset[1];let newOverTop;let newOverBottom;if(overTop<0){newOverBottom=position.top+myOffset+atOffset+offset+data.collisionHeight-outerHeight-withinOffset;if(newOverBottom<0||newOverBottom<abs(overTop))position.top+=myOffset+atOffset+offset;}else{if(overBottom>0){newOverTop=position.top-data.collisionPosition.marginTop+myOffset+atOffset+offset-offsetTop;if(newOverTop>0||abs(newOverTop)<overBottom)position.top+=myOffset+atOffset+offset;}}}},flipfit:{left(...args){collisions.flip.left.apply(this,args);collisions.fit.left.apply(this,args);},top(...args){collisions.flip.top.apply(this,args);collisions.fit.top.apply(this,args);}}};$.position={scrollbarWidth(){if(cachedScrollbarWidth!==undefined)return cachedScrollbarWidth;const div=$('<div '+"style='display:block;position:absolute;width:50px;height:50px;overflow:hidden;'>"+"<div style='height:100px;width:auto;'></div></div>");const innerDiv=div.children()[0];$('body').append(div);const w1=innerDiv.offsetWidth;div[0].style.overflow='scroll';let w2=innerDiv.offsetWidth;if(w1===w2)w2=div[0].clientWidth;div.remove();cachedScrollbarWidth=w1-w2;return cachedScrollbarWidth;},getScrollInfo(within){const overflowX=within.isWindow||within.isDocument?'':window.getComputedStyle(within.element[0])['overflow-x'];const overflowY=within.isWindow||within.isDocument?'':window.getComputedStyle(within.element[0])['overflow-y'];const hasOverflowX=overflowX==='scroll'||(overflowX==='auto'&&within.width<within.element[0].scrollWidth);const hasOverflowY=overflowY==='scroll'||(overflowY==='auto'&&within.height<within.element[0].scrollHeight);return {width:hasOverflowY?$.position.scrollbarWidth():0,height:hasOverflowX?$.position.scrollbarWidth():0};},getWithinInfo(element){const withinElement=$(element||window);const isWindow=$.isWindow(withinElement[0]);const isDocument=!!withinElement[0]&&withinElement[0].nodeType===9;const hasOffset=!isWindow&&!isDocument;return {element:withinElement,isWindow,isDocument,offset:hasOffset?$(element).offset():{left:0,top:0},scrollLeft:withinElement.scrollLeft(),scrollTop:withinElement.scrollTop(),width:withinElement.outerWidth(),height:withinElement.outerHeight()};}};$.fn.position=function(options){if(!options||!options.of)return _position.apply(this,arguments);options=$.extend({},options);const within=$.position.getWithinInfo(options.within);const scrollInfo=$.position.getScrollInfo(within);const collision=(options.collision||'flip').split(' ');const offsets={};const target=typeof options.of==='string'?$(document).find(options.of):$(options.of);const dimensions=getDimensions(target);const targetWidth=dimensions.width;const targetHeight=dimensions.height;const targetOffset=dimensions.offset;if(target[0].preventDefault)options.at='left top';const basePosition=$.extend({},targetOffset);$.each(['my','at'],function(){let pos=(options[this]||'').split(' ');if(pos.length===1)pos=regexHorizontal.test(pos[0])?pos.concat(['center']):regexVertical.test(pos[0])?['center'].concat(pos):['center','center'];pos[0]=regexHorizontal.test(pos[0])?pos[0]:'center';pos[1]=regexVertical.test(pos[1])?pos[1]:'center';const horizontalOffset=regexOffset.exec(pos[0]);const verticalOffset=regexOffset.exec(pos[1]);offsets[this]=[horizontalOffset?horizontalOffset[0]:0,verticalOffset?verticalOffset[0]:0];options[this]=[regexPosition.exec(pos[0])[0],regexPosition.exec(pos[1])[0]];});if(collision.length===1)collision[1]=collision[0];if(options.at[0]==='right')basePosition.left+=targetWidth;else{if(options.at[0]==='center')basePosition.left+=targetWidth/2;}if(options.at[1]==='bottom')basePosition.top+=targetHeight;else{if(options.at[1]==='center')basePosition.top+=targetHeight/2;}const atOffset=getOffsets(offsets.at,targetWidth,targetHeight);basePosition.left+=atOffset[0];basePosition.top+=atOffset[1];return this.each(function(){let using;const elem=$(this);const elemWidth=elem.outerWidth();const elemHeight=elem.outerHeight();const marginLeft=parseCss(this,'marginLeft');const marginTop=parseCss(this,'marginTop');const collisionWidth=elemWidth+marginLeft+parseCss(this,'marginRight')+scrollInfo.width;const collisionHeight=elemHeight+marginTop+parseCss(this,'marginBottom')+scrollInfo.height;const position=$.extend({},basePosition);const myOffset=getOffsets(offsets.my,elem.outerWidth(),elem.outerHeight());if(options.my[0]==='right')position.left-=elemWidth;else{if(options.my[0]==='center')position.left-=elemWidth/2;}if(options.my[1]==='bottom')position.top-=elemHeight;else{if(options.my[1]==='center')position.top-=elemHeight/2;}position.left+=myOffset[0];position.top+=myOffset[1];const collisionPosition={marginLeft,marginTop};$.each(['left','top'],function(i,dir){if(collisions[collision[i]])collisions[collision[i]][dir](position,{targetWidth,targetHeight,elemWidth,elemHeight,collisionPosition,collisionWidth,collisionHeight,offset:[atOffset[0]+myOffset[0],atOffset[1]+myOffset[1]],my:options.my,at:options.at,within,elem});});if(options.using)using=function(props){const left=targetOffset.left-position.left;const right=left+targetWidth-elemWidth;const top=targetOffset.top-position.top;const bottom=top+targetHeight-elemHeight;const feedback={target:{element:target,left:targetOffset.left,top:targetOffset.top,width:targetWidth,height:targetHeight},element:{element:elem,left:position.left,top:position.top,width:elemWidth,height:elemHeight},horizontal:right<0?'left':left>0?'right':'center',vertical:bottom<0?'top':top>0?'bottom':'middle'};if(targetWidth<elemWidth&&abs(left+right)<targetWidth)feedback.horizontal='center';if(targetHeight<elemHeight&&abs(top+bottom)<targetHeight)feedback.vertical='middle';if(max(abs(left),abs(right))>max(abs(top),abs(bottom)))feedback.important='horizontal';else feedback.important='vertical';options.using.call(this,props,feedback);};elem.offset($.extend(position,{using}));});};if(!$.hasOwnProperty('ui'))$.ui={};$.ui.position=collisions;})(jQuery);;
(function($,Drupal,drupalSettings,bodyScrollLock){drupalSettings.dialog={autoOpen:true,dialogClass:'',buttonClass:'button',buttonPrimaryClass:'button--primary',close(event){Drupal.dialog(event.target).close();Drupal.detachBehaviors(event.target,null,'unload');}};Drupal.dialog=function(element,options){let undef;const $element=$(element);const dialog={open:false,returnValue:undef};function openDialog(settings){settings=$.extend({},drupalSettings.dialog,options,settings);$(window).trigger('dialog:beforecreate',[dialog,$element,settings]);$element.dialog(settings);dialog.open=true;if(settings.modal)bodyScrollLock.lock($element.get(0));$(window).trigger('dialog:aftercreate',[dialog,$element,settings]);}function closeDialog(value){$(window).trigger('dialog:beforeclose',[dialog,$element]);bodyScrollLock.clearBodyLocks();$element.dialog('close');dialog.returnValue=value;dialog.open=false;$(window).trigger('dialog:afterclose',[dialog,$element]);}dialog.show=()=>{openDialog({modal:false});};dialog.showModal=()=>{openDialog({modal:true});};dialog.close=closeDialog;return dialog;};})(jQuery,Drupal,drupalSettings,bodyScrollLock);;
(function($,Drupal,drupalSettings,debounce,displace){drupalSettings.dialog=$.extend({autoResize:true,maxHeight:'95%'},drupalSettings.dialog);function resetPosition(options){const offsets=displace.offsets;const left=offsets.left-offsets.right;const top=offsets.top-offsets.bottom;const leftString=`${(left>0?'+':'-')+Math.abs(Math.round(left/2))}px`;const topString=`${(top>0?'+':'-')+Math.abs(Math.round(top/2))}px`;options.position={my:`center${left!==0?leftString:''} center${top!==0?topString:''}`,of:window};return options;}function resetSize(event){const positionOptions=['width','height','minWidth','minHeight','maxHeight','maxWidth','position'];let adjustedOptions={};let windowHeight=$(window).height();let option;let optionValue;let adjustedValue;for(let n=0;n<positionOptions.length;n++){option=positionOptions[n];optionValue=event.data.settings[option];if(optionValue)if(typeof optionValue==='string'&&/%$/.test(optionValue)&&/height/i.test(option)){windowHeight-=displace.offsets.top+displace.offsets.bottom;adjustedValue=parseInt(0.01*parseInt(optionValue,10)*windowHeight,10);if(option==='height'&&event.data.$element.parent().outerHeight()<adjustedValue)adjustedValue='auto';adjustedOptions[option]=adjustedValue;}}if(!event.data.settings.modal)adjustedOptions=resetPosition(adjustedOptions);event.data.$element.dialog('option',adjustedOptions).trigger('dialogContentResize');}$(window).on({'dialog:aftercreate':function(event,dialog,$element,settings){const autoResize=debounce(resetSize,20);const eventData={settings,$element};if(settings.autoResize===true||settings.autoResize==='true'){const uiDialog=$element.dialog('option',{resizable:false,draggable:false}).dialog('widget');uiDialog[0].style.position='fixed';$(window).on('resize.dialogResize scroll.dialogResize',eventData,autoResize).trigger('resize.dialogResize');$(document).on('drupalViewportOffsetChange.dialogResize',eventData,autoResize);}},'dialog:beforeclose':function(event,dialog,$element){$(window).off('.dialogResize');$(document).off('.dialogResize');}});})(jQuery,Drupal,drupalSettings,Drupal.debounce,Drupal.displace);;
(function($,{tabbable,isTabbable}){$.widget('ui.dialog',$.ui.dialog,{options:{buttonClass:'button',buttonPrimaryClass:'button--primary'},_createButtons(){const opts=this.options;let primaryIndex;let index;const il=opts.buttons.length;for(index=0;index<il;index++)if(opts.buttons[index].primary&&opts.buttons[index].primary===true){primaryIndex=index;delete opts.buttons[index].primary;break;}this._super();const $buttons=this.uiButtonSet.children().addClass(opts.buttonClass);if(typeof primaryIndex!=='undefined')$buttons.eq(index).addClass(opts.buttonPrimaryClass);},_focusTabbable(){let hasFocus=this._focusedElement?this._focusedElement.get(0):null;if(!hasFocus)hasFocus=this.element.find('[autofocus]').get(0);if(!hasFocus){const $elements=[this.element,this.uiDialogButtonPane];for(let i=0;i<$elements.length;i++){const element=$elements[i].get(0);if(element){const elementTabbable=tabbable(element);hasFocus=elementTabbable.length?elementTabbable[0]:null;}if(hasFocus)break;}}if(!hasFocus){const closeBtn=this.uiDialogTitlebarClose.get(0);hasFocus=closeBtn&&isTabbable(closeBtn)?closeBtn:null;}if(!hasFocus)hasFocus=this.uiDialog.get(0);$(hasFocus).eq(0).trigger('focus');}});})(jQuery,window.tabbable);;
(($)=>{$.widget('ui.dialog',$.ui.dialog,{_allowInteraction(event){if(event.target.classList===undefined)return this._super(event);return event.target.classList.contains('ck')||this._super(event);}});})(jQuery);;
(function($,Drupal,drupalSettings){function findFieldForFormatSelector($formatSelector){const fieldId=$formatSelector.attr('data-editor-for');return $(`#${fieldId}`).get(0);}function filterXssWhenSwitching(field,format,originalFormatID,callback){if(format.editor.isXssSafe)callback(field,format);else $.ajax({url:Drupal.url(`editor/filter_xss/${format.format}`),type:'POST',data:{value:field.value,original_format_id:originalFormatID},dataType:'json',success(xssFilteredValue){if(xssFilteredValue!==false)field.value=xssFilteredValue;callback(field,format);}});}function changeTextEditor(field,newFormatID){const previousFormatID=field.getAttribute('data-editor-active-text-format');if(drupalSettings.editor.formats[previousFormatID])Drupal.editorDetach(field,drupalSettings.editor.formats[previousFormatID]);else $(field).off('.editor');if(drupalSettings.editor.formats[newFormatID]){const format=drupalSettings.editor.formats[newFormatID];filterXssWhenSwitching(field,format,previousFormatID,Drupal.editorAttach);}field.setAttribute('data-editor-active-text-format',newFormatID);}function onTextFormatChange(event){const select=event.target;const field=event.data.field;const activeFormatID=field.getAttribute('data-editor-active-text-format');const newFormatID=select.value;if(newFormatID===activeFormatID)return;const supportContentFiltering=drupalSettings.editor.formats[newFormatID]&&drupalSettings.editor.formats[newFormatID].editorSupportsContentFiltering;const hasContent=field.value!=='';if(hasContent&&supportContentFiltering){const message=Drupal.t('Changing the text format to %text_format will permanently remove content that is not allowed in that text format.<br><br>Save your changes before switching the text format to avoid losing data.',{'%text_format':$(select).find('option:selected')[0].textContent});const confirmationDialog=Drupal.dialog(`<div>${message}</div>`,{title:Drupal.t('Change text format?'),dialogClass:'editor-change-text-format-modal',resizable:false,buttons:[{text:Drupal.t('Continue'),class:'button button--primary',click(){changeTextEditor(field,newFormatID);confirmationDialog.close();}},{text:Drupal.t('Cancel'),class:'button',click(){select.value=activeFormatID;const eventChange=new Event('change');select.dispatchEvent(eventChange);confirmationDialog.close();}}],closeOnEscape:false,create(){$(this).parent().find('.ui-dialog-titlebar-close').remove();},beforeClose:false,close(event){$(event.target).remove();}});confirmationDialog.showModal();}else changeTextEditor(field,newFormatID);}Drupal.editors={};Drupal.behaviors.editor={attach(context,settings){if(!settings.editor)return;once('editor','[data-editor-for]',context).forEach((editor)=>{const $this=$(editor);const field=findFieldForFormatSelector($this);if(!field)return;const activeFormatID=editor.value;field.setAttribute('data-editor-active-text-format',activeFormatID);if(settings.editor.formats[activeFormatID])Drupal.editorAttach(field,settings.editor.formats[activeFormatID]);$(field).on('change.editor keypress.editor',()=>{field.setAttribute('data-editor-value-is-changed','true');$(field).off('.editor');});if(editor.tagName==='SELECT')$this.on('change.editorAttach',{field},onTextFormatChange);$this.parents('form').on('submit',(event)=>{if(event.isDefaultPrevented())return;if(settings.editor.formats[activeFormatID])Drupal.editorDetach(field,settings.editor.formats[activeFormatID],'serialize');});});},detach(context,settings,trigger){let editors;if(trigger==='serialize')editors=once.filter('editor','[data-editor-for]',context);else editors=once.remove('editor','[data-editor-for]',context);editors.forEach((editor)=>{const $this=$(editor);const activeFormatID=editor.value;const field=findFieldForFormatSelector($this);if(field&&activeFormatID in settings.editor.formats)Drupal.editorDetach(field,settings.editor.formats[activeFormatID],trigger);});}};Drupal.editorAttach=function(field,format){if(format.editor){Drupal.editors[format.editor].attach(field,format);Drupal.editors[format.editor].onChange(field,()=>{$(field).trigger('formUpdated');field.setAttribute('data-editor-value-is-changed','true');});}};Drupal.editorDetach=function(field,format,trigger){if(format.editor){Drupal.editors[format.editor].detach(field,format,trigger);if(field.getAttribute('data-editor-value-is-changed')==='false')field.value=field.getAttribute('data-editor-value-original');}};})(jQuery,Drupal,drupalSettings);;
((Drupal,debounce,CKEditor5,$,once)=>{Drupal.CKEditor5Instances=new Map();const callbacks=new Map();const required=new Set();function findFunc(scope,name){if(!scope)return null;const parts=name.includes('.')?name.split('.'):name;if(parts.length>1)return findFunc(scope[parts.shift()],parts);return typeof scope[parts[0]]==='function'?scope[parts[0]]:null;}function buildFunc(config){const {func}=config;const fn=findFunc(window,func.name);if(typeof fn==='function'){const result=func.invoke?fn(...func.args):fn;return result;}return null;}function buildRegexp(config){const {pattern}=config.regexp;const main=pattern.match(/\/(.+)\/.*/)[1];const options=pattern.match(/\/.+\/(.*)/)[1];return new RegExp(main,options);}function processConfig(config){function processArray(config){return config.map((item)=>{if(typeof item==='object')return processConfig(item);return item;});}return Object.entries(config).reduce((processed,[key,value])=>{if(typeof value==='object'){if(!value)return processed;if(value.hasOwnProperty('func'))processed[key]=buildFunc(value);else if(value.hasOwnProperty('regexp'))processed[key]=buildRegexp(value);else if(Array.isArray(value))processed[key]=processArray(value);else processed[key]=processConfig(value);}else processed[key]=value;return processed;},{});}const setElementId=(element)=>{const id=Math.random().toString().slice(2,9);element.setAttribute('data-ckeditor5-id',id);return id;};const getElementId=(element)=>element.getAttribute('data-ckeditor5-id');function selectPlugins(plugins){return plugins.map((pluginDefinition)=>{const [build,name]=pluginDefinition.split('.');if(CKEditor5[build]&&CKEditor5[build][name])return CKEditor5[build][name];console.warn(`Failed to load ${build} - ${name}`);return null;});}function processRules(rulesGroup){try{[...rulesGroup.cssRules].forEach(ckeditor5SelectorProcessing);}catch(e){console.warn(`Stylesheet ${rulesGroup.href} not included in CKEditor reset due to the browser's CORS policy.`);}}function ckeditor5SelectorProcessing(rule){if(rule.cssRules)processRules(rule);if(!rule.selectorText)return;const offCanvasId='#drupal-off-canvas';const CKEditorClass='.ck';const styleFence='[data-drupal-ck-style-fence]';if(rule.selectorText.includes(offCanvasId)||rule.selectorText.includes(CKEditorClass))rule.selectorText=rule.selectorText.split(/,/g).map((selector)=>{if(selector.includes(offCanvasId))return `${selector.trim()}:not(${styleFence} *)`;if(selector.includes(CKEditorClass))return [selector.trim(),selector.trim().replace(CKEditorClass,`${offCanvasId} ${styleFence} ${CKEditorClass}`)];return selector;}).flat().join(', ');}function offCanvasCss(element){const fenceName='data-drupal-ck-style-fence';const editor=Drupal.CKEditor5Instances.get(element.getAttribute('data-ckeditor5-id'));editor.ui.view.element.setAttribute(fenceName,'');if(once('ckeditor5-off-canvas-reset','body').length){[...document.styleSheets].forEach(processRules);const prefix=`#drupal-off-canvas-wrapper [${fenceName}]`;const addedCss=[`${prefix} .ck.ck-content {display:block;min-height:5rem;}`,`${prefix} .ck.ck-content * {display:revert;background:revert;color:initial;padding:revert;}`,`${prefix} .ck.ck-content li {display:list-item}`,`${prefix} .ck.ck-content ol li {list-style-type: decimal}`];const prefixedCss=[...addedCss].join('\n');const offCanvasCssStyle=document.createElement('style');offCanvasCssStyle.textContent=prefixedCss;offCanvasCssStyle.setAttribute('id','ckeditor5-off-canvas-reset');document.body.appendChild(offCanvasCssStyle);}}Drupal.editors.ckeditor5={attach(element,format){const {editorClassic}=CKEditor5;const {toolbar,plugins,config,language}=format.editorSettings;const extraPlugins=selectPlugins(plugins);const pluginConfig=processConfig(config);const editorConfig={extraPlugins,toolbar,...pluginConfig,language:{...pluginConfig.language,...language}};const id=setElementId(element);const {ClassicEditor}=editorClassic;ClassicEditor.create(element,editorConfig).then((editor)=>{function calculateLineHeight(rows){const element=document.createElement('p');element.setAttribute('style','visibility: hidden;');element.innerHTML='&nbsp;';editor.ui.view.editable.element.append(element);const styles=window.getComputedStyle(element);const height=element.clientHeight;const marginTop=parseInt(styles.marginTop,10);const marginBottom=parseInt(styles.marginBottom,10);const mostMargin=marginTop>=marginBottom?marginTop:marginBottom;element.remove();return ((height+mostMargin)*(rows-1)+marginTop+height+marginBottom);}Drupal.CKEditor5Instances.set(id,editor);const rows=editor.sourceElement.getAttribute('rows');editor.ui.view.editable.element.closest('.ck-editor').style.setProperty('--ck-min-height',`${calculateLineHeight(rows)}px`);if(element.hasAttribute('required')){required.add(id);element.removeAttribute('required');}if(element.hasAttribute('disabled'))editor.enableReadOnlyMode('ckeditor5_disabled');$(document).on(`drupalViewportOffsetChange.ckeditor5.${id}`,(event,offsets)=>{editor.ui.viewportOffset=offsets;});editor.model.document.on('change:data',()=>{const callback=callbacks.get(id);if(callback){if(editor.plugins.has('SourceEditing'))if(editor.plugins.get('SourceEditing').isSourceEditingMode){callback();return;}debounce(callback,400)();}});const isOffCanvas=element.closest('#drupal-off-canvas');if(isOffCanvas)offCanvasCss(element);}).catch((error)=>{console.info('Debugging can be done with an unminified version of CKEditor by installing from the source file. Consult documentation at https://www.drupal.org/node/3258901');console.error(error);});},detach(element,format,trigger){const id=getElementId(element);const editor=Drupal.CKEditor5Instances.get(id);if(!editor)return;$(document).off(`drupalViewportOffsetChange.ckeditor5.${id}`);if(trigger==='serialize')editor.updateSourceElement();else{element.removeAttribute('contentEditable');return editor.destroy().then(()=>{Drupal.CKEditor5Instances.delete(id);callbacks.delete(id);if(required.has(id)){element.setAttribute('required','required');required.delete(id);}}).catch((error)=>{console.error(error);});}},onChange(element,callback){callbacks.set(getElementId(element),callback);},attachInlineEditor(element,format,mainToolbarId){const {editorDecoupled}=CKEditor5;const {toolbar,plugins,config:pluginConfig,language}=format.editorSettings;const extraPlugins=selectPlugins(plugins);const config={extraPlugins,toolbar,language,...processConfig(pluginConfig)};const id=setElementId(element);const {DecoupledEditor}=editorDecoupled;DecoupledEditor.create(element,config).then((editor)=>{Drupal.CKEditor5Instances.set(id,editor);const toolbar=document.getElementById(mainToolbarId);toolbar.appendChild(editor.ui.view.toolbar.element);editor.model.document.on('change:data',()=>{const callback=callbacks.get(id);if(callback)debounce(callback,400)(editor.getData());});}).catch((error)=>{console.error(error);});}};Drupal.ckeditor5={saveCallback:null,openDialog(url,saveCallback,dialogSettings){const classes=dialogSettings.dialogClass?dialogSettings.dialogClass.split(' '):[];classes.push('ui-dialog--narrow');dialogSettings.dialogClass=classes.join(' ');dialogSettings.autoResize=window.matchMedia('(min-width: 600px)').matches;dialogSettings.width='auto';const ckeditorAjaxDialog=Drupal.ajax({dialog:dialogSettings,dialogType:'modal',selector:'.ckeditor5-dialog-loading-link',url,progress:{type:'fullscreen'},submit:{editor_object:{}}});ckeditorAjaxDialog.execute();Drupal.ckeditor5.saveCallback=saveCallback;}};function redirectTextareaFragmentToCKEditor5Instance(){const hash=window.location.hash.substr(1);const element=document.getElementById(hash);if(element){const editorID=getElementId(element);const editor=Drupal.CKEditor5Instances.get(editorID);if(editor){editor.sourceElement.nextElementSibling.setAttribute('id',`cke_${hash}`);window.location.replace(`#cke_${hash}`);}}}$(window).on('hashchange.ckeditor',redirectTextareaFragmentToCKEditor5Instance);$(window).on('dialog:beforecreate',()=>{$('.ckeditor5-dialog-loading').animate({top:'-40px'},function removeDialogLoading(){$(this).remove();});});$(window).on('editor:dialogsave',(e,values)=>{if(Drupal.ckeditor5.saveCallback)Drupal.ckeditor5.saveCallback(values);});$(window).on('dialog:afterclose',()=>{if(Drupal.ckeditor5.saveCallback)Drupal.ckeditor5.saveCallback=null;});})(Drupal,Drupal.debounce,CKEditor5,jQuery,once);;
/* @license GNU-GPL-2.0-or-later https://raw.githubusercontent.com/ckeditor/ckeditor5/v40.2.0/LICENSE.md */
!function(t){const e=t.en=t.en||{};e.dictionary=Object.assign(e.dictionary||{},{Bold:"Bold",Code:"Code",Italic:"Italic",Strikethrough:"Strikethrough",Subscript:"Subscript",Superscript:"Superscript",Underline:"Underline"})}(window.CKEDITOR_TRANSLATIONS||(window.CKEDITOR_TRANSLATIONS={})),
/*!
 * @license Copyright (c) 2003-2023, CKSource Holding sp. z o.o. All rights reserved.
 * For licensing, see LICENSE.md.
 */(()=>{var t={55:(t,e,i)=>{"use strict";i.d(e,{Z:()=>r});var n=i(609),s=i.n(n)()((function(t){return t[1]}));s.push([t.id,".ck-content code{background-color:hsla(0,0%,78%,.3);border-radius:2px;padding:.15em}.ck.ck-editor__editable .ck-code_selected{background-color:hsla(0,0%,78%,.5)}",""]);const r=s},609:t=>{"use strict";t.exports=function(t){var e=[];return e.toString=function(){return this.map((function(e){var i=t(e);return e[2]?"@media ".concat(e[2]," {").concat(i,"}"):i})).join("")},e.i=function(t,i,n){"string"==typeof t&&(t=[[null,t,""]]);var s={};if(n)for(var r=0;r<this.length;r++){var o=this[r][0];null!=o&&(s[o]=!0)}for(var a=0;a<t.length;a++){var c=[].concat(t[a]);n&&s[c[0]]||(i&&(c[2]?c[2]="".concat(i," and ").concat(c[2]):c[2]=i),e.push(c))}},e}},62:(t,e,i)=>{"use strict";var n,s=function(){return void 0===n&&(n=Boolean(window&&document&&document.all&&!window.atob)),n},r=function(){var t={};return function(e){if(void 0===t[e]){var i=document.querySelector(e);if(window.HTMLIFrameElement&&i instanceof window.HTMLIFrameElement)try{i=i.contentDocument.head}catch(t){i=null}t[e]=i}return t[e]}}(),o=[];function a(t){for(var e=-1,i=0;i<o.length;i++)if(o[i].identifier===t){e=i;break}return e}function c(t,e){for(var i={},n=[],s=0;s<t.length;s++){var r=t[s],c=e.base?r[0]+e.base:r[0],l=i[c]||0,u="".concat(c," ").concat(l);i[c]=l+1;var d=a(u),g={css:r[1],media:r[2],sourceMap:r[3]};-1!==d?(o[d].references++,o[d].updater(g)):o.push({identifier:u,updater:b(g,e),references:1}),n.push(u)}return n}function l(t){var e=document.createElement("style"),n=t.attributes||{};if(void 0===n.nonce){var s=i.nc;s&&(n.nonce=s)}if(Object.keys(n).forEach((function(t){e.setAttribute(t,n[t])})),"function"==typeof t.insert)t.insert(e);else{var o=r(t.insert||"head");if(!o)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");o.appendChild(e)}return e}var u,d=(u=[],function(t,e){return u[t]=e,u.filter(Boolean).join("\n")});function g(t,e,i,n){var s=i?"":n.media?"@media ".concat(n.media," {").concat(n.css,"}"):n.css;if(t.styleSheet)t.styleSheet.cssText=d(e,s);else{var r=document.createTextNode(s),o=t.childNodes;o[e]&&t.removeChild(o[e]),o.length?t.insertBefore(r,o[e]):t.appendChild(r)}}function m(t,e,i){var n=i.css,s=i.media,r=i.sourceMap;if(s?t.setAttribute("media",s):t.removeAttribute("media"),r&&"undefined"!=typeof btoa&&(n+="\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(encodeURIComponent(JSON.stringify(r))))," */")),t.styleSheet)t.styleSheet.cssText=n;else{for(;t.firstChild;)t.removeChild(t.firstChild);t.appendChild(document.createTextNode(n))}}var p=null,h=0;function b(t,e){var i,n,s;if(e.singleton){var r=h++;i=p||(p=l(e)),n=g.bind(null,i,r,!1),s=g.bind(null,i,r,!0)}else i=l(e),n=m.bind(null,i,e),s=function(){!function(t){if(null===t.parentNode)return!1;t.parentNode.removeChild(t)}(i)};return n(t),function(e){if(e){if(e.css===t.css&&e.media===t.media&&e.sourceMap===t.sourceMap)return;n(t=e)}else s()}}t.exports=function(t,e){(e=e||{}).singleton||"boolean"==typeof e.singleton||(e.singleton=s());var i=c(t=t||[],e);return function(t){if(t=t||[],"[object Array]"===Object.prototype.toString.call(t)){for(var n=0;n<i.length;n++){var s=a(i[n]);o[s].references--}for(var r=c(t,e),l=0;l<i.length;l++){var u=a(i[l]);0===o[u].references&&(o[u].updater(),o.splice(u,1))}i=r}}}},704:(t,e,i)=>{t.exports=i(79)("./src/core.js")},181:(t,e,i)=>{t.exports=i(79)("./src/typing.js")},273:(t,e,i)=>{t.exports=i(79)("./src/ui.js")},79:t=>{"use strict";t.exports=CKEditor5.dll}},e={};function i(n){var s=e[n];if(void 0!==s)return s.exports;var r=e[n]={id:n,exports:{}};return t[n](r,r.exports,i),r.exports}i.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return i.d(e,{a:e}),e},i.d=(t,e)=>{for(var n in e)i.o(e,n)&&!i.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:e[n]})},i.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),i.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},i.nc=void 0;var n={};(()=>{"use strict";i.r(n),i.d(n,{Bold:()=>l,BoldEditing:()=>r,BoldUI:()=>c,Code:()=>f,CodeEditing:()=>g,CodeUI:()=>w,Italic:()=>T,ItalicEditing:()=>y,ItalicUI:()=>E,Strikethrough:()=>N,StrikethroughEditing:()=>A,StrikethroughUI:()=>I,Subscript:()=>F,SubscriptEditing:()=>B,SubscriptUI:()=>U,Superscript:()=>j,SuperscriptEditing:()=>M,SuperscriptUI:()=>V,Underline:()=>H,UnderlineEditing:()=>z,UnderlineUI:()=>q});var t=i(704);class e extends t.Command{constructor(t,e){super(t),this.attributeKey=e}refresh(){const t=this.editor.model,e=t.document;this.value=this._getValueFromFirstAllowedNode(),this.isEnabled=t.schema.checkAttributeInSelection(e.selection,this.attributeKey)}execute(t={}){const e=this.editor.model,i=e.document.selection,n=void 0===t.forceValue?!this.value:t.forceValue;e.change((t=>{if(i.isCollapsed)n?t.setSelectionAttribute(this.attributeKey,!0):t.removeSelectionAttribute(this.attributeKey);else{const s=e.schema.getValidRanges(i.getRanges(),this.attributeKey);for(const e of s)n?t.setAttribute(this.attributeKey,n,e):t.removeAttribute(this.attributeKey,e)}}))}_getValueFromFirstAllowedNode(){const t=this.editor.model,e=t.schema,i=t.document.selection;if(i.isCollapsed)return i.hasAttribute(this.attributeKey);for(const t of i.getRanges())for(const i of t.getItems())if(e.checkAttribute(i,this.attributeKey))return i.hasAttribute(this.attributeKey);return!1}}const s="bold";class r extends t.Plugin{static get pluginName(){return"BoldEditing"}init(){const t=this.editor;t.model.schema.extend("$text",{allowAttributes:s}),t.model.schema.setAttributeProperties(s,{isFormatting:!0,copyOnEnter:!0}),t.conversion.attributeToElement({model:s,view:"strong",upcastAlso:["b",t=>{const e=t.getStyle("font-weight");return e&&("bold"==e||Number(e)>=600)?{name:!0,styles:["font-weight"]}:null}]}),t.commands.add(s,new e(t,s)),t.keystrokes.set("CTRL+B",s)}}var o=i(273);const a="bold";class c extends t.Plugin{static get pluginName(){return"BoldUI"}init(){const e=this.editor,i=e.t;e.ui.componentFactory.add(a,(n=>{const s=e.commands.get(a),r=new o.ButtonView(n);return r.set({label:i("Bold"),icon:t.icons.bold,keystroke:"CTRL+B",tooltip:!0,isToggleable:!0}),r.bind("isOn","isEnabled").to(s,"value","isEnabled"),this.listenTo(r,"execute",(()=>{e.execute(a),e.editing.view.focus()})),r}))}}class l extends t.Plugin{static get requires(){return[r,c]}static get pluginName(){return"Bold"}}var u=i(181);const d="code";class g extends t.Plugin{static get pluginName(){return"CodeEditing"}static get requires(){return[u.TwoStepCaretMovement]}init(){const t=this.editor;t.model.schema.extend("$text",{allowAttributes:d}),t.model.schema.setAttributeProperties(d,{isFormatting:!0,copyOnEnter:!1}),t.conversion.attributeToElement({model:d,view:"code",upcastAlso:{styles:{"word-wrap":"break-word"}}}),t.commands.add(d,new e(t,d)),t.plugins.get(u.TwoStepCaretMovement).registerAttribute(d),(0,u.inlineHighlight)(t,d,"code","ck-code_selected")}}var m=i(62),p=i.n(m),h=i(55),b={injectType:"singletonStyleTag",attributes:{"data-cke":!0},insert:"head",singleton:!0};p()(h.Z,b);h.Z.locals;const v="code";class w extends t.Plugin{static get pluginName(){return"CodeUI"}init(){const t=this.editor,e=t.t;t.ui.componentFactory.add(v,(i=>{const n=t.commands.get(v),s=new o.ButtonView(i);return s.set({label:e("Code"),icon:'<svg viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="m12.5 5.7 5.2 3.9v1.3l-5.6 4c-.1.2-.3.2-.5.2-.3-.1-.6-.7-.6-1l.3-.4 4.7-3.5L11.5 7l-.2-.2c-.1-.3-.1-.6 0-.8.2-.2.5-.4.8-.4a.8.8 0 0 1 .4.1zm-5.2 0L2 9.6v1.3l5.6 4c.*******.5.2.3-.1.7-.7.6-1 0-.1 0-.3-.2-.4l-5-3.5L8.2 7l.2-.2c.1-.3.1-.6 0-.8-.2-.2-.5-.4-.8-.4a.8.8 0 0 0-.3.1z"/></svg>',tooltip:!0,isToggleable:!0}),s.bind("isOn","isEnabled").to(n,"value","isEnabled"),this.listenTo(s,"execute",(()=>{t.execute(v),t.editing.view.focus()})),s}))}}class f extends t.Plugin{static get requires(){return[g,w]}static get pluginName(){return"Code"}}const x="italic";class y extends t.Plugin{static get pluginName(){return"ItalicEditing"}init(){const t=this.editor;t.model.schema.extend("$text",{allowAttributes:x}),t.model.schema.setAttributeProperties(x,{isFormatting:!0,copyOnEnter:!0}),t.conversion.attributeToElement({model:x,view:"i",upcastAlso:["em",{styles:{"font-style":"italic"}}]}),t.commands.add(x,new e(t,x)),t.keystrokes.set("CTRL+I",x)}}const S="italic";class E extends t.Plugin{static get pluginName(){return"ItalicUI"}init(){const t=this.editor,e=t.t;t.ui.componentFactory.add(S,(i=>{const n=t.commands.get(S),s=new o.ButtonView(i);return s.set({label:e("Italic"),icon:'<svg viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="m9.586 14.633.021.004c-.036.335.095.655.393.962.**************.274.201h1.474a.6.6 0 1 1 0 1.2H5.304a.6.6 0 0 1 0-1.2h1.15c.474-.07.809-.182 1.005-.334.157-.122.291-.32.404-.597l2.416-9.55a1.053 1.053 0 0 0-.281-.823 1.12 1.12 0 0 0-.442-.296H8.15a.6.6 0 0 1 0-1.2h6.443a.6.6 0 1 1 0 1.2h-1.195c-.376.056-.65.155-.823.296-.215.175-.423.439-.623.79l-2.366 9.347z"/></svg>',keystroke:"CTRL+I",tooltip:!0,isToggleable:!0}),s.bind("isOn","isEnabled").to(n,"value","isEnabled"),this.listenTo(s,"execute",(()=>{t.execute(S),t.editing.view.focus()})),s}))}}class T extends t.Plugin{static get requires(){return[y,E]}static get pluginName(){return"Italic"}}const k="strikethrough";class A extends t.Plugin{static get pluginName(){return"StrikethroughEditing"}init(){const t=this.editor;t.model.schema.extend("$text",{allowAttributes:k}),t.model.schema.setAttributeProperties(k,{isFormatting:!0,copyOnEnter:!0}),t.conversion.attributeToElement({model:k,view:"s",upcastAlso:["del","strike",{styles:{"text-decoration":"line-through"}}]}),t.commands.add(k,new e(t,k)),t.keystrokes.set("CTRL+SHIFT+X","strikethrough")}}const C="strikethrough";class I extends t.Plugin{static get pluginName(){return"StrikethroughUI"}init(){const t=this.editor,e=t.t;t.ui.componentFactory.add(C,(i=>{const n=t.commands.get(C),s=new o.ButtonView(i);return s.set({label:e("Strikethrough"),icon:'<svg viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="M7 16.4c-.8-.4-1.5-.9-2.2-1.5a.6.6 0 0 1-.2-.5l.3-.6h1c1 1.2 2.1 1.7 3.7 1.7 1 0 1.8-.3 2.3-.6.6-.4.6-1.2.6-1.3.2-1.2-.9-2.1-.9-2.1h2.1c.3.7.4 1.2.4 1.7v.8l-.6 1.2c-.6.8-1.1 1-1.6 1.2a6 6 0 0 1-2.4.6c-1 0-1.8-.3-2.5-.6zM6.8 9 6 8.3c-.4-.5-.5-.8-.5-1.6 0-.7.1-1.3.5-1.8.4-.6 1-1 1.6-1.3a6.3 6.3 0 0 1 4.7 0 4 4 0 0 1 1.7 1l.3.7c0 .1.2.4-.2.7-.4.2-.9.1-1 0a3 3 0 0 0-1.2-1c-.4-.2-1-.3-2-.4-.7 0-1.4.2-2 .6-.8.6-1 .8-1 1.5 0 .8.5 1 1.2 ******* 1.1.7 1.9 1H6.8z"/><path d="M3 10.5V9h14v1.5z"/></svg>',keystroke:"CTRL+SHIFT+X",tooltip:!0,isToggleable:!0}),s.bind("isOn","isEnabled").to(n,"value","isEnabled"),this.listenTo(s,"execute",(()=>{t.execute(C),t.editing.view.focus()})),s}))}}class N extends t.Plugin{static get requires(){return[A,I]}static get pluginName(){return"Strikethrough"}}const P="subscript";class B extends t.Plugin{static get pluginName(){return"SubscriptEditing"}init(){const t=this.editor;t.model.schema.extend("$text",{allowAttributes:P}),t.model.schema.setAttributeProperties(P,{isFormatting:!0,copyOnEnter:!0}),t.conversion.attributeToElement({model:P,view:"sub",upcastAlso:[{styles:{"vertical-align":"sub"}}]}),t.commands.add(P,new e(t,P))}}const O="subscript";class U extends t.Plugin{static get pluginName(){return"SubscriptUI"}init(){const t=this.editor,e=t.t;t.ui.componentFactory.add(O,(i=>{const n=t.commands.get(O),s=new o.ButtonView(i);return s.set({label:e("Subscript"),icon:'<svg viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="m7.03 10.349 3.818-3.819a.8.8 0 1 1 1.132 1.132L8.16 11.48l3.819 3.818a.8.8 0 1 1-1.132 1.132L7.03 12.61l-3.818 3.82a.8.8 0 1 1-1.132-1.132L5.9 11.48 2.08 7.662A.8.8 0 1 1 3.212 6.53l3.818 3.82zm8.147 7.829h2.549c.254 0 .447.05.58.152a.49.49 0 0 1 .201.413.54.54 0 0 1-.159.393c-.105.108-.266.162-.48.162h-3.594c-.245 0-.435-.066-.572-.197a.621.621 0 0 1-.205-.463c0-.114.044-.265.132-.453a1.62 1.62 0 0 1 .288-.444c.433-.436.824-.81 1.172-1.122.348-.312.597-.517.747-.615.267-.183.49-.368.667-.553.177-.185.312-.375.405-.57.093-.194.139-.384.139-.57a1.008 1.008 0 0 0-.554-.917 1.197 1.197 0 0 0-.56-.133c-.426 0-.761.182-1.005.546a2.332 2.332 0 0 0-.164.39 1.609 1.609 0 0 1-.258.488c-.096.114-.237.17-.423.17a.558.558 0 0 1-.405-.156.568.568 0 0 1-.161-.427c0-.218.05-.446.151-.683.101-.238.252-.453.452-.646s.454-.349.762-.467a2.998 2.998 0 0 1 1.081-.178c.498 0 .923.076 1.274.228a1.916 1.916 0 0 1 1.004 1.032 1.984 1.984 0 0 1-.156 1.794c-.2.32-.405.572-.613.754-.208.182-.558.468-1.048.857-.49.39-.826.691-1.008.906a2.703 2.703 0 0 0-.24.309z"/></svg>',tooltip:!0,isToggleable:!0}),s.bind("isOn","isEnabled").to(n,"value","isEnabled"),this.listenTo(s,"execute",(()=>{t.execute(O),t.editing.view.focus()})),s}))}}class F extends t.Plugin{static get requires(){return[B,U]}static get pluginName(){return"Subscript"}}const L="superscript";class M extends t.Plugin{static get pluginName(){return"SuperscriptEditing"}init(){const t=this.editor;t.model.schema.extend("$text",{allowAttributes:L}),t.model.schema.setAttributeProperties(L,{isFormatting:!0,copyOnEnter:!0}),t.conversion.attributeToElement({model:L,view:"sup",upcastAlso:[{styles:{"vertical-align":"super"}}]}),t.commands.add(L,new e(t,L))}}const R="superscript";class V extends t.Plugin{static get pluginName(){return"SuperscriptUI"}init(){const t=this.editor,e=t.t;t.ui.componentFactory.add(R,(i=>{const n=t.commands.get(R),s=new o.ButtonView(i);return s.set({label:e("Superscript"),icon:'<svg viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="M15.677 8.678h2.549c.254 0 .447.05.58.152a.49.49 0 0 1 .201.413.54.54 0 0 1-.159.393c-.105.108-.266.162-.48.162h-3.594c-.245 0-.435-.066-.572-.197a.621.621 0 0 1-.205-.463c0-.114.044-.265.132-.453a1.62 1.62 0 0 1 .288-.444c.433-.436.824-.81 1.172-1.122.348-.312.597-.517.747-.615.267-.183.49-.368.667-.553.177-.185.312-.375.405-.57.093-.194.139-.384.139-.57a1.008 1.008 0 0 0-.554-.917 1.197 1.197 0 0 0-.56-.133c-.426 0-.761.182-1.005.546a2.332 2.332 0 0 0-.164.39 1.609 1.609 0 0 1-.258.488c-.096.114-.237.17-.423.17a.558.558 0 0 1-.405-.156.568.568 0 0 1-.161-.427c0-.218.05-.446.151-.683.101-.238.252-.453.452-.646s.454-.349.762-.467a2.998 2.998 0 0 1 1.081-.178c.498 0 .923.076 1.274.228a1.916 1.916 0 0 1 1.004 1.032 1.984 1.984 0 0 1-.156 1.794c-.2.32-.405.572-.613.754-.208.182-.558.468-1.048.857-.49.39-.826.691-1.008.906a2.703 2.703 0 0 0-.24.309zM7.03 10.349l3.818-3.819a.8.8 0 1 1 1.132 1.132L8.16 11.48l3.819 3.818a.8.8 0 1 1-1.132 1.132L7.03 12.61l-3.818 3.82a.8.8 0 1 1-1.132-1.132L5.9 11.48 2.08 7.662A.8.8 0 1 1 3.212 6.53l3.818 3.82z"/></svg>',tooltip:!0,isToggleable:!0}),s.bind("isOn","isEnabled").to(n,"value","isEnabled"),this.listenTo(s,"execute",(()=>{t.execute(R),t.editing.view.focus()})),s}))}}class j extends t.Plugin{static get requires(){return[M,V]}static get pluginName(){return"Superscript"}}const K="underline";class z extends t.Plugin{static get pluginName(){return"UnderlineEditing"}init(){const t=this.editor;t.model.schema.extend("$text",{allowAttributes:K}),t.model.schema.setAttributeProperties(K,{isFormatting:!0,copyOnEnter:!0}),t.conversion.attributeToElement({model:K,view:"u",upcastAlso:{styles:{"text-decoration":"underline"}}}),t.commands.add(K,new e(t,K)),t.keystrokes.set("CTRL+U","underline")}}const _="underline";class q extends t.Plugin{static get pluginName(){return"UnderlineUI"}init(){const t=this.editor,e=t.t;t.ui.componentFactory.add(_,(i=>{const n=t.commands.get(_),s=new o.ButtonView(i);return s.set({label:e("Underline"),icon:'<svg viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="M3 18v-1.5h14V18zm2.2-8V3.6c0-.4.4-.6.8-.6.3 0 .7.2.7.6v6.2c0 2 1.3 2.8 3.2 2.8 1.9 0 3.4-.9 3.4-2.9V3.6c0-.3.4-.5.8-.5.3 0 .7.2.7.5V10c0 2.7-2.2 4-4.9 4-2.6 0-4.7-1.2-4.7-4z"/></svg>',keystroke:"CTRL+U",tooltip:!0,isToggleable:!0}),s.bind("isOn","isEnabled").to(n,"value","isEnabled"),this.listenTo(s,"execute",(()=>{t.execute(_),t.editing.view.focus()})),s}))}}class H extends t.Plugin{static get requires(){return[z,q]}static get pluginName(){return"Underline"}}})(),(window.CKEditor5=window.CKEditor5||{}).basicStyles=n})();;
/* @license GNU-GPL-2.0-or-later https://www.drupal.org/licensing/faq */
!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.CKEditor5=t():(e.CKEditor5=e.CKEditor5||{},e.CKEditor5.drupalEmphasis=t())}(globalThis,(()=>(()=>{var e={"ckeditor5/src/core.js":(e,t,r)=>{e.exports=r("dll-reference CKEditor5.dll")("./src/core.js")},"dll-reference CKEditor5.dll":e=>{"use strict";e.exports=CKEditor5.dll}},t={};function r(o){var i=t[o];if(void 0!==i)return i.exports;var s=t[o]={exports:{}};return e[o](s,s.exports,r),s.exports}r.d=(e,t)=>{for(var o in t)r.o(t,o)&&!r.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})},r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t);var o={};return(()=>{"use strict";r.d(o,{default:()=>n});var e=r("ckeditor5/src/core.js");class t extends e.Plugin{static get pluginName(){return"DrupalEmphasisEditing"}init(){this.editor.conversion.for("downcast").attributeToElement({model:"italic",view:"em",converterPriority:"high"})}}const i=t;class s extends e.Plugin{static get requires(){return[i]}static get pluginName(){return"DrupalEmphasis"}}const n={DrupalEmphasis:s}})(),o=o.default})()));;
/* @license GNU-GPL-2.0-or-later https://raw.githubusercontent.com/ckeditor/ckeditor5/v40.2.0/LICENSE.md */
!function(e){const t=e.en=e.en||{};t.dictionary=Object.assign(t.dictionary||{},{"Choose heading":"Choose heading",Heading:"Heading","Heading 1":"Heading 1","Heading 2":"Heading 2","Heading 3":"Heading 3","Heading 4":"Heading 4","Heading 5":"Heading 5","Heading 6":"Heading 6",Paragraph:"Paragraph","Type or paste your content here.":"Type or paste your content here.","Type your title":"Type your title"})}(window.CKEDITOR_TRANSLATIONS||(window.CKEDITOR_TRANSLATIONS={})),
/*!
 * @license Copyright (c) 2003-2023, CKSource Holding sp. z o.o. All rights reserved.
 * For licensing, see LICENSE.md.
 */(()=>{var e={159:(e,t,n)=>{"use strict";n.d(t,{Z:()=>a});var o=n(609),i=n.n(o)()((function(e){return e[1]}));i.push([e.id,".ck.ck-heading_heading1{font-size:20px}.ck.ck-heading_heading2{font-size:17px}.ck.ck-heading_heading3{font-size:14px}.ck[class*=ck-heading_heading]{font-weight:700}.ck.ck-dropdown.ck-heading-dropdown .ck-dropdown__button .ck-button__label{width:8em}.ck.ck-dropdown.ck-heading-dropdown .ck-dropdown__panel .ck-list__item{min-width:18em}",""]);const a=i},609:e=>{"use strict";e.exports=function(e){var t=[];return t.toString=function(){return this.map((function(t){var n=e(t);return t[2]?"@media ".concat(t[2]," {").concat(n,"}"):n})).join("")},t.i=function(e,n,o){"string"==typeof e&&(e=[[null,e,""]]);var i={};if(o)for(var a=0;a<this.length;a++){var r=this[a][0];null!=r&&(i[r]=!0)}for(var s=0;s<e.length;s++){var d=[].concat(e[s]);o&&i[d[0]]||(n&&(d[2]?d[2]="".concat(n," and ").concat(d[2]):d[2]=n),t.push(d))}},t}},62:(e,t,n)=>{"use strict";var o,i=function(){return void 0===o&&(o=Boolean(window&&document&&document.all&&!window.atob)),o},a=function(){var e={};return function(t){if(void 0===e[t]){var n=document.querySelector(t);if(window.HTMLIFrameElement&&n instanceof window.HTMLIFrameElement)try{n=n.contentDocument.head}catch(e){n=null}e[t]=n}return e[t]}}(),r=[];function s(e){for(var t=-1,n=0;n<r.length;n++)if(r[n].identifier===e){t=n;break}return t}function d(e,t){for(var n={},o=[],i=0;i<e.length;i++){var a=e[i],d=t.base?a[0]+t.base:a[0],c=n[d]||0,l="".concat(d," ").concat(c);n[d]=c+1;var h=s(l),g={css:a[1],media:a[2],sourceMap:a[3]};-1!==h?(r[h].references++,r[h].updater(g)):r.push({identifier:l,updater:f(g,t),references:1}),o.push(l)}return o}function c(e){var t=document.createElement("style"),o=e.attributes||{};if(void 0===o.nonce){var i=n.nc;i&&(o.nonce=i)}if(Object.keys(o).forEach((function(e){t.setAttribute(e,o[e])})),"function"==typeof e.insert)e.insert(t);else{var r=a(e.insert||"head");if(!r)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");r.appendChild(t)}return t}var l,h=(l=[],function(e,t){return l[e]=t,l.filter(Boolean).join("\n")});function g(e,t,n,o){var i=n?"":o.media?"@media ".concat(o.media," {").concat(o.css,"}"):o.css;if(e.styleSheet)e.styleSheet.cssText=h(t,i);else{var a=document.createTextNode(i),r=e.childNodes;r[t]&&e.removeChild(r[t]),r.length?e.insertBefore(a,r[t]):e.appendChild(a)}}function m(e,t,n){var o=n.css,i=n.media,a=n.sourceMap;if(i?e.setAttribute("media",i):e.removeAttribute("media"),a&&"undefined"!=typeof btoa&&(o+="\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(encodeURIComponent(JSON.stringify(a))))," */")),e.styleSheet)e.styleSheet.cssText=o;else{for(;e.firstChild;)e.removeChild(e.firstChild);e.appendChild(document.createTextNode(o))}}var u=null,p=0;function f(e,t){var n,o,i;if(t.singleton){var a=p++;n=u||(u=c(t)),o=g.bind(null,n,a,!1),i=g.bind(null,n,a,!0)}else n=c(t),o=m.bind(null,n,t),i=function(){!function(e){if(null===e.parentNode)return!1;e.parentNode.removeChild(e)}(n)};return o(e),function(t){if(t){if(t.css===e.css&&t.media===e.media&&t.sourceMap===e.sourceMap)return;o(e=t)}else i()}}e.exports=function(e,t){(t=t||{}).singleton||"boolean"==typeof t.singleton||(t.singleton=i());var n=d(e=e||[],t);return function(e){if(e=e||[],"[object Array]"===Object.prototype.toString.call(e)){for(var o=0;o<n.length;o++){var i=s(n[o]);r[i].references--}for(var a=d(e,t),c=0;c<n.length;c++){var l=s(n[c]);0===r[l].references&&(r[l].updater(),r.splice(l,1))}n=a}}}},704:(e,t,n)=>{e.exports=n(79)("./src/core.js")},492:(e,t,n)=>{e.exports=n(79)("./src/engine.js")},747:(e,t,n)=>{e.exports=n(79)("./src/paragraph.js")},273:(e,t,n)=>{e.exports=n(79)("./src/ui.js")},209:(e,t,n)=>{e.exports=n(79)("./src/utils.js")},79:e=>{"use strict";e.exports=CKEditor5.dll}},t={};function n(o){var i=t[o];if(void 0!==i)return i.exports;var a=t[o]={id:o,exports:{}};return e[o](a,a.exports,n),a.exports}n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var o in t)n.o(t,o)&&!n.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.nc=void 0;var o={};(()=>{"use strict";n.r(o),n.d(o,{Heading:()=>f,HeadingButtonsUI:()=>w,HeadingEditing:()=>d,HeadingUI:()=>p,Title:()=>x});var e=n(704),t=n(747),i=n(209);class a extends e.Command{constructor(e,t){super(e),this.modelElements=t}refresh(){const e=(0,i.first)(this.editor.model.document.selection.getSelectedBlocks());this.value=!!e&&this.modelElements.includes(e.name)&&e.name,this.isEnabled=!!e&&this.modelElements.some((t=>r(e,t,this.editor.model.schema)))}execute(e){const t=this.editor.model,n=t.document,o=e.value;t.change((e=>{const i=Array.from(n.selection.getSelectedBlocks()).filter((e=>r(e,o,t.schema)));for(const t of i)t.is("element",o)||e.rename(t,o)}))}}function r(e,t,n){return n.checkChild(e.parent,t)&&!n.isObject(e)}const s="paragraph";class d extends e.Plugin{static get pluginName(){return"HeadingEditing"}constructor(e){super(e),e.config.define("heading",{options:[{model:"paragraph",title:"Paragraph",class:"ck-heading_paragraph"},{model:"heading1",view:"h2",title:"Heading 1",class:"ck-heading_heading1"},{model:"heading2",view:"h3",title:"Heading 2",class:"ck-heading_heading2"},{model:"heading3",view:"h4",title:"Heading 3",class:"ck-heading_heading3"}]})}static get requires(){return[t.Paragraph]}init(){const e=this.editor,t=e.config.get("heading.options"),n=[];for(const o of t)"paragraph"!==o.model&&(e.model.schema.register(o.model,{inheritAllFrom:"$block"}),e.conversion.elementToElement(o),n.push(o.model));this._addDefaultH1Conversion(e),e.commands.add("heading",new a(e,n))}afterInit(){const e=this.editor,t=e.commands.get("enter"),n=e.config.get("heading.options");t&&this.listenTo(t,"afterExecute",((t,o)=>{const i=e.model.document.selection.getFirstPosition().parent;n.some((e=>i.is("element",e.model)))&&!i.is("element",s)&&0===i.childCount&&o.writer.rename(i,s)}))}_addDefaultH1Conversion(e){e.conversion.for("upcast").elementToElement({model:"heading1",view:"h1",converterPriority:i.priorities.low+1})}}var c=n(273);function l(e){const t=e.t,n={Paragraph:t("Paragraph"),"Heading 1":t("Heading 1"),"Heading 2":t("Heading 2"),"Heading 3":t("Heading 3"),"Heading 4":t("Heading 4"),"Heading 5":t("Heading 5"),"Heading 6":t("Heading 6")};return e.config.get("heading.options").map((e=>{const t=n[e.title];return t&&t!=e.title&&(e.title=t),e}))}var h=n(62),g=n.n(h),m=n(159),u={injectType:"singletonStyleTag",attributes:{"data-cke":!0},insert:"head",singleton:!0};g()(m.Z,u);m.Z.locals;class p extends e.Plugin{static get pluginName(){return"HeadingUI"}init(){const e=this.editor,t=e.t,n=l(e),o=t("Choose heading"),a=t("Heading");e.ui.componentFactory.add("heading",(t=>{const r={},s=new i.Collection,d=e.commands.get("heading"),l=e.commands.get("paragraph"),h=[d];for(const e of n){const t={type:"button",model:new c.Model({label:e.title,class:e.class,role:"menuitemradio",withText:!0})};"paragraph"===e.model?(t.model.bind("isOn").to(l,"value"),t.model.set("commandName","paragraph"),h.push(l)):(t.model.bind("isOn").to(d,"value",(t=>t===e.model)),t.model.set({commandName:"heading",commandValue:e.model})),s.add(t),r[e.model]=e.title}const g=(0,c.createDropdown)(t);return(0,c.addListToDropdown)(g,s,{ariaLabel:a,role:"menu"}),g.buttonView.set({ariaLabel:a,ariaLabelledBy:void 0,isOn:!1,withText:!0,tooltip:a}),g.extendTemplate({attributes:{class:["ck-heading-dropdown"]}}),g.bind("isEnabled").toMany(h,"isEnabled",((...e)=>e.some((e=>e)))),g.buttonView.bind("label").to(d,"value",l,"value",((e,t)=>{const n=e||t&&"paragraph";return"boolean"==typeof n?o:r[n]?r[n]:o})),this.listenTo(g,"execute",(t=>{const{commandName:n,commandValue:o}=t.source;e.execute(n,o?{value:o}:void 0),e.editing.view.focus()})),g}))}}class f extends e.Plugin{static get requires(){return[d,p]}static get pluginName(){return"Heading"}}const v={heading1:'<svg viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="M19 9v10h-2v-8h-2V9h4zM4 8.5h5V4a1 1 0 0 1 1-1h.5a1 1 0 0 1 1 1v11.5a1 1 0 0 1-1 1H10a1 1 0 0 1-1-1V11H4v4.5a1 1 0 0 1-1 1h-.5a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1H3a1 1 0 0 1 1 1v4.5z"/></svg>',heading2:'<svg viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="M3 8.5h5V4a1 1 0 0 1 1-1h.5a1 1 0 0 1 1 1v11.5a1 1 0 0 1-1 1H9a1 1 0 0 1-1-1V11H3v4.5a1 1 0 0 1-1 1h-.5a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1H2a1 1 0 0 1 1 1v4.5zm16.076 8.343V18.5h-6.252c.067-.626.27-1.22.61-1.78.338-.561 1.006-1.305 2.005-2.232.804-.749 1.297-1.257 1.479-1.523.245-.368.368-.732.368-1.092 0-.398-.107-.703-.32-.917-.214-.214-.51-.32-.886-.32-.372 0-.669.111-.889.336-.22.224-.347.596-.38 1.117l-1.778-.178c.106-.982.438-1.686.997-2.114.558-.427 1.257-.64 2.095-.64.918 0 1.64.247 2.164.742.525.495.787 1.11.787 1.847 0 .419-.075.818-.225 1.197-.15.378-.388.775-.714 1.19-.216.275-.605.67-1.168 1.187-.563.516-.92.859-1.07 1.028a3.11 3.11 0 0 0-.365.495h3.542z"/></svg>',heading3:'<svg viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="M3 8.5h5V4a1 1 0 0 1 1-1h.5a1 1 0 0 1 1 1v11.5a1 1 0 0 1-1 1H9a1 1 0 0 1-1-1V11H3v4.5a1 1 0 0 1-1 1h-.5a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1H2a1 1 0 0 1 1 1v4.5zm9.989 7.53 1.726-.209c.055.44.203.777.445 1.01.24.232.533.349.876.349.368 0 .678-.14.93-.42.251-.279.377-.655.377-1.13 0-.448-.12-.803-.362-1.066a1.153 1.153 0 0 0-.882-.393c-.228 0-.501.044-.819.133l.197-1.453c.482.012.85-.092 1.105-.315.253-.222.38-.517.38-.885 0-.313-.093-.563-.279-.75-.186-.185-.434-.278-.743-.278a1.07 1.07 0 0 0-.78.317c-.216.212-.347.52-.394.927l-1.644-.28c.114-.562.287-1.012.517-1.348.231-.337.553-.601.965-.794a3.24 3.24 0 0 1 1.387-.289c.876 0 1.579.28 2.108.838.436.457.653.973.653 1.549 0 .817-.446 1.468-1.339 1.955.533.114.96.37 1.28.768.319.398.478.878.478 1.441 0 .817-.298 1.513-.895 2.088-.596.576-1.339.864-2.228.864-.842 0-1.54-.243-2.094-.727-.555-.485-.876-1.118-.965-1.901z"/></svg>',heading4:'<svg viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="M3.5 8.5h5V4a1 1 0 0 1 1-1h.5a1 1 0 0 1 1 1v11.5a1 1 0 0 1-1 1h-.5a1 1 0 0 1-1-1V11h-5v4.5a1 1 0 0 1-1 1H2a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h.5a1 1 0 0 1 1 1v4.5zm13.55 10v-1.873h-3.81v-1.561l4.037-5.91h1.498v5.904h1.156v1.567h-1.156V18.5H17.05zm0-3.44v-3.18l-2.14 3.18h2.14z"/></svg>',heading5:'<svg viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="M3.5 8.5h5V4a1 1 0 0 1 1-1h.5a1 1 0 0 1 1 1v11.5a1 1 0 0 1-1 1h-.5a1 1 0 0 1-1-1V11h-5v4.5a1 1 0 0 1-1 1H2a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h.5a1 1 0 0 1 1 1v4.5zm9.578 7.607 1.777-.184c.05.402.201.72.45.955a1.223 1.223 0 0 0 1.81-.101c.258-.303.387-.759.387-1.368 0-.572-.128-1-.384-1.286-.256-.285-.59-.428-1-.428-.512 0-.971.226-1.377.679l-1.448-.21.915-4.843h4.716v1.67H15.56l-.28 1.58a2.697 2.697 0 0 1 1.219-.298 2.68 2.68 0 0 1 2.012.863c.55.576.825 1.323.825 2.241a3.36 3.36 0 0 1-.666 2.05c-.605.821-1.445 1.232-2.52 1.232-.86 0-1.56-.23-2.101-.692-.542-.461-.866-1.081-.971-1.86z"/></svg>',heading6:'<svg viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="M3.5 8.5h5V4a1 1 0 0 1 1-1h.5a1 1 0 0 1 1 1v11.5a1 1 0 0 1-1 1h-.5a1 1 0 0 1-1-1V11h-5v4.5a1 1 0 0 1-1 1H2a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h.5a1 1 0 0 1 1 1v4.5zm15.595 2.973-1.726.19c-.043-.355-.153-.617-.33-.787-.178-.169-.409-.253-.692-.253-.377 0-.695.169-.956.507-.26.339-.424 1.043-.492 2.114.445-.525.997-.787 1.657-.787.745 0 1.383.284 1.914.85.531.568.797 1.3.797 2.197 0 .952-.28 1.716-.838 2.291-.559.576-1.276.864-2.152.864-.94 0-1.712-.365-2.317-1.095-.605-.73-.908-1.927-.908-3.59 0-1.705.316-2.935.946-3.688.63-.753 1.45-1.13 2.457-1.13.706 0 1.291.198 1.755.594.463.395.758.97.885 1.723zm-4.043 3.891c0 .58.133 1.028.4 1.343.266.315.57.473.914.473.33 0 .605-.13.825-.388.22-.258.33-.68.33-1.27 0-.604-.118-1.047-.355-1.329a1.115 1.115 0 0 0-.89-.422c-.342 0-.632.134-.869.403s-.355.666-.355 1.19z"/></svg>'};class w extends e.Plugin{init(){l(this.editor).filter((e=>"paragraph"!==e.model)).map((e=>this._createButton(e)))}_createButton(e){const t=this.editor;t.ui.componentFactory.add(e.model,(n=>{const o=new c.ButtonView(n),i=t.commands.get("heading");return o.label=e.title,o.icon=e.icon||v[e.model],o.tooltip=!0,o.isToggleable=!0,o.bind("isEnabled").to(i),o.bind("isOn").to(i,"value",(t=>t==e.model)),o.on("execute",(()=>{t.execute("heading",{value:e.model}),t.editing.view.focus()})),o}))}}var b=n(492);const y=new Set(["paragraph","heading1","heading2","heading3","heading4","heading5","heading6"]);class x extends e.Plugin{constructor(){super(...arguments),this._bodyPlaceholder=new Map}static get pluginName(){return"Title"}static get requires(){return["Paragraph"]}init(){const e=this.editor,t=e.model;t.schema.register("title",{isBlock:!0,allowIn:"$root"}),t.schema.register("title-content",{isBlock:!0,allowIn:"title",allowAttributes:["alignment"]}),t.schema.extend("$text",{allowIn:"title-content"}),t.schema.addAttributeCheck((e=>{if(e.endsWith("title-content $text"))return!1})),e.editing.mapper.on("modelToViewPosition",C(e.editing.view)),e.data.mapper.on("modelToViewPosition",C(e.editing.view)),e.conversion.for("downcast").elementToElement({model:"title-content",view:"h1"}),e.conversion.for("downcast").add((e=>e.on("insert:title",((e,t,n)=>{n.consumable.consume(t.item,e.name)})))),e.data.upcastDispatcher.on("element:h1",H,{priority:"high"}),e.data.upcastDispatcher.on("element:h2",H,{priority:"high"}),e.data.upcastDispatcher.on("element:h3",H,{priority:"high"}),t.document.registerPostFixer((e=>this._fixTitleContent(e))),t.document.registerPostFixer((e=>this._fixTitleElement(e))),t.document.registerPostFixer((e=>this._fixBodyElement(e))),t.document.registerPostFixer((e=>this._fixExtraParagraph(e))),this._attachPlaceholders(),this._attachTabPressHandling()}getTitle(e={}){const t=e.rootName?e.rootName:void 0,n=this._getTitleElement(t).getChild(0);return this.editor.data.stringify(n,e)}getBody(e={}){const t=this.editor,n=t.data,o=t.model,i=e.rootName?e.rootName:void 0,a=t.model.document.getRoot(i),r=t.editing.view,s=new b.DowncastWriter(r.document),d=o.createRangeIn(a),c=s.createDocumentFragment(),l=o.createPositionAfter(a.getChild(0)),h=o.createRange(l,o.createPositionAt(a,"end")),g=new Map;for(const e of o.markers){const t=h.getIntersection(e.getRange());t&&g.set(e.name,t)}return n.mapper.clearBindings(),n.mapper.bindElements(a,c),n.downcastDispatcher.convert(d,g,s,e),s.remove(s.createRangeOn(c.getChild(0))),t.data.processor.toData(c)}_getTitleElement(e){const t=this.editor.model.document.getRoot(e);for(const e of t.getChildren())if(T(e))return e}_fixTitleContent(e){let t=!1;for(const n of this.editor.model.document.getRootNames()){const o=this._getTitleElement(n);if(!o||1===o.maxOffset)continue;const i=Array.from(o.getChildren());i.shift();for(const t of i)e.move(e.createRangeOn(t),o,"after"),e.rename(t,"paragraph");t=!0}return t}_fixTitleElement(e){let t=!1;const n=this.editor.model;for(const o of this.editor.model.document.getRoots()){const i=Array.from(o.getChildren()).filter(T),a=i[0],r=o.getChild(0);if(r.is("element","title"))i.length>1&&(_(i,e,n),t=!0);else if(a||y.has(r.name))y.has(r.name)?P(r,e,n):e.move(e.createRangeOn(a),o,0),_(i,e,n),t=!0;else{const n=e.createElement("title");e.insert(n,o),e.insertElement("title-content",n),t=!0}}return t}_fixBodyElement(e){let t=!1;for(const n of this.editor.model.document.getRootNames()){const o=this.editor.model.document.getRoot(n);if(o.childCount<2){const i=e.createElement("paragraph");e.insert(i,o,1),this._bodyPlaceholder.set(n,i),t=!0}}return t}_fixExtraParagraph(e){let t=!1;for(const n of this.editor.model.document.getRootNames()){const o=this.editor.model.document.getRoot(n),i=this._bodyPlaceholder.get(n);k(i,o)&&(this._bodyPlaceholder.delete(n),e.remove(i),t=!0)}return t}_attachPlaceholders(){const e=this.editor,t=e.t,n=e.editing.view,o=e.sourceElement,i=e.config.get("title.placeholder")||t("Type your title"),a=e.config.get("placeholder")||o&&"textarea"===o.tagName.toLowerCase()&&o.getAttribute("placeholder")||t("Type or paste your content here.");e.editing.downcastDispatcher.on("insert:title-content",((e,t,o)=>{const a=o.mapper.toViewElement(t.item);a.placeholder=i,(0,b.enablePlaceholder)({view:n,element:a,keepOnFocus:!0})}));const r=new Map;n.document.registerPostFixer((e=>{let t=!1;for(const o of n.document.roots){if(o.isEmpty)continue;const n=o.getChild(1),i=r.get(o.rootName);n!==i&&(i&&((0,b.hidePlaceholder)(e,i),e.removeAttribute("data-placeholder",i)),e.setAttribute("data-placeholder",a,n),r.set(o.rootName,n),t=!0),t=(0,b.needsPlaceholder)(n,!0)&&2===o.childCount&&"p"===n.name?!!(0,b.showPlaceholder)(e,n)||t:!!(0,b.hidePlaceholder)(e,n)||t}return t}))}_attachTabPressHandling(){const e=this.editor,t=e.model;e.keystrokes.set("TAB",((e,n)=>{t.change((e=>{const o=t.document.selection,i=Array.from(o.getSelectedBlocks());if(1===i.length&&i[0].is("element","title-content")){const t=o.getFirstPosition().root.getChild(1);e.setSelection(t,0),n()}}))})),e.keystrokes.set("SHIFT + TAB",((n,o)=>{t.change((n=>{const a=t.document.selection;if(!a.isCollapsed)return;const r=(0,i.first)(a.getSelectedBlocks()),s=a.getFirstPosition(),d=e.model.document.getRoot(s.root.rootName),c=d.getChild(0);r===d.getChild(1)&&s.isAtStart&&(n.setSelection(c.getChild(0),0),o())}))}))}}function H(e,t,n){const o=t.modelCursor,i=t.viewItem;if(!o.isAtStart||!o.parent.is("element","$root"))return;if(!n.consumable.consume(i,{name:!0}))return;const a=n.writer,r=a.createElement("title"),s=a.createElement("title-content");a.append(s,r),a.insert(r,o),n.convertChildren(i,s),n.updateConversionResult(r,t)}function C(e){return(t,n)=>{const o=n.modelPosition.parent;if(!o.is("element","title"))return;const i=o.parent,a=n.mapper.toViewElement(i);n.viewPosition=e.createPositionAt(a,0),t.stop()}}function T(e){return e.is("element","title")}function P(e,t,n){const o=t.createElement("title");t.insert(o,e,"before"),t.insert(e,o,0),t.rename(e,"title-content"),n.schema.removeDisallowedAttributes([e],t)}function _(e,t,n){let o=!1;for(const i of e)0!==i.index&&(E(i,t,n),o=!0);return o}function E(e,t,n){const o=e.getChild(0);o.isEmpty?t.remove(e):(t.move(t.createRangeOn(o),e,"before"),t.rename(o,"paragraph"),t.remove(e),n.schema.removeDisallowedAttributes([o],t))}function k(e,t){return!(!e||!e.is("element","paragraph")||e.childCount)&&!(t.childCount<=2||t.getChild(t.childCount-1)!==e)}})(),(window.CKEditor5=window.CKEditor5||{}).heading=o})();;
/* @license GNU-GPL-2.0-or-later https://github.com/ckeditor/ckeditor5/blob/v35.1.0/LICENSE.md */
/*!
 * @license Copyright (c) 2003-2023, CKSource Holding sp. z o.o. All rights reserved.
 * For licensing, see LICENSE.md.
 */(()=>{var e={945:(e,r,t)=>{e.exports=t(79)("./src/clipboard.js")},704:(e,r,t)=>{e.exports=t(79)("./src/core.js")},331:(e,r,t)=>{e.exports=t(79)("./src/enter.js")},468:(e,r,t)=>{e.exports=t(79)("./src/select-all.js")},181:(e,r,t)=>{e.exports=t(79)("./src/typing.js")},254:(e,r,t)=>{e.exports=t(79)("./src/undo.js")},79:e=>{"use strict";e.exports=CKEditor5.dll}},r={};function t(s){var o=r[s];if(void 0!==o)return o.exports;var n=r[s]={exports:{}};return e[s](n,n.exports,t),n.exports}t.d=(e,r)=>{for(var s in r)t.o(r,s)&&!t.o(e,s)&&Object.defineProperty(e,s,{enumerable:!0,get:r[s]})},t.o=(e,r)=>Object.prototype.hasOwnProperty.call(e,r),t.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var s={};(()=>{"use strict";t.r(s),t.d(s,{Essentials:()=>a});var e=t(704),r=t(945),o=t(331),n=t(468),i=t(181),l=t(254);class a extends e.Plugin{static get requires(){return[r.Clipboard,o.Enter,n.SelectAll,o.ShiftEnter,i.Typing,l.Undo]}static get pluginName(){return"Essentials"}}})(),(window.CKEditor5=window.CKEditor5||{}).essentials=s})();;
/* @license GNU-GPL-2.0-or-later https://www.drupal.org/licensing/faq */
!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.CKEditor5=t():(e.CKEditor5=e.CKEditor5||{},e.CKEditor5.drupalHtmlEngine=t())}(globalThis,(()=>(()=>{var e={"ckeditor5/src/core.js":(e,t,n)=>{e.exports=n("dll-reference CKEditor5.dll")("./src/core.js")},"dll-reference CKEditor5.dll":e=>{"use strict";e.exports=CKEditor5.dll}},t={};function n(p){var r=t[p];if(void 0!==r)return r.exports;var s=t[p]={exports:{}};return e[p](s,s.exports,n),s.exports}n.d=(e,t)=>{for(var p in t)n.o(t,p)&&!n.o(e,p)&&Object.defineProperty(e,p,{enumerable:!0,get:t[p]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t);var p={};return(()=>{"use strict";n.d(p,{default:()=>o});var e=n("ckeditor5/src/core.js");class t{constructor(){this.chunks=[],this.selfClosingTags=["area","base","br","col","embed","hr","img","input","link","meta","param","source","track","wbr"]}build(){return this.chunks.join("")}appendNode(e){e.nodeType===Node.TEXT_NODE?this._appendText(e):e.nodeType===Node.ELEMENT_NODE?this._appendElement(e):e.nodeType===Node.DOCUMENT_FRAGMENT_NODE?this._appendChildren(e):e.nodeType===Node.COMMENT_NODE&&this._appendComment(e)}_appendElement(e){const t=e.nodeName.toLowerCase();this._append("<"),this._append(t),this._appendAttributes(e),this._append(">"),this.selfClosingTags.includes(t)||(this._appendChildren(e),this._append("</"),this._append(t),this._append(">"))}_appendChildren(e){Object.keys(e.childNodes).forEach((t=>{this.appendNode(e.childNodes[t])}))}_appendAttributes(e){Object.keys(e.attributes).forEach((t=>{this._append(" "),this._append(e.attributes[t].name),this._append('="'),this._append(this.constructor._escapeAttribute(e.attributes[t].value)),this._append('"')}))}_appendText(e){const t=document.implementation.createHTMLDocument("").createElement("p");t.textContent=e.textContent,this._append(t.innerHTML)}_appendComment(e){this._append("\x3c!--"),this._append(e.textContent),this._append("--\x3e")}_append(e){this.chunks.push(e)}static _escapeAttribute(e){return e.replace(/&/g,"&amp;").replace(/'/g,"&apos;").replace(/"/g,"&quot;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/\r\n/g,"&#13;").replace(/[\r\n]/g,"&#13;")}}class r{getHtml(e){const n=new t;return n.appendNode(e),n.build()}}class s extends e.Plugin{init(){this.editor.data.processor.htmlWriter=new r}static get pluginName(){return"DrupalHtmlEngine"}}const o={DrupalHtmlEngine:s}})(),p=p.default})()));;
/* @license GNU-GPL-2.0-or-later https://raw.githubusercontent.com/ckeditor/ckeditor5/v40.2.0/LICENSE.md */
!function(e){const t=e.en=e.en||{};t.dictionary=Object.assign(t.dictionary||{},{"Break text":"Break text","Caption for image: %0":"Caption for image: %0","Caption for the image":"Caption for the image","Centered image":"Centered image","Change image text alternative":"Change image text alternative","Enter image caption":"Enter image caption","Full size image":"Full size image","Image resize list":"Image resize list","Image toolbar":"Image toolbar","image widget":"image widget","In line":"In line",Insert:"Insert","Insert image":"Insert image","Insert image via URL":"Insert image via URL","Left aligned image":"Left aligned image",Original:"Original","Replace from computer":"Replace from computer","Replace image":"Replace image","Replace image from computer":"Replace image from computer","Resize image":"Resize image","Resize image to %0":"Resize image to %0","Resize image to the original size":"Resize image to the original size","Right aligned image":"Right aligned image","Side image":"Side image","Text alternative":"Text alternative",Update:"Update","Update image URL":"Update image URL","Upload failed":"Upload failed","Upload from computer":"Upload from computer","Upload image from computer":"Upload image from computer","Wrap text":"Wrap text"})}(window.CKEDITOR_TRANSLATIONS||(window.CKEDITOR_TRANSLATIONS={})),
/*!
 * @license Copyright (c) 2003-2023, CKSource Holding sp. z o.o. All rights reserved.
 * For licensing, see LICENSE.md.
 */(()=>{var e={487:(e,t,i)=>{"use strict";i.d(t,{Z:()=>a});var n=i(609),o=i.n(n)()((function(e){return e[1]}));o.push([e.id,'.ck-vertical-form .ck-button:after{bottom:-1px;content:"";position:absolute;right:-1px;top:-1px;width:0;z-index:1}.ck-vertical-form .ck-button:focus:after{display:none}@media screen and (max-width:600px){.ck.ck-responsive-form .ck-button:after{bottom:-1px;content:"";position:absolute;right:-1px;top:-1px;width:0;z-index:1}.ck.ck-responsive-form .ck-button:focus:after{display:none}}.ck-vertical-form>.ck-button:nth-last-child(2):after{border-right:1px solid var(--ck-color-base-border)}.ck.ck-responsive-form{padding:var(--ck-spacing-large)}.ck.ck-responsive-form:focus{outline:none}[dir=ltr] .ck.ck-responsive-form>:not(:first-child),[dir=rtl] .ck.ck-responsive-form>:not(:last-child){margin-left:var(--ck-spacing-standard)}@media screen and (max-width:600px){.ck.ck-responsive-form{padding:0;width:calc(var(--ck-input-width)*.8)}.ck.ck-responsive-form .ck-labeled-field-view{margin:var(--ck-spacing-large) var(--ck-spacing-large) 0}.ck.ck-responsive-form .ck-labeled-field-view .ck-input-text{min-width:0;width:100%}.ck.ck-responsive-form .ck-labeled-field-view .ck-labeled-field-view__error{white-space:normal}.ck.ck-responsive-form>.ck-button:nth-last-child(2):after{border-right:1px solid var(--ck-color-base-border)}.ck.ck-responsive-form>.ck-button:last-child,.ck.ck-responsive-form>.ck-button:nth-last-child(2){border-radius:0;margin-top:var(--ck-spacing-large);padding:var(--ck-spacing-standard)}.ck.ck-responsive-form>.ck-button:last-child:not(:focus),.ck.ck-responsive-form>.ck-button:nth-last-child(2):not(:focus){border-top:1px solid var(--ck-color-base-border)}[dir=ltr] .ck.ck-responsive-form>.ck-button:last-child,[dir=ltr] .ck.ck-responsive-form>.ck-button:nth-last-child(2),[dir=rtl] .ck.ck-responsive-form>.ck-button:last-child,[dir=rtl] .ck.ck-responsive-form>.ck-button:nth-last-child(2){margin-left:0}[dir=rtl] .ck.ck-responsive-form>.ck-button:last-child:last-of-type,[dir=rtl] .ck.ck-responsive-form>.ck-button:nth-last-child(2):last-of-type{border-right:1px solid var(--ck-color-base-border)}}',""]);const a=o},417:(e,t,i)=>{"use strict";i.d(t,{Z:()=>a});var n=i(609),o=i.n(n)()((function(e){return e[1]}));o.push([e.id,".ck-content .image{clear:both;display:table;margin:.9em auto;min-width:50px;text-align:center}.ck-content .image img{display:block;height:auto;margin:0 auto;max-width:100%;min-width:100%}.ck-content .image-inline{align-items:flex-start;display:inline-flex;max-width:100%}.ck-content .image-inline picture{display:flex}.ck-content .image-inline img,.ck-content .image-inline picture{flex-grow:1;flex-shrink:1;max-width:100%}.ck.ck-editor__editable .image>figcaption.ck-placeholder:before{overflow:hidden;padding-left:inherit;padding-right:inherit;text-overflow:ellipsis;white-space:nowrap}.ck.ck-editor__editable .image{z-index:1}.ck.ck-editor__editable .image.ck-widget_selected{z-index:2}.ck.ck-editor__editable .image-inline{z-index:1}.ck.ck-editor__editable .image-inline.ck-widget_selected{z-index:2}.ck.ck-editor__editable .image-inline.ck-widget_selected ::selection{display:none}.ck.ck-editor__editable .image-inline img{height:auto}.ck.ck-editor__editable td .image-inline img,.ck.ck-editor__editable th .image-inline img{max-width:none}",""]);const a=o},86:(e,t,i)=>{"use strict";i.d(t,{Z:()=>a});var n=i(609),o=i.n(n)()((function(e){return e[1]}));o.push([e.id,":root{--ck-color-image-caption-background:#f7f7f7;--ck-color-image-caption-text:#333;--ck-color-image-caption-highligted-background:#fd0}.ck-content .image>figcaption{background-color:var(--ck-color-image-caption-background);caption-side:bottom;color:var(--ck-color-image-caption-text);display:table-caption;font-size:.75em;outline-offset:-1px;padding:.6em;word-break:break-word}.ck.ck-editor__editable .image>figcaption.image__caption_highlighted{animation:ck-image-caption-highlight .6s ease-out}@keyframes ck-image-caption-highlight{0%{background-color:var(--ck-color-image-caption-highligted-background)}to{background-color:var(--ck-color-image-caption-background)}}",""]);const a=o},554:(e,t,i)=>{"use strict";i.d(t,{Z:()=>a});var n=i(609),o=i.n(n)()((function(e){return e[1]}));o.push([e.id,".ck.ck-image-insert-url .ck-image-insert-url__action-row{display:grid;grid-template-columns:repeat(2,1fr)}:root{--ck-image-insert-insert-by-url-width:250px}.ck.ck-image-insert-url{--ck-input-width:100%}.ck.ck-image-insert-url .ck-image-insert-url__action-row{grid-column-gap:var(--ck-spacing-large);margin-top:var(--ck-spacing-large)}.ck.ck-image-insert-url .ck-image-insert-url__action-row .ck-button-cancel,.ck.ck-image-insert-url .ck-image-insert-url__action-row .ck-button-save{justify-content:center;min-width:auto}.ck.ck-image-insert-url .ck-image-insert-url__action-row .ck-button .ck-button__label{color:var(--ck-color-text)}.ck.ck-image-insert-form>.ck.ck-button{display:block;padding:var(--ck-list-button-padding);width:100%}[dir=ltr] .ck.ck-image-insert-form>.ck.ck-button{text-align:left}[dir=rtl] .ck.ck-image-insert-form>.ck.ck-button{text-align:right}.ck.ck-image-insert-form>.ck.ck-collapsible:not(:first-child){border-top:1px solid var(--ck-color-base-border)}.ck.ck-image-insert-form>.ck.ck-collapsible:not(:last-child){border-bottom:1px solid var(--ck-color-base-border)}.ck.ck-image-insert-form>.ck.ck-collapsible,.ck.ck-image-insert-form>.ck.ck-image-insert-url{min-width:var(--ck-image-insert-insert-by-url-width)}.ck.ck-image-insert-form>.ck.ck-image-insert-url{padding:var(--ck-spacing-large)}.ck.ck-image-insert-form:focus{outline:none}",""]);const a=o},390:(e,t,i)=>{"use strict";i.d(t,{Z:()=>a});var n=i(609),o=i.n(n)()((function(e){return e[1]}));o.push([e.id,".ck.ck-editor__editable img.image_placeholder{background-size:100% 100%}",""]);const a=o},968:(e,t,i)=>{"use strict";i.d(t,{Z:()=>a});var n=i(609),o=i.n(n)()((function(e){return e[1]}));o.push([e.id,".ck-content img.image_resized{height:auto}.ck-content .image.image_resized{box-sizing:border-box;display:block;max-width:100%}.ck-content .image.image_resized img{width:100%}.ck-content .image.image_resized>figcaption{display:block}.ck.ck-editor__editable td .image-inline.image_resized img,.ck.ck-editor__editable th .image-inline.image_resized img{max-width:100%}[dir=ltr] .ck.ck-button.ck-button_with-text.ck-resize-image-button .ck-button__icon{margin-right:var(--ck-spacing-standard)}[dir=rtl] .ck.ck-button.ck-button_with-text.ck-resize-image-button .ck-button__icon{margin-left:var(--ck-spacing-standard)}.ck.ck-dropdown .ck-button.ck-resize-image-button .ck-button__label{width:4em}",""]);const a=o},151:(e,t,i)=>{"use strict";i.d(t,{Z:()=>a});var n=i(609),o=i.n(n)()((function(e){return e[1]}));o.push([e.id,":root{--ck-image-style-spacing:1.5em;--ck-inline-image-style-spacing:calc(var(--ck-image-style-spacing)/2)}.ck-content .image-style-block-align-left,.ck-content .image-style-block-align-right{max-width:calc(100% - var(--ck-image-style-spacing))}.ck-content .image-style-align-left,.ck-content .image-style-align-right{clear:none}.ck-content .image-style-side{float:right;margin-left:var(--ck-image-style-spacing);max-width:50%}.ck-content .image-style-align-left{float:left;margin-right:var(--ck-image-style-spacing)}.ck-content .image-style-align-center{margin-left:auto;margin-right:auto}.ck-content .image-style-align-right{float:right;margin-left:var(--ck-image-style-spacing)}.ck-content .image-style-block-align-right{margin-left:auto;margin-right:0}.ck-content .image-style-block-align-left{margin-left:0;margin-right:auto}.ck-content p+.image-style-align-left,.ck-content p+.image-style-align-right,.ck-content p+.image-style-side{margin-top:0}.ck-content .image-inline.image-style-align-left,.ck-content .image-inline.image-style-align-right{margin-bottom:var(--ck-inline-image-style-spacing);margin-top:var(--ck-inline-image-style-spacing)}.ck-content .image-inline.image-style-align-left{margin-right:var(--ck-inline-image-style-spacing)}.ck-content .image-inline.image-style-align-right{margin-left:var(--ck-inline-image-style-spacing)}.ck.ck-splitbutton.ck-splitbutton_flatten.ck-splitbutton_open>.ck-splitbutton__action:not(.ck-disabled),.ck.ck-splitbutton.ck-splitbutton_flatten.ck-splitbutton_open>.ck-splitbutton__arrow:not(.ck-disabled),.ck.ck-splitbutton.ck-splitbutton_flatten.ck-splitbutton_open>.ck-splitbutton__arrow:not(.ck-disabled):not(:hover),.ck.ck-splitbutton.ck-splitbutton_flatten:hover>.ck-splitbutton__action:not(.ck-disabled),.ck.ck-splitbutton.ck-splitbutton_flatten:hover>.ck-splitbutton__arrow:not(.ck-disabled),.ck.ck-splitbutton.ck-splitbutton_flatten:hover>.ck-splitbutton__arrow:not(.ck-disabled):not(:hover){background-color:var(--ck-color-button-on-background)}.ck.ck-splitbutton.ck-splitbutton_flatten.ck-splitbutton_open>.ck-splitbutton__action:not(.ck-disabled):after,.ck.ck-splitbutton.ck-splitbutton_flatten.ck-splitbutton_open>.ck-splitbutton__arrow:not(.ck-disabled):after,.ck.ck-splitbutton.ck-splitbutton_flatten.ck-splitbutton_open>.ck-splitbutton__arrow:not(.ck-disabled):not(:hover):after,.ck.ck-splitbutton.ck-splitbutton_flatten:hover>.ck-splitbutton__action:not(.ck-disabled):after,.ck.ck-splitbutton.ck-splitbutton_flatten:hover>.ck-splitbutton__arrow:not(.ck-disabled):after,.ck.ck-splitbutton.ck-splitbutton_flatten:hover>.ck-splitbutton__arrow:not(.ck-disabled):not(:hover):after{display:none}.ck.ck-splitbutton.ck-splitbutton_flatten.ck-splitbutton_open:hover>.ck-splitbutton__action:not(.ck-disabled),.ck.ck-splitbutton.ck-splitbutton_flatten.ck-splitbutton_open:hover>.ck-splitbutton__arrow:not(.ck-disabled),.ck.ck-splitbutton.ck-splitbutton_flatten.ck-splitbutton_open:hover>.ck-splitbutton__arrow:not(.ck-disabled):not(:hover){background-color:var(--ck-color-button-on-hover-background)}",""]);const a=o},651:(e,t,i)=>{"use strict";i.d(t,{Z:()=>a});var n=i(609),o=i.n(n)()((function(e){return e[1]}));o.push([e.id,'.ck-image-upload-complete-icon{border-radius:50%;display:block;position:absolute;right:min(var(--ck-spacing-medium),6%);top:min(var(--ck-spacing-medium),6%);z-index:1}.ck-image-upload-complete-icon:after{content:"";position:absolute}:root{--ck-color-image-upload-icon:#fff;--ck-color-image-upload-icon-background:#008a00;--ck-image-upload-icon-size:20;--ck-image-upload-icon-width:2px;--ck-image-upload-icon-is-visible:clamp(0px,100% - 50px,1px)}.ck-image-upload-complete-icon{animation-delay:0ms,3s;animation-duration:.5s,.5s;animation-fill-mode:forwards,forwards;animation-name:ck-upload-complete-icon-show,ck-upload-complete-icon-hide;background:var(--ck-color-image-upload-icon-background);font-size:calc(1px*var(--ck-image-upload-icon-size));height:calc(var(--ck-image-upload-icon-is-visible)*var(--ck-image-upload-icon-size));opacity:0;overflow:hidden;width:calc(var(--ck-image-upload-icon-is-visible)*var(--ck-image-upload-icon-size))}.ck-image-upload-complete-icon:after{animation-delay:.5s;animation-duration:.5s;animation-fill-mode:forwards;animation-name:ck-upload-complete-icon-check;border-right:var(--ck-image-upload-icon-width) solid var(--ck-color-image-upload-icon);border-top:var(--ck-image-upload-icon-width) solid var(--ck-color-image-upload-icon);box-sizing:border-box;height:0;left:25%;opacity:0;top:50%;transform:scaleX(-1) rotate(135deg);transform-origin:left top;width:0}@keyframes ck-upload-complete-icon-show{0%{opacity:0}to{opacity:1}}@keyframes ck-upload-complete-icon-hide{0%{opacity:1}to{opacity:0}}@keyframes ck-upload-complete-icon-check{0%{height:0;opacity:1;width:0}33%{height:0;width:.3em}to{height:.45em;opacity:1;width:.3em}}',""]);const a=o},582:(e,t,i)=>{"use strict";i.d(t,{Z:()=>a});var n=i(609),o=i.n(n)()((function(e){return e[1]}));o.push([e.id,'.ck .ck-upload-placeholder-loader{align-items:center;display:flex;justify-content:center;left:0;position:absolute;top:0}.ck .ck-upload-placeholder-loader:before{content:"";position:relative}:root{--ck-color-upload-placeholder-loader:#b3b3b3;--ck-upload-placeholder-loader-size:32px;--ck-upload-placeholder-image-aspect-ratio:2.8}.ck .ck-image-upload-placeholder{margin:0;width:100%}.ck .ck-image-upload-placeholder.image-inline{width:calc(var(--ck-upload-placeholder-loader-size)*2*var(--ck-upload-placeholder-image-aspect-ratio))}.ck .ck-image-upload-placeholder img{aspect-ratio:var(--ck-upload-placeholder-image-aspect-ratio)}.ck .ck-upload-placeholder-loader{height:100%;width:100%}.ck .ck-upload-placeholder-loader:before{animation:ck-upload-placeholder-loader 1s linear infinite;border-radius:50%;border-right:2px solid transparent;border-top:3px solid var(--ck-color-upload-placeholder-loader);height:var(--ck-upload-placeholder-loader-size);width:var(--ck-upload-placeholder-loader-size)}@keyframes ck-upload-placeholder-loader{to{transform:rotate(1turn)}}',""]);const a=o},283:(e,t,i)=>{"use strict";i.d(t,{Z:()=>a});var n=i(609),o=i.n(n)()((function(e){return e[1]}));o.push([e.id,".ck.ck-editor__editable .image,.ck.ck-editor__editable .image-inline{position:relative}.ck.ck-editor__editable .image .ck-progress-bar,.ck.ck-editor__editable .image-inline .ck-progress-bar{left:0;position:absolute;top:0}.ck.ck-editor__editable .image-inline.ck-appear,.ck.ck-editor__editable .image.ck-appear{animation:fadeIn .7s}.ck.ck-editor__editable .image .ck-progress-bar,.ck.ck-editor__editable .image-inline .ck-progress-bar{background:var(--ck-color-upload-bar-background);height:2px;transition:width .1s;width:0}@keyframes fadeIn{0%{opacity:0}to{opacity:1}}",""]);const a=o},915:(e,t,i)=>{"use strict";i.d(t,{Z:()=>a});var n=i(609),o=i.n(n)()((function(e){return e[1]}));o.push([e.id,".ck.ck-text-alternative-form{display:flex;flex-direction:row;flex-wrap:nowrap}.ck.ck-text-alternative-form .ck-labeled-field-view{display:inline-block}.ck.ck-text-alternative-form .ck-label{display:none}@media screen and (max-width:600px){.ck.ck-text-alternative-form{flex-wrap:wrap}.ck.ck-text-alternative-form .ck-labeled-field-view{flex-basis:100%}.ck.ck-text-alternative-form .ck-button{flex-basis:50%}}",""]);const a=o},609:e=>{"use strict";e.exports=function(e){var t=[];return t.toString=function(){return this.map((function(t){var i=e(t);return t[2]?"@media ".concat(t[2]," {").concat(i,"}"):i})).join("")},t.i=function(e,i,n){"string"==typeof e&&(e=[[null,e,""]]);var o={};if(n)for(var a=0;a<this.length;a++){var s=this[a][0];null!=s&&(o[s]=!0)}for(var r=0;r<e.length;r++){var l=[].concat(e[r]);n&&o[l[0]]||(i&&(l[2]?l[2]="".concat(i," and ").concat(l[2]):l[2]=i),t.push(l))}},t}},62:(e,t,i)=>{"use strict";var n,o=function(){return void 0===n&&(n=Boolean(window&&document&&document.all&&!window.atob)),n},a=function(){var e={};return function(t){if(void 0===e[t]){var i=document.querySelector(t);if(window.HTMLIFrameElement&&i instanceof window.HTMLIFrameElement)try{i=i.contentDocument.head}catch(e){i=null}e[t]=i}return e[t]}}(),s=[];function r(e){for(var t=-1,i=0;i<s.length;i++)if(s[i].identifier===e){t=i;break}return t}function l(e,t){for(var i={},n=[],o=0;o<e.length;o++){var a=e[o],l=t.base?a[0]+t.base:a[0],c=i[l]||0,g="".concat(l," ").concat(c);i[l]=c+1;var m=r(g),d={css:a[1],media:a[2],sourceMap:a[3]};-1!==m?(s[m].references++,s[m].updater(d)):s.push({identifier:g,updater:f(d,t),references:1}),n.push(g)}return n}function c(e){var t=document.createElement("style"),n=e.attributes||{};if(void 0===n.nonce){var o=i.nc;o&&(n.nonce=o)}if(Object.keys(n).forEach((function(e){t.setAttribute(e,n[e])})),"function"==typeof e.insert)e.insert(t);else{var s=a(e.insert||"head");if(!s)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");s.appendChild(t)}return t}var g,m=(g=[],function(e,t){return g[e]=t,g.filter(Boolean).join("\n")});function d(e,t,i,n){var o=i?"":n.media?"@media ".concat(n.media," {").concat(n.css,"}"):n.css;if(e.styleSheet)e.styleSheet.cssText=m(t,o);else{var a=document.createTextNode(o),s=e.childNodes;s[t]&&e.removeChild(s[t]),s.length?e.insertBefore(a,s[t]):e.appendChild(a)}}function u(e,t,i){var n=i.css,o=i.media,a=i.sourceMap;if(o?e.setAttribute("media",o):e.removeAttribute("media"),a&&"undefined"!=typeof btoa&&(n+="\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(encodeURIComponent(JSON.stringify(a))))," */")),e.styleSheet)e.styleSheet.cssText=n;else{for(;e.firstChild;)e.removeChild(e.firstChild);e.appendChild(document.createTextNode(n))}}var p=null,h=0;function f(e,t){var i,n,o;if(t.singleton){var a=h++;i=p||(p=c(t)),n=d.bind(null,i,a,!1),o=d.bind(null,i,a,!0)}else i=c(t),n=u.bind(null,i,t),o=function(){!function(e){if(null===e.parentNode)return!1;e.parentNode.removeChild(e)}(i)};return n(e),function(t){if(t){if(t.css===e.css&&t.media===e.media&&t.sourceMap===e.sourceMap)return;n(e=t)}else o()}}e.exports=function(e,t){(t=t||{}).singleton||"boolean"==typeof t.singleton||(t.singleton=o());var i=l(e=e||[],t);return function(e){if(e=e||[],"[object Array]"===Object.prototype.toString.call(e)){for(var n=0;n<i.length;n++){var o=r(i[n]);s[o].references--}for(var a=l(e,t),c=0;c<i.length;c++){var g=r(i[c]);0===s[g].references&&(s[g].updater(),s.splice(g,1))}i=a}}}},945:(e,t,i)=>{e.exports=i(79)("./src/clipboard.js")},704:(e,t,i)=>{e.exports=i(79)("./src/core.js")},492:(e,t,i)=>{e.exports=i(79)("./src/engine.js")},181:(e,t,i)=>{e.exports=i(79)("./src/typing.js")},273:(e,t,i)=>{e.exports=i(79)("./src/ui.js")},254:(e,t,i)=>{e.exports=i(79)("./src/undo.js")},448:(e,t,i)=>{e.exports=i(79)("./src/upload.js")},209:(e,t,i)=>{e.exports=i(79)("./src/utils.js")},995:(e,t,i)=>{e.exports=i(79)("./src/widget.js")},79:e=>{"use strict";e.exports=CKEditor5.dll}},t={};function i(n){var o=t[n];if(void 0!==o)return o.exports;var a=t[n]={id:n,exports:{}};return e[n](a,a.exports,i),a.exports}i.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return i.d(t,{a:t}),t},i.d=(e,t)=>{for(var n in t)i.o(t,n)&&!i.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},i.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),i.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},i.nc=void 0;var n={};(()=>{"use strict";i.r(n),i.d(n,{AutoImage:()=>k,Image:()=>ee,ImageBlock:()=>Q,ImageBlockEditing:()=>M,ImageCaption:()=>re,ImageCaptionEditing:()=>ne,ImageCaptionUI:()=>oe,ImageCaptionUtils:()=>te,ImageEditing:()=>O,ImageInline:()=>Y,ImageInsert:()=>Ue,ImageInsertUI:()=>K,ImageInsertViaUrl:()=>Ve,ImageResize:()=>We,ImageResizeButtons:()=>Ne,ImageResizeEditing:()=>Re,ImageResizeHandles:()=>Fe,ImageSizeAttributes:()=>L,ImageStyle:()=>dt,ImageStyleEditing:()=>nt,ImageStyleUI:()=>lt,ImageTextAlternative:()=>V,ImageTextAlternativeEditing:()=>w,ImageTextAlternativeUI:()=>B,ImageToolbar:()=>ut,ImageUpload:()=>Ae,ImageUploadEditing:()=>xe,ImageUploadProgress:()=>Ie,ImageUploadUI:()=>de,ImageUtils:()=>h,PictureEditing:()=>pt});var e=i(704),t=i(945),o=i(492),a=i(254),s=i(181),r=i(209),l=i(995);function c(e){return e.createContainerElement("figure",{class:"image"},[e.createEmptyElement("img"),e.createSlot("children")])}function g(e,t){const i=e.plugins.get("ImageUtils"),n=e.plugins.has("ImageInlineEditing")&&e.plugins.has("ImageBlockEditing");return e=>{if(!i.isInlineImageView(e))return null;if(!n)return o(e);return("block"==e.getStyle("display")||e.findAncestor(i.isBlockImageView)?"imageBlock":"imageInline")!==t?null:o(e)};function o(e){const t={name:!0};return e.hasAttribute("src")&&(t.attributes=["src"]),t}}function m(e,t){const i=(0,r.first)(t.getSelectedBlocks());return!i||e.isObject(i)||i.isEmpty&&"listItem"!=i.name?"imageBlock":"imageInline"}function d(e){return e&&e.endsWith("px")?parseInt(e):null}function u(e){const t=d(e.getStyle("width")),i=d(e.getStyle("height"));return!(!t||!i)}const p=/^(image|image-inline)$/;class h extends e.Plugin{constructor(){super(...arguments),this._domEmitter=new((0,r.DomEmitterMixin)())}static get pluginName(){return"ImageUtils"}isImage(e){return this.isInlineImage(e)||this.isBlockImage(e)}isInlineImageView(e){return!!e&&e.is("element","img")}isBlockImageView(e){return!!e&&e.is("element","figure")&&e.hasClass("image")}insertImage(e={},t=null,i=null,n={}){const o=this.editor,a=o.model,s=a.document.selection,r=f(o,t||s,i);e={...Object.fromEntries(s.getAttributes()),...e};for(const t in e)a.schema.checkAttribute(r,t)||delete e[t];return a.change((i=>{const{setImageSizes:o=!0}=n,s=i.createElement(r,e);return a.insertObject(s,t,null,{setSelection:"on",findOptimalPosition:t||"imageInline"==r?void 0:"auto"}),s.parent?(o&&this.setImageNaturalSizeAttributes(s),s):null}))}setImageNaturalSizeAttributes(e){const t=e.getAttribute("src");t&&(e.getAttribute("width")||e.getAttribute("height")||this.editor.model.change((i=>{const n=new r.global.window.Image;this._domEmitter.listenTo(n,"load",(()=>{e.getAttribute("width")||e.getAttribute("height")||this.editor.model.enqueueChange(i.batch,(t=>{t.setAttribute("width",n.naturalWidth,e),t.setAttribute("height",n.naturalHeight,e)})),this._domEmitter.stopListening(n,"load")})),n.src=t})))}getClosestSelectedImageWidget(e){const t=e.getFirstPosition();if(!t)return null;const i=e.getSelectedElement();if(i&&this.isImageWidget(i))return i;let n=t.parent;for(;n;){if(n.is("element")&&this.isImageWidget(n))return n;n=n.parent}return null}getClosestSelectedImageElement(e){const t=e.getSelectedElement();return this.isImage(t)?t:e.getFirstPosition().findAncestor("imageBlock")}getImageWidgetFromImageView(e){return e.findAncestor({classes:p})}isImageAllowed(){const e=this.editor.model.document.selection;return function(e,t){const i=f(e,t,null);if("imageBlock"==i){const i=function(e,t){const i=(0,l.findOptimalInsertionRange)(e,t),n=i.start.parent;if(n.isEmpty&&!n.is("element","$root"))return n.parent;return n}(t,e.model);if(e.model.schema.checkChild(i,"imageBlock"))return!0}else if(e.model.schema.checkChild(t.focus,"imageInline"))return!0;return!1}(this.editor,e)&&function(e){return[...e.focus.getAncestors()].every((e=>!e.is("element","imageBlock")))}(e)}toImageWidget(e,t,i){t.setCustomProperty("image",!0,e);return(0,l.toWidget)(e,t,{label:()=>{const t=this.findViewImgElement(e).getAttribute("alt");return t?`${t} ${i}`:i}})}isImageWidget(e){return!!e.getCustomProperty("image")&&(0,l.isWidget)(e)}isBlockImage(e){return!!e&&e.is("element","imageBlock")}isInlineImage(e){return!!e&&e.is("element","imageInline")}findViewImgElement(e){if(this.isInlineImageView(e))return e;const t=this.editor.editing.view;for(const{item:i}of t.createRangeIn(e))if(this.isInlineImageView(i))return i}destroy(){return this._domEmitter.stopListening(),super.destroy()}}function f(e,t,i){const n=e.model.schema,o=e.config.get("image.insert.type");return e.plugins.has("ImageBlockEditing")?e.plugins.has("ImageInlineEditing")?i||("inline"===o?"imageInline":"auto"!==o?"imageBlock":t.is("selection")?m(n,t):n.checkChild(t,"imageInline")?"imageInline":"imageBlock"):"imageBlock":"imageInline"}const b=new RegExp(String(/^(http(s)?:\/\/)?[\w-]+\.[\w.~:/[\]@!$&'()*+,;=%-]+/.source+/\.(jpg|jpeg|png|gif|ico|webp|JPG|JPEG|PNG|GIF|ICO|WEBP)/.source+/(\?[\w.~:/[\]@!$&'()*+,;=%-]*)?/.source+/(#[\w.~:/[\]@!$&'()*+,;=%-]*)?$/.source));class k extends e.Plugin{static get requires(){return[t.Clipboard,h,a.Undo,s.Delete]}static get pluginName(){return"AutoImage"}constructor(e){super(e),this._timeoutId=null,this._positionToInsert=null}init(){const e=this.editor,t=e.model.document,i=e.plugins.get("ClipboardPipeline");this.listenTo(i,"inputTransformation",(()=>{const e=t.selection.getFirstRange(),i=o.LivePosition.fromPosition(e.start);i.stickiness="toPrevious";const n=o.LivePosition.fromPosition(e.end);n.stickiness="toNext",t.once("change:data",(()=>{this._embedImageBetweenPositions(i,n),i.detach(),n.detach()}),{priority:"high"})})),e.commands.get("undo").on("execute",(()=>{this._timeoutId&&(r.global.window.clearTimeout(this._timeoutId),this._positionToInsert.detach(),this._timeoutId=null,this._positionToInsert=null)}),{priority:"high"})}_embedImageBetweenPositions(e,t){const i=this.editor,n=new o.LiveRange(e,t),a=n.getWalker({ignoreElementEnd:!0}),s=Object.fromEntries(i.model.document.selection.getAttributes()),r=this.editor.plugins.get("ImageUtils");let l="";for(const e of a)e.item.is("$textProxy")&&(l+=e.item.data);l=l.trim(),l.match(b)?(this._positionToInsert=o.LivePosition.fromPosition(e),this._timeoutId=setTimeout((()=>{if(!i.commands.get("insertImage").isEnabled)return void n.detach();i.model.change((e=>{let t;this._timeoutId=null,e.remove(n),n.detach(),"$graveyard"!==this._positionToInsert.root.rootName&&(t=this._positionToInsert.toPosition()),r.insertImage({...s,src:l},t),this._positionToInsert.detach(),this._positionToInsert=null}));i.plugins.get("Delete").requestUndoOnBackspace()}),100)):n.detach()}}class I extends e.Command{refresh(){const e=this.editor.plugins.get("ImageUtils").getClosestSelectedImageElement(this.editor.model.document.selection);this.isEnabled=!!e,this.isEnabled&&e.hasAttribute("alt")?this.value=e.getAttribute("alt"):this.value=!1}execute(e){const t=this.editor,i=t.plugins.get("ImageUtils"),n=t.model,o=i.getClosestSelectedImageElement(n.document.selection);n.change((t=>{t.setAttribute("alt",e.newValue,o)}))}}class w extends e.Plugin{static get requires(){return[h]}static get pluginName(){return"ImageTextAlternativeEditing"}init(){this.editor.commands.add("imageTextAlternative",new I(this.editor))}}var v=i(273),y=i(62),_=i.n(y),C=i(915),E={injectType:"singletonStyleTag",attributes:{"data-cke":!0},insert:"head",singleton:!0};_()(C.Z,E);C.Z.locals;var x=i(487),S={injectType:"singletonStyleTag",attributes:{"data-cke":!0},insert:"head",singleton:!0};_()(x.Z,S);x.Z.locals;class A extends v.View{constructor(t){super(t);const i=this.locale.t;this.focusTracker=new r.FocusTracker,this.keystrokes=new r.KeystrokeHandler,this.labeledInput=this._createLabeledInputView(),this.saveButtonView=this._createButton(i("Save"),e.icons.check,"ck-button-save"),this.saveButtonView.type="submit",this.cancelButtonView=this._createButton(i("Cancel"),e.icons.cancel,"ck-button-cancel","cancel"),this._focusables=new v.ViewCollection,this._focusCycler=new v.FocusCycler({focusables:this._focusables,focusTracker:this.focusTracker,keystrokeHandler:this.keystrokes,actions:{focusPrevious:"shift + tab",focusNext:"tab"}}),this.setTemplate({tag:"form",attributes:{class:["ck","ck-text-alternative-form","ck-responsive-form"],tabindex:"-1"},children:[this.labeledInput,this.saveButtonView,this.cancelButtonView]})}render(){super.render(),this.keystrokes.listenTo(this.element),(0,v.submitHandler)({view:this}),[this.labeledInput,this.saveButtonView,this.cancelButtonView].forEach((e=>{this._focusables.add(e),this.focusTracker.add(e.element)}))}destroy(){super.destroy(),this.focusTracker.destroy(),this.keystrokes.destroy()}_createButton(e,t,i,n){const o=new v.ButtonView(this.locale);return o.set({label:e,icon:t,tooltip:!0}),o.extendTemplate({attributes:{class:i}}),n&&o.delegate("execute").to(this,n),o}_createLabeledInputView(){const e=this.locale.t,t=new v.LabeledFieldView(this.locale,v.createLabeledInputText);return t.label=e("Text alternative"),t}}function T(e){const t=e.editing.view,i=v.BalloonPanelView.defaultPositions,n=e.plugins.get("ImageUtils");return{target:t.domConverter.mapViewToDom(n.getClosestSelectedImageWidget(t.document.selection)),positions:[i.northArrowSouth,i.northArrowSouthWest,i.northArrowSouthEast,i.southArrowNorth,i.southArrowNorthWest,i.southArrowNorthEast,i.viewportStickyNorth]}}class B extends e.Plugin{static get requires(){return[v.ContextualBalloon]}static get pluginName(){return"ImageTextAlternativeUI"}init(){this._createButton()}destroy(){super.destroy(),this._form&&this._form.destroy()}_createButton(){const t=this.editor,i=t.t;t.ui.componentFactory.add("imageTextAlternative",(n=>{const o=t.commands.get("imageTextAlternative"),a=new v.ButtonView(n);return a.set({label:i("Change image text alternative"),icon:e.icons.textAlternative,tooltip:!0}),a.bind("isEnabled").to(o,"isEnabled"),a.bind("isOn").to(o,"value",(e=>!!e)),this.listenTo(a,"execute",(()=>{this._showForm()})),a}))}_createForm(){const e=this.editor,t=e.editing.view.document,i=e.plugins.get("ImageUtils");this._balloon=this.editor.plugins.get("ContextualBalloon"),this._form=new((0,v.CssTransitionDisablerMixin)(A))(e.locale),this._form.render(),this.listenTo(this._form,"submit",(()=>{e.execute("imageTextAlternative",{newValue:this._form.labeledInput.fieldView.element.value}),this._hideForm(!0)})),this.listenTo(this._form,"cancel",(()=>{this._hideForm(!0)})),this._form.keystrokes.set("Esc",((e,t)=>{this._hideForm(!0),t()})),this.listenTo(e.ui,"update",(()=>{i.getClosestSelectedImageWidget(t.selection)?this._isVisible&&function(e){const t=e.plugins.get("ContextualBalloon");if(e.plugins.get("ImageUtils").getClosestSelectedImageWidget(e.editing.view.document.selection)){const i=T(e);t.updatePosition(i)}}(e):this._hideForm(!0)})),(0,v.clickOutsideHandler)({emitter:this._form,activator:()=>this._isVisible,contextElements:()=>[this._balloon.view.element],callback:()=>this._hideForm()})}_showForm(){if(this._isVisible)return;this._form||this._createForm();const e=this.editor,t=e.commands.get("imageTextAlternative"),i=this._form.labeledInput;this._form.disableCssTransitions(),this._isInBalloon||this._balloon.add({view:this._form,position:T(e)}),i.fieldView.value=i.fieldView.element.value=t.value||"",this._form.labeledInput.fieldView.select(),this._form.enableCssTransitions()}_hideForm(e=!1){this._isInBalloon&&(this._form.focusTracker.isFocused&&this._form.saveButtonView.focus(),this._balloon.remove(this._form),e&&this.editor.editing.view.focus())}get _isVisible(){return!!this._balloon&&this._balloon.visibleView===this._form}get _isInBalloon(){return!!this._balloon&&this._balloon.hasView(this._form)}}class V extends e.Plugin{static get requires(){return[w,B]}static get pluginName(){return"ImageTextAlternative"}}function U(e,t){const i=(t,i,n)=>{if(!n.consumable.consume(i.item,t.name))return;const o=n.writer,a=n.mapper.toViewElement(i.item),s=e.findViewImgElement(a);null===i.attributeNewValue?(o.removeAttribute("srcset",s),o.removeAttribute("sizes",s)):i.attributeNewValue&&(o.setAttribute("srcset",i.attributeNewValue,s),o.setAttribute("sizes","100vw",s))};return e=>{e.on(`attribute:srcset:${t}`,i)}}function z(e,t,i){const n=(t,i,n)=>{if(!n.consumable.consume(i.item,t.name))return;const o=n.writer,a=n.mapper.toViewElement(i.item),s=e.findViewImgElement(a);o.setAttribute(i.attributeKey,i.attributeNewValue||"",s)};return e=>{e.on(`attribute:${i}:${t}`,n)}}class R extends o.Observer{observe(e){this.listenTo(e,"load",((e,t)=>{const i=t.target;this.checkShouldIgnoreEventFromTarget(i)||"IMG"==i.tagName&&this._fireEvents(t)}),{useCapture:!0})}stopObserving(e){this.stopListening(e)}_fireEvents(e){this.isEnabled&&(this.document.fire("layoutChanged"),this.document.fire("imageLoaded",e))}}class P extends e.Command{constructor(e){super(e);const t=e.config.get("image.insert.type");e.plugins.has("ImageBlockEditing")||"block"===t&&(0,r.logWarning)("image-block-plugin-required"),e.plugins.has("ImageInlineEditing")||"inline"===t&&(0,r.logWarning)("image-inline-plugin-required")}refresh(){const e=this.editor.plugins.get("ImageUtils");this.isEnabled=e.isImageAllowed()}execute(e){const t=(0,r.toArray)(e.source),i=this.editor.model.document.selection,n=this.editor.plugins.get("ImageUtils"),o=Object.fromEntries(i.getAttributes());t.forEach(((e,t)=>{const a=i.getSelectedElement();if("string"==typeof e&&(e={src:e}),t&&a&&n.isImage(a)){const t=this.editor.model.createPositionAfter(a);n.insertImage({...e,...o},t)}else n.insertImage({...e,...o})}))}}class N extends e.Command{constructor(e){super(e),this.decorate("cleanupImage")}refresh(){const e=this.editor.plugins.get("ImageUtils"),t=this.editor.model.document.selection.getSelectedElement();this.isEnabled=e.isImage(t),this.value=this.isEnabled?t.getAttribute("src"):null}execute(e){const t=this.editor.model.document.selection.getSelectedElement(),i=this.editor.plugins.get("ImageUtils");this.editor.model.change((n=>{n.setAttribute("src",e.source,t),this.cleanupImage(n,t),i.setImageNaturalSizeAttributes(t)}))}cleanupImage(e,t){e.removeAttribute("srcset",t),e.removeAttribute("sizes",t),e.removeAttribute("sources",t),e.removeAttribute("width",t),e.removeAttribute("height",t),e.removeAttribute("alt",t)}}class O extends e.Plugin{static get requires(){return[h]}static get pluginName(){return"ImageEditing"}init(){const e=this.editor,t=e.conversion;e.editing.view.addObserver(R),t.for("upcast").attributeToAttribute({view:{name:"img",key:"alt"},model:"alt"}).attributeToAttribute({view:{name:"img",key:"srcset"},model:"srcset"});const i=new P(e),n=new N(e);e.commands.add("insertImage",i),e.commands.add("replaceImageSource",n),e.commands.add("imageInsert",i)}}class L extends e.Plugin{static get requires(){return[h]}static get pluginName(){return"ImageSizeAttributes"}afterInit(){this._registerSchema(),this._registerConverters("imageBlock"),this._registerConverters("imageInline")}_registerSchema(){this.editor.plugins.has("ImageBlockEditing")&&this.editor.model.schema.extend("imageBlock",{allowAttributes:["width","height"]}),this.editor.plugins.has("ImageInlineEditing")&&this.editor.model.schema.extend("imageInline",{allowAttributes:["width","height"]})}_registerConverters(e){const t=this.editor,i=t.plugins.get("ImageUtils"),n="imageBlock"===e?"figure":"img";function o(t,n,o,a){t.on(`attribute:${n}:${e}`,((t,n,s)=>{if(!s.consumable.consume(n.item,t.name))return;const r=s.writer,l=s.mapper.toViewElement(n.item),c=i.findViewImgElement(l);if(null!==n.attributeNewValue?r.setAttribute(o,n.attributeNewValue,c):r.removeAttribute(o,c),n.item.hasAttribute("sources"))return;const g=n.item.hasAttribute("resizedWidth");if("imageInline"===e&&!g&&!a)return;const m=n.item.getAttribute("width"),d=n.item.getAttribute("height");m&&d&&r.setStyle("aspect-ratio",`${m}/${d}`,c)}))}t.conversion.for("upcast").attributeToAttribute({view:{name:n,styles:{width:/.+/}},model:{key:"width",value:e=>u(e)?d(e.getStyle("width")):null}}).attributeToAttribute({view:{name:n,key:"width"},model:"width"}).attributeToAttribute({view:{name:n,styles:{height:/.+/}},model:{key:"height",value:e=>u(e)?d(e.getStyle("height")):null}}).attributeToAttribute({view:{name:n,key:"height"},model:"height"}),t.conversion.for("editingDowncast").add((e=>{o(e,"width","width",!0),o(e,"height","height",!0)})),t.conversion.for("dataDowncast").add((e=>{o(e,"width","width",!1),o(e,"height","height",!1)}))}}class F extends e.Command{constructor(e,t){super(e),this._modelElementName=t}refresh(){const e=this.editor.plugins.get("ImageUtils"),t=e.getClosestSelectedImageElement(this.editor.model.document.selection);"imageBlock"===this._modelElementName?this.isEnabled=e.isInlineImage(t):this.isEnabled=e.isBlockImage(t)}execute(e={}){const t=this.editor,i=this.editor.model,n=t.plugins.get("ImageUtils"),o=n.getClosestSelectedImageElement(i.document.selection),a=Object.fromEntries(o.getAttributes());return a.src||a.uploadId?i.change((t=>{const{setImageSizes:s=!0}=e,r=Array.from(i.markers).filter((e=>e.getRange().containsItem(o))),l=n.insertImage(a,i.createSelection(o,"on"),this._modelElementName,{setImageSizes:s});if(!l)return null;const c=t.createRangeOn(l);for(const e of r){const i=e.getRange(),n="$graveyard"!=i.root.rootName?i.getJoined(c,!0):c;t.updateMarker(e,{range:n})}return{oldElement:o,newElement:l}})):null}}var D=i(390),j={injectType:"singletonStyleTag",attributes:{"data-cke":!0},insert:"head",singleton:!0};_()(D.Z,j);D.Z.locals;class W extends e.Plugin{static get requires(){return[h]}static get pluginName(){return"ImagePlaceholder"}afterInit(){this._setupSchema(),this._setupConversion(),this._setupLoadListener()}_setupSchema(){const e=this.editor.model.schema;e.isRegistered("imageBlock")&&e.extend("imageBlock",{allowAttributes:["placeholder"]}),e.isRegistered("imageInline")&&e.extend("imageInline",{allowAttributes:["placeholder"]})}_setupConversion(){const e=this.editor,t=e.conversion,i=e.plugins.get("ImageUtils");t.for("editingDowncast").add((e=>{e.on("attribute:placeholder",((e,t,n)=>{if(!n.consumable.test(t.item,e.name))return;if(!t.item.is("element","imageBlock")&&!t.item.is("element","imageInline"))return;n.consumable.consume(t.item,e.name);const o=n.writer,a=n.mapper.toViewElement(t.item),s=i.findViewImgElement(a);t.attributeNewValue?(o.addClass("image_placeholder",s),o.setStyle("background-image",`url(${t.attributeNewValue})`,s),o.setCustomProperty("editingPipeline:doNotReuseOnce",!0,s)):(o.removeClass("image_placeholder",s),o.removeStyle("background-image",s))}))}))}_setupLoadListener(){const e=this.editor,t=e.model,i=e.editing,n=i.view,o=e.plugins.get("ImageUtils");n.addObserver(R),this.listenTo(n.document,"imageLoaded",((e,a)=>{const s=n.domConverter.mapDomToView(a.target);if(!s)return;const r=o.getImageWidgetFromImageView(s);if(!r)return;const l=i.mapper.toModelElement(r);l&&l.hasAttribute("placeholder")&&t.enqueueChange({isUndoable:!1},(e=>{e.removeAttribute("placeholder",l)}))}))}}class M extends e.Plugin{static get requires(){return[O,L,h,W,t.ClipboardPipeline]}static get pluginName(){return"ImageBlockEditing"}init(){const e=this.editor;e.model.schema.register("imageBlock",{inheritAllFrom:"$blockObject",allowAttributes:["alt","src","srcset"]}),this._setupConversion(),e.plugins.has("ImageInlineEditing")&&(e.commands.add("imageTypeBlock",new F(this.editor,"imageBlock")),this._setupClipboardIntegration())}_setupConversion(){const e=this.editor,t=e.t,i=e.conversion,n=e.plugins.get("ImageUtils");i.for("dataDowncast").elementToStructure({model:"imageBlock",view:(e,{writer:t})=>c(t)}),i.for("editingDowncast").elementToStructure({model:"imageBlock",view:(e,{writer:i})=>n.toImageWidget(c(i),i,t("image widget"))}),i.for("downcast").add(z(n,"imageBlock","src")).add(z(n,"imageBlock","alt")).add(U(n,"imageBlock")),i.for("upcast").elementToElement({view:g(e,"imageBlock"),model:(e,{writer:t})=>t.createElement("imageBlock",e.hasAttribute("src")?{src:e.getAttribute("src")}:void 0)}).add(function(e){const t=(t,i,n)=>{if(!n.consumable.test(i.viewItem,{name:!0,classes:"image"}))return;const o=e.findViewImgElement(i.viewItem);if(!o||!n.consumable.test(o,{name:!0}))return;n.consumable.consume(i.viewItem,{name:!0,classes:"image"});const a=n.convertItem(o,i.modelCursor),s=(0,r.first)(a.modelRange.getItems());s?(n.convertChildren(i.viewItem,s),n.updateConversionResult(s,i)):n.consumable.revert(i.viewItem,{name:!0,classes:"image"})};return e=>{e.on("element:figure",t)}}(n))}_setupClipboardIntegration(){const e=this.editor,t=e.model,i=e.editing.view,n=e.plugins.get("ImageUtils"),a=e.plugins.get("ClipboardPipeline");this.listenTo(a,"inputTransformation",((a,s)=>{const r=Array.from(s.content.getChildren());let l;if(!r.every(n.isInlineImageView))return;l=s.targetRanges?e.editing.mapper.toModelRange(s.targetRanges[0]):t.document.selection.getFirstRange();const c=t.createSelection(l);if("imageBlock"===m(t.schema,c)){const e=new o.UpcastWriter(i.document),t=r.map((t=>e.createElement("figure",{class:"image"},t)));s.content=e.createDocumentFragment(t)}})),this.listenTo(a,"contentInsertion",((e,i)=>{"paste"===i.method&&t.change((e=>{const t=e.createRangeIn(i.content);for(const e of t.getItems())e.is("element","imageBlock")&&n.setImageNaturalSizeAttributes(e)}))}))}}var q=i(554),$={injectType:"singletonStyleTag",attributes:{"data-cke":!0},insert:"head",singleton:!0};_()(q.Z,$);q.Z.locals;class Z extends v.View{constructor(e,t=[]){super(e),this.focusTracker=new r.FocusTracker,this.keystrokes=new r.KeystrokeHandler,this._focusables=new v.ViewCollection,this.children=this.createCollection(),this._focusCycler=new v.FocusCycler({focusables:this._focusables,focusTracker:this.focusTracker,keystrokeHandler:this.keystrokes,actions:{focusPrevious:"shift + tab",focusNext:"tab"}});for(const e of t)this.children.add(e),this._focusables.add(e),e instanceof v.CollapsibleView&&this._focusables.addMany(e.children);if(this._focusables.length>1)for(const e of this._focusables)H(e)&&(e.focusCycler.on("forwardCycle",(e=>{this._focusCycler.focusNext(),e.stop()})),e.focusCycler.on("backwardCycle",(e=>{this._focusCycler.focusPrevious(),e.stop()})));this.setTemplate({tag:"form",attributes:{class:["ck","ck-image-insert-form"],tabindex:-1},children:this.children})}render(){super.render(),(0,v.submitHandler)({view:this});for(const e of this._focusables)this.focusTracker.add(e.element);this.keystrokes.listenTo(this.element);const e=e=>e.stopPropagation();this.keystrokes.set("arrowright",e),this.keystrokes.set("arrowleft",e),this.keystrokes.set("arrowup",e),this.keystrokes.set("arrowdown",e)}destroy(){super.destroy(),this.focusTracker.destroy(),this.keystrokes.destroy()}focus(){this._focusCycler.focusFirst()}}function H(e){return"focusCycler"in e}class K extends e.Plugin{static get pluginName(){return"ImageInsertUI"}static get requires(){return[h]}constructor(e){super(e),this._integrations=new Map,e.config.define("image.insert.integrations",["upload","assetManager","url"])}init(){const e=this.editor,t=e.model.document.selection,i=e.plugins.get("ImageUtils");this.set("isImageSelected",!1),this.listenTo(e.model.document,"change",(()=>{this.isImageSelected=i.isImage(t.getSelectedElement())}));const n=e=>this._createToolbarComponent(e);e.ui.componentFactory.add("insertImage",n),e.ui.componentFactory.add("imageInsert",n)}registerIntegration({name:e,observable:t,buttonViewCreator:i,formViewCreator:n,requiresForm:o}){this._integrations.has(e)&&(0,r.logWarning)("image-insert-integration-exists",{name:e}),this._integrations.set(e,{observable:t,buttonViewCreator:i,formViewCreator:n,requiresForm:!!o})}_createToolbarComponent(e){const t=this.editor,i=e.t,n=this._prepareIntegrations();if(!n.length)return null;let o;const a=n[0];if(1==n.length){if(!a.requiresForm)return a.buttonViewCreator(!0);o=a.buttonViewCreator(!0)}else{const t=a.buttonViewCreator(!1);o=new v.SplitButtonView(e,t),o.tooltip=!0,o.bind("label").to(this,"isImageSelected",(e=>i(e?"Replace image":"Insert image")))}const s=this.dropdownView=(0,v.createDropdown)(e,o),r=n.map((({observable:e})=>e));return s.bind("isEnabled").toMany(r,"isEnabled",((...e)=>e.some((e=>e)))),s.once("change:isOpen",(()=>{const e=n.map((({formViewCreator:e})=>e(1==n.length))),i=new Z(t.locale,e);s.panelView.children.add(i)})),s}_prepareIntegrations(){const e=this.editor.config.get("image.insert.integrations"),t=[];if(!e.length)return(0,r.logWarning)("image-insert-integrations-not-specified"),t;for(const i of e)this._integrations.has(i)?t.push(this._integrations.get(i)):["upload","assetManager","url"].includes(i)||(0,r.logWarning)("image-insert-unknown-integration",{item:i});return t.length||(0,r.logWarning)("image-insert-integrations-not-registered"),t}}var G=i(417),J={injectType:"singletonStyleTag",attributes:{"data-cke":!0},insert:"head",singleton:!0};_()(G.Z,J);G.Z.locals;class Q extends e.Plugin{static get requires(){return[M,l.Widget,V,K]}static get pluginName(){return"ImageBlock"}}class X extends e.Plugin{static get requires(){return[O,L,h,W,t.ClipboardPipeline]}static get pluginName(){return"ImageInlineEditing"}init(){const e=this.editor,t=e.model.schema;t.register("imageInline",{inheritAllFrom:"$inlineObject",allowAttributes:["alt","src","srcset"]}),t.addChildCheck(((e,t)=>{if(e.endsWith("caption")&&"imageInline"===t.name)return!1})),this._setupConversion(),e.plugins.has("ImageBlockEditing")&&(e.commands.add("imageTypeInline",new F(this.editor,"imageInline")),this._setupClipboardIntegration())}_setupConversion(){const e=this.editor,t=e.t,i=e.conversion,n=e.plugins.get("ImageUtils");i.for("dataDowncast").elementToElement({model:"imageInline",view:(e,{writer:t})=>t.createEmptyElement("img")}),i.for("editingDowncast").elementToStructure({model:"imageInline",view:(e,{writer:i})=>n.toImageWidget(function(e){return e.createContainerElement("span",{class:"image-inline"},e.createEmptyElement("img"))}(i),i,t("image widget"))}),i.for("downcast").add(z(n,"imageInline","src")).add(z(n,"imageInline","alt")).add(U(n,"imageInline")),i.for("upcast").elementToElement({view:g(e,"imageInline"),model:(e,{writer:t})=>t.createElement("imageInline",e.hasAttribute("src")?{src:e.getAttribute("src")}:void 0)})}_setupClipboardIntegration(){const e=this.editor,t=e.model,i=e.editing.view,n=e.plugins.get("ImageUtils"),a=e.plugins.get("ClipboardPipeline");this.listenTo(a,"inputTransformation",((a,s)=>{const r=Array.from(s.content.getChildren());let l;if(!r.every(n.isBlockImageView))return;l=s.targetRanges?e.editing.mapper.toModelRange(s.targetRanges[0]):t.document.selection.getFirstRange();const c=t.createSelection(l);if("imageInline"===m(t.schema,c)){const e=new o.UpcastWriter(i.document),t=r.map((t=>1===t.childCount?(Array.from(t.getAttributes()).forEach((i=>e.setAttribute(...i,n.findViewImgElement(t)))),t.getChild(0)):t));s.content=e.createDocumentFragment(t)}})),this.listenTo(a,"contentInsertion",((e,i)=>{"paste"===i.method&&t.change((e=>{const t=e.createRangeIn(i.content);for(const e of t.getItems())e.is("element","imageInline")&&n.setImageNaturalSizeAttributes(e)}))}))}}class Y extends e.Plugin{static get requires(){return[X,l.Widget,V,K]}static get pluginName(){return"ImageInline"}}class ee extends e.Plugin{static get requires(){return[Q,Y]}static get pluginName(){return"Image"}}class te extends e.Plugin{static get pluginName(){return"ImageCaptionUtils"}static get requires(){return[h]}getCaptionFromImageModelElement(e){for(const t of e.getChildren())if(t&&t.is("element","caption"))return t;return null}getCaptionFromModelSelection(e){const t=this.editor.plugins.get("ImageUtils"),i=e.getFirstPosition().findAncestor("caption");return i&&t.isBlockImage(i.parent)?i:null}matchImageCaptionViewElement(e){const t=this.editor.plugins.get("ImageUtils");return"figcaption"==e.name&&t.isBlockImageView(e.parent)?{name:!0}:null}}class ie extends e.Command{refresh(){const e=this.editor,t=e.plugins.get("ImageCaptionUtils"),i=e.plugins.get("ImageUtils");if(!e.plugins.has(M))return this.isEnabled=!1,void(this.value=!1);const n=e.model.document.selection,o=n.getSelectedElement();if(!o){const e=t.getCaptionFromModelSelection(n);return this.isEnabled=!!e,void(this.value=!!e)}this.isEnabled=i.isImage(o),this.isEnabled?this.value=!!t.getCaptionFromImageModelElement(o):this.value=!1}execute(e={}){const{focusCaptionOnShow:t}=e;this.editor.model.change((e=>{this.value?this._hideImageCaption(e):this._showImageCaption(e,t)}))}_showImageCaption(e,t){const i=this.editor.model.document.selection,n=this.editor.plugins.get("ImageCaptionEditing"),o=this.editor.plugins.get("ImageUtils");let a=i.getSelectedElement();const s=n._getSavedCaption(a);o.isInlineImage(a)&&(this.editor.execute("imageTypeBlock"),a=i.getSelectedElement());const r=s||e.createElement("caption");e.append(r,a),t&&e.setSelection(r,"in")}_hideImageCaption(e){const t=this.editor,i=t.model.document.selection,n=t.plugins.get("ImageCaptionEditing"),o=t.plugins.get("ImageCaptionUtils");let a,s=i.getSelectedElement();s?a=o.getCaptionFromImageModelElement(s):(a=o.getCaptionFromModelSelection(i),s=a.parent),n._saveCaption(s,a),e.setSelection(s,"on"),e.remove(a)}}class ne extends e.Plugin{static get requires(){return[h,te]}static get pluginName(){return"ImageCaptionEditing"}constructor(e){super(e),this._savedCaptionsMap=new WeakMap}init(){const e=this.editor,t=e.model.schema;t.isRegistered("caption")?t.extend("caption",{allowIn:"imageBlock"}):t.register("caption",{allowIn:"imageBlock",allowContentOf:"$block",isLimit:!0}),e.commands.add("toggleImageCaption",new ie(this.editor)),this._setupConversion(),this._setupImageTypeCommandsIntegration(),this._registerCaptionReconversion()}_setupConversion(){const e=this.editor,t=e.editing.view,i=e.plugins.get("ImageUtils"),n=e.plugins.get("ImageCaptionUtils"),a=e.t;e.conversion.for("upcast").elementToElement({view:e=>n.matchImageCaptionViewElement(e),model:"caption"}),e.conversion.for("dataDowncast").elementToElement({model:"caption",view:(e,{writer:t})=>i.isBlockImage(e.parent)?t.createContainerElement("figcaption"):null}),e.conversion.for("editingDowncast").elementToElement({model:"caption",view:(e,{writer:n})=>{if(!i.isBlockImage(e.parent))return null;const s=n.createEditableElement("figcaption");n.setCustomProperty("imageCaption",!0,s),s.placeholder=a("Enter image caption"),(0,o.enablePlaceholder)({view:t,element:s,keepOnFocus:!0});const r=e.parent.getAttribute("alt"),c=r?a("Caption for image: %0",[r]):a("Caption for the image");return(0,l.toWidgetEditable)(s,n,{label:c})}})}_setupImageTypeCommandsIntegration(){const e=this.editor,t=e.plugins.get("ImageUtils"),i=e.plugins.get("ImageCaptionUtils"),n=e.commands.get("imageTypeInline"),o=e.commands.get("imageTypeBlock"),a=e=>{if(!e.return)return;const{oldElement:n,newElement:o}=e.return;if(!n)return;if(t.isBlockImage(n)){const e=i.getCaptionFromImageModelElement(n);if(e)return void this._saveCaption(o,e)}const a=this._getSavedCaption(n);a&&this._saveCaption(o,a)};n&&this.listenTo(n,"execute",a,{priority:"low"}),o&&this.listenTo(o,"execute",a,{priority:"low"})}_getSavedCaption(e){const t=this._savedCaptionsMap.get(e);return t?o.Element.fromJSON(t):null}_saveCaption(e,t){this._savedCaptionsMap.set(e,t.toJSON())}_registerCaptionReconversion(){const e=this.editor,t=e.model,i=e.plugins.get("ImageUtils"),n=e.plugins.get("ImageCaptionUtils");t.document.on("change:data",(()=>{const o=t.document.differ.getChanges();for(const t of o){if("alt"!==t.attributeKey)continue;const o=t.range.start.nodeAfter;if(i.isBlockImage(o)){const t=n.getCaptionFromImageModelElement(o);if(!t)return;e.editing.reconvertItem(t)}}}))}}class oe extends e.Plugin{static get requires(){return[te]}static get pluginName(){return"ImageCaptionUI"}init(){const t=this.editor,i=t.editing.view,n=t.plugins.get("ImageCaptionUtils"),o=t.t;t.ui.componentFactory.add("toggleImageCaption",(a=>{const s=t.commands.get("toggleImageCaption"),r=new v.ButtonView(a);return r.set({icon:e.icons.caption,tooltip:!0,isToggleable:!0}),r.bind("isOn","isEnabled").to(s,"value","isEnabled"),r.bind("label").to(s,"value",(e=>o(e?"Toggle caption off":"Toggle caption on"))),this.listenTo(r,"execute",(()=>{t.execute("toggleImageCaption",{focusCaptionOnShow:!0});const e=n.getCaptionFromModelSelection(t.model.document.selection);if(e){const n=t.editing.mapper.toViewElement(e);i.scrollToTheSelection(),i.change((e=>{e.addClass("image__caption_highlighted",n)}))}t.editing.view.focus()})),r}))}}var ae=i(86),se={injectType:"singletonStyleTag",attributes:{"data-cke":!0},insert:"head",singleton:!0};_()(ae.Z,se);ae.Z.locals;class re extends e.Plugin{static get requires(){return[ne,oe]}static get pluginName(){return"ImageCaption"}}var le=i(448);function ce(e){const t=e.map((e=>e.replace("+","\\+")));return new RegExp(`^image\\/(${t.join("|")})$`)}function ge(e){return new Promise(((t,i)=>{const n=e.getAttribute("src");fetch(n).then((e=>e.blob())).then((e=>{const i=me(e,n),o=i.replace("image/",""),a=new File([e],`image.${o}`,{type:i});t(a)})).catch((e=>e&&"TypeError"===e.name?function(e){return function(e){return new Promise(((t,i)=>{const n=r.global.document.createElement("img");n.addEventListener("load",(()=>{const e=r.global.document.createElement("canvas");e.width=n.width,e.height=n.height;e.getContext("2d").drawImage(n,0,0),e.toBlob((e=>e?t(e):i()))})),n.addEventListener("error",(()=>i())),n.src=e}))}(e).then((t=>{const i=me(t,e),n=i.replace("image/","");return new File([t],`image.${n}`,{type:i})}))}(n).then(t).catch(i):i(e)))}))}function me(e,t){return e.type?e.type:t.match(/data:(image\/\w+);base64/)?t.match(/data:(image\/\w+);base64/)[1].toLowerCase():"image/jpeg"}class de extends e.Plugin{static get pluginName(){return"ImageUploadUI"}init(){const t=this.editor,i=t.t,n=n=>{const o=new le.FileDialogButtonView(n),a=t.commands.get("uploadImage"),s=t.config.get("image.upload.types"),r=ce(s);return o.set({acceptedType:s.map((e=>`image/${e}`)).join(","),allowMultipleFiles:!0,label:i("Upload image from computer"),icon:e.icons.imageUpload,tooltip:!0}),o.bind("isEnabled").to(a),o.on("done",((e,i)=>{const n=Array.from(i).filter((e=>r.test(e.type)));n.length&&(t.execute("uploadImage",{file:n}),t.editing.view.focus())})),o};if(t.ui.componentFactory.add("uploadImage",n),t.ui.componentFactory.add("imageUpload",n),t.plugins.has("ImageInsertUI")){const e=t.plugins.get("ImageInsertUI"),n=t.commands.get("uploadImage");e.registerIntegration({name:"upload",observable:n,buttonViewCreator:()=>{const n=t.ui.componentFactory.create("uploadImage");return n.bind("label").to(e,"isImageSelected",(e=>i(e?"Replace image from computer":"Upload image from computer"))),n},formViewCreator:()=>{const n=t.ui.componentFactory.create("uploadImage");return n.withText=!0,n.bind("label").to(e,"isImageSelected",(e=>i(e?"Replace from computer":"Upload from computer"))),n.on("execute",(()=>{e.dropdownView.isOpen=!1})),n}})}}}var ue=i(283),pe={injectType:"singletonStyleTag",attributes:{"data-cke":!0},insert:"head",singleton:!0};_()(ue.Z,pe);ue.Z.locals;var he=i(651),fe={injectType:"singletonStyleTag",attributes:{"data-cke":!0},insert:"head",singleton:!0};_()(he.Z,fe);he.Z.locals;var be=i(582),ke={injectType:"singletonStyleTag",attributes:{"data-cke":!0},insert:"head",singleton:!0};_()(be.Z,ke);be.Z.locals;class Ie extends e.Plugin{static get pluginName(){return"ImageUploadProgress"}constructor(e){super(e),this.uploadStatusChange=(e,t,i)=>{const n=this.editor,o=t.item,a=o.getAttribute("uploadId");if(!i.consumable.consume(t.item,e.name))return;const s=n.plugins.get("ImageUtils"),r=n.plugins.get(le.FileRepository),l=a?t.attributeNewValue:null,c=this.placeholder,g=n.editing.mapper.toViewElement(o),m=i.writer;if("reading"==l)return we(g,m),void ve(s,c,g,m);if("uploading"==l){const e=r.loaders.get(a);return we(g,m),void(e?(ye(g,m),function(e,t,i,n){const o=function(e){const t=e.createUIElement("div",{class:"ck-progress-bar"});return e.setCustomProperty("progressBar",!0,t),t}(t);t.insert(t.createPositionAt(e,"end"),o),i.on("change:uploadedPercent",((e,t,i)=>{n.change((e=>{e.setStyle("width",i+"%",o)}))}))}(g,m,e,n.editing.view),function(e,t,i,n){if(n.data){const o=e.findViewImgElement(t);i.setAttribute("src",n.data,o)}}(s,g,m,e)):ve(s,c,g,m))}"complete"==l&&r.loaders.get(a)&&function(e,t,i){const n=t.createUIElement("div",{class:"ck-image-upload-complete-icon"});t.insert(t.createPositionAt(e,"end"),n),setTimeout((()=>{i.change((e=>e.remove(e.createRangeOn(n))))}),3e3)}(g,m,n.editing.view),function(e,t){Ce(e,t,"progressBar")}(g,m),ye(g,m),function(e,t){t.removeClass("ck-appear",e)}(g,m)},this.placeholder="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw=="}init(){const e=this.editor;e.plugins.has("ImageBlockEditing")&&e.editing.downcastDispatcher.on("attribute:uploadStatus:imageBlock",this.uploadStatusChange),e.plugins.has("ImageInlineEditing")&&e.editing.downcastDispatcher.on("attribute:uploadStatus:imageInline",this.uploadStatusChange)}}function we(e,t){e.hasClass("ck-appear")||t.addClass("ck-appear",e)}function ve(e,t,i,n){i.hasClass("ck-image-upload-placeholder")||n.addClass("ck-image-upload-placeholder",i);const o=e.findViewImgElement(i);o.getAttribute("src")!==t&&n.setAttribute("src",t,o),_e(i,"placeholder")||n.insert(n.createPositionAfter(o),function(e){const t=e.createUIElement("div",{class:"ck-upload-placeholder-loader"});return e.setCustomProperty("placeholder",!0,t),t}(n))}function ye(e,t){e.hasClass("ck-image-upload-placeholder")&&t.removeClass("ck-image-upload-placeholder",e),Ce(e,t,"placeholder")}function _e(e,t){for(const i of e.getChildren())if(i.getCustomProperty(t))return i}function Ce(e,t,i){const n=_e(e,i);n&&t.remove(t.createRangeOn(n))}class Ee extends e.Command{refresh(){const e=this.editor,t=e.plugins.get("ImageUtils"),i=e.model.document.selection.getSelectedElement();this.isEnabled=t.isImageAllowed()||t.isImage(i)}execute(e){const t=(0,r.toArray)(e.file),i=this.editor.model.document.selection,n=this.editor.plugins.get("ImageUtils"),o=Object.fromEntries(i.getAttributes());t.forEach(((e,t)=>{const a=i.getSelectedElement();if(t&&a&&n.isImage(a)){const t=this.editor.model.createPositionAfter(a);this._uploadImage(e,o,t)}else this._uploadImage(e,o)}))}_uploadImage(e,t,i){const n=this.editor,o=n.plugins.get(le.FileRepository).createLoader(e),a=n.plugins.get("ImageUtils");o&&a.insertImage({...t,uploadId:o.id},i)}}class xe extends e.Plugin{static get requires(){return[le.FileRepository,v.Notification,t.ClipboardPipeline,h]}static get pluginName(){return"ImageUploadEditing"}constructor(e){super(e),e.config.define("image",{upload:{types:["jpeg","png","gif","bmp","webp","tiff"]}}),this._uploadImageElements=new Map}init(){const e=this.editor,t=e.model.document,i=e.conversion,n=e.plugins.get(le.FileRepository),a=e.plugins.get("ImageUtils"),s=e.plugins.get("ClipboardPipeline"),r=ce(e.config.get("image.upload.types")),l=new Ee(e);e.commands.add("uploadImage",l),e.commands.add("imageUpload",l),i.for("upcast").attributeToAttribute({view:{name:"img",key:"uploadId"},model:"uploadId"}),this.listenTo(e.editing.view.document,"clipboardInput",((t,i)=>{if(n=i.dataTransfer,Array.from(n.types).includes("text/html")&&""!==n.getData("text/html"))return;var n;const o=Array.from(i.dataTransfer.files).filter((e=>!!e&&r.test(e.type)));o.length&&(t.stop(),e.model.change((t=>{i.targetRanges&&t.setSelection(i.targetRanges.map((t=>e.editing.mapper.toModelRange(t)))),e.execute("uploadImage",{file:o})})))})),this.listenTo(s,"inputTransformation",((t,i)=>{const s=Array.from(e.editing.view.createRangeIn(i.content)).map((e=>e.item)).filter((e=>function(e,t){return!(!e.isInlineImageView(t)||!t.getAttribute("src")||!t.getAttribute("src").match(/^data:image\/\w+;base64,/g)&&!t.getAttribute("src").match(/^blob:/g))}(a,e)&&!e.getAttribute("uploadProcessed"))).map((e=>({promise:ge(e),imageElement:e})));if(!s.length)return;const r=new o.UpcastWriter(e.editing.view.document);for(const e of s){r.setAttribute("uploadProcessed",!0,e.imageElement);const t=n.createLoader(e.promise);t&&(r.setAttribute("src","",e.imageElement),r.setAttribute("uploadId",t.id,e.imageElement))}})),e.editing.view.document.on("dragover",((e,t)=>{t.preventDefault()})),t.on("change",(()=>{const i=t.differ.getChanges({includeChangesInGraveyard:!0}).reverse(),o=new Set;for(const t of i)if("insert"==t.type&&"$text"!=t.name){const i=t.position.nodeAfter,a="$graveyard"==t.position.root.rootName;for(const t of Se(e,i)){const e=t.getAttribute("uploadId");if(!e)continue;const i=n.loaders.get(e);i&&(a?o.has(e)||i.abort():(o.add(e),this._uploadImageElements.set(e,t),"idle"==i.status&&this._readAndUpload(i)))}}})),this.on("uploadComplete",((e,{imageElement:t,data:i})=>{const n=i.urls?i.urls:i;this.editor.model.change((e=>{e.setAttribute("src",n.default,t),this._parseAndSetSrcsetAttributeOnImage(n,t,e),a.setImageNaturalSizeAttributes(t)}))}),{priority:"low"})}afterInit(){const e=this.editor.model.schema;this.editor.plugins.has("ImageBlockEditing")&&e.extend("imageBlock",{allowAttributes:["uploadId","uploadStatus"]}),this.editor.plugins.has("ImageInlineEditing")&&e.extend("imageInline",{allowAttributes:["uploadId","uploadStatus"]})}_readAndUpload(e){const t=this.editor,i=t.model,n=t.locale.t,o=t.plugins.get(le.FileRepository),a=t.plugins.get(v.Notification),s=t.plugins.get("ImageUtils"),l=this._uploadImageElements;return i.enqueueChange({isUndoable:!1},(t=>{t.setAttribute("uploadStatus","reading",l.get(e.id))})),e.read().then((()=>{const n=e.upload(),o=l.get(e.id);if(r.env.isSafari){const e=t.editing.mapper.toViewElement(o),i=s.findViewImgElement(e);t.editing.view.once("render",(()=>{if(!i.parent)return;const e=t.editing.view.domConverter.mapViewToDom(i.parent);if(!e)return;const n=e.style.display;e.style.display="none",e._ckHack=e.offsetHeight,e.style.display=n}))}return i.enqueueChange({isUndoable:!1},(e=>{e.setAttribute("uploadStatus","uploading",o)})),n})).then((t=>{i.enqueueChange({isUndoable:!1},(i=>{const n=l.get(e.id);i.setAttribute("uploadStatus","complete",n),this.fire("uploadComplete",{data:t,imageElement:n})})),c()})).catch((t=>{if("error"!==e.status&&"aborted"!==e.status)throw t;"error"==e.status&&t&&a.showWarning(t,{title:n("Upload failed"),namespace:"upload"}),i.enqueueChange({isUndoable:!1},(t=>{t.remove(l.get(e.id))})),c()}));function c(){i.enqueueChange({isUndoable:!1},(t=>{const i=l.get(e.id);t.removeAttribute("uploadId",i),t.removeAttribute("uploadStatus",i),l.delete(e.id)})),o.destroyLoader(e)}}_parseAndSetSrcsetAttributeOnImage(e,t,i){let n=0;const o=Object.keys(e).filter((e=>{const t=parseInt(e,10);if(!isNaN(t))return n=Math.max(n,t),!0})).map((t=>`${e[t]} ${t}w`)).join(", ");if(""!=o){const e={srcset:o};t.hasAttribute("width")||t.hasAttribute("height")||(e.width=n),i.setAttributes(e,t)}}}function Se(e,t){const i=e.plugins.get("ImageUtils");return Array.from(e.model.createRangeOn(t)).filter((e=>i.isImage(e.item))).map((e=>e.item))}class Ae extends e.Plugin{static get pluginName(){return"ImageUpload"}static get requires(){return[xe,de,Ie]}}class Te extends v.View{constructor(e){super(e),this.set("imageURLInputValue",""),this.set("isImageSelected",!1),this.set("isEnabled",!0),this.focusTracker=new r.FocusTracker,this.keystrokes=new r.KeystrokeHandler,this._focusables=new v.ViewCollection,this.focusCycler=new v.FocusCycler({focusables:this._focusables,focusTracker:this.focusTracker,keystrokeHandler:this.keystrokes,actions:{focusPrevious:"shift + tab",focusNext:"tab"}}),this.urlInputView=this._createUrlInputView(),this.insertButtonView=this._createInsertButton(),this.cancelButtonView=this._createCancelButton(),this._focusables.addMany([this.urlInputView,this.insertButtonView,this.cancelButtonView]),this.setTemplate({tag:"div",attributes:{class:["ck","ck-image-insert-url"]},children:[this.urlInputView,{tag:"div",attributes:{class:["ck","ck-image-insert-url__action-row"]},children:[this.insertButtonView,this.cancelButtonView]}]})}render(){super.render();for(const e of this._focusables)this.focusTracker.add(e.element);this.keystrokes.listenTo(this.element)}destroy(){super.destroy(),this.focusTracker.destroy(),this.keystrokes.destroy()}_createUrlInputView(){const e=this.locale,t=e.t,i=new v.LabeledFieldView(e,v.createLabeledInputText);return i.bind("label").to(this,"isImageSelected",(e=>t(e?"Update image URL":"Insert image via URL"))),i.bind("isEnabled").to(this),i.fieldView.placeholder="https://example.com/image.png",i.fieldView.bind("value").to(this,"imageURLInputValue",(e=>e||"")),i.fieldView.on("input",(()=>{this.imageURLInputValue=i.fieldView.element.value.trim()})),i}_createInsertButton(){const t=this.locale,i=t.t,n=new v.ButtonView(t);return n.set({icon:e.icons.check,class:"ck-button-save",type:"submit",withText:!0}),n.bind("label").to(this,"isImageSelected",(e=>i(e?"Update":"Insert"))),n.bind("isEnabled").to(this,"imageURLInputValue",this,"isEnabled",((...e)=>e.every((e=>e)))),n.delegate("execute").to(this,"submit"),n}_createCancelButton(){const t=this.locale,i=t.t,n=new v.ButtonView(t);return n.set({label:i("Cancel"),icon:e.icons.cancel,class:"ck-button-cancel",withText:!0}),n.bind("isEnabled").to(this),n.delegate("execute").to(this,"cancel"),n}focus(e){-1===e?this.focusCycler.focusLast():this.focusCycler.focusFirst()}}class Be extends e.Plugin{static get pluginName(){return"ImageInsertViaUrlUI"}static get requires(){return[K]}init(){this._imageInsertUI=this.editor.plugins.get("ImageInsertUI");const e=this.editor.commands.get("insertImage");this._imageInsertUI.registerIntegration({name:"url",observable:e,requiresForm:!0,buttonViewCreator:e=>this._createInsertUrlButton(e),formViewCreator:e=>this._createInsertUrlView(e)})}_createInsertUrlView(e){const t=this.editor,i=t.locale,n=i.t,o=t.commands.get("replaceImageSource"),a=t.commands.get("insertImage"),s=new Te(i),r=e?null:new v.CollapsibleView(i,[s]);return s.bind("isImageSelected").to(this._imageInsertUI),s.bind("isEnabled").toMany([a,o],"isEnabled",((...e)=>e.some((e=>e)))),s.imageURLInputValue=o.value||"",this._imageInsertUI.dropdownView.on("change:isOpen",(()=>{this._imageInsertUI.dropdownView.isOpen&&(s.imageURLInputValue=o.value||"",r&&(r.isCollapsed=!0))}),{priority:"low"}),s.on("submit",(()=>{o.isEnabled?t.execute("replaceImageSource",{source:s.imageURLInputValue}):t.execute("insertImage",{source:s.imageURLInputValue}),this._closePanel()})),s.on("cancel",(()=>this._closePanel())),r?(r.set({isCollapsed:!0}),r.bind("label").to(this._imageInsertUI,"isImageSelected",(e=>n(e?"Update image URL":"Insert image via URL"))),r):s}_createInsertUrlButton(t){const i=t?v.DropdownButtonView:v.ButtonView,n=this.editor,o=new i(n.locale),a=n.locale.t;return o.set({icon:e.icons.imageUrl,tooltip:!0}),o.bind("label").to(this._imageInsertUI,"isImageSelected",(e=>a(e?"Update image URL":"Insert image via URL"))),o}_closePanel(){this.editor.editing.view.focus(),this._imageInsertUI.dropdownView.isOpen=!1}}class Ve extends e.Plugin{static get pluginName(){return"ImageInsertViaUrl"}static get requires(){return[Be,K]}}class Ue extends e.Plugin{static get pluginName(){return"ImageInsert"}static get requires(){return[Ae,Ve,K]}}class ze extends e.Command{refresh(){const e=this.editor,t=e.plugins.get("ImageUtils").getClosestSelectedImageElement(e.model.document.selection);this.isEnabled=!!t,t&&t.hasAttribute("resizedWidth")?this.value={width:t.getAttribute("resizedWidth"),height:null}:this.value=null}execute(e){const t=this.editor,i=t.model,n=t.plugins.get("ImageUtils"),o=n.getClosestSelectedImageElement(i.document.selection);this.value={width:e.width,height:null},o&&i.change((t=>{t.setAttribute("resizedWidth",e.width,o),t.removeAttribute("resizedHeight",o),n.setImageNaturalSizeAttributes(o)}))}}class Re extends e.Plugin{static get requires(){return[h]}static get pluginName(){return"ImageResizeEditing"}constructor(e){super(e),e.config.define("image",{resizeUnit:"%",resizeOptions:[{name:"resizeImage:original",value:null,icon:"original"},{name:"resizeImage:25",value:"25",icon:"small"},{name:"resizeImage:50",value:"50",icon:"medium"},{name:"resizeImage:75",value:"75",icon:"large"}]})}init(){const e=this.editor,t=new ze(e);this._registerSchema(),this._registerConverters("imageBlock"),this._registerConverters("imageInline"),e.commands.add("resizeImage",t),e.commands.add("imageResize",t)}_registerSchema(){this.editor.plugins.has("ImageBlockEditing")&&this.editor.model.schema.extend("imageBlock",{allowAttributes:["resizedWidth","resizedHeight"]}),this.editor.plugins.has("ImageInlineEditing")&&this.editor.model.schema.extend("imageInline",{allowAttributes:["resizedWidth","resizedHeight"]})}_registerConverters(e){const t=this.editor,i=t.plugins.get("ImageUtils");t.conversion.for("downcast").add((t=>t.on(`attribute:resizedWidth:${e}`,((e,t,i)=>{if(!i.consumable.consume(t.item,e.name))return;const n=i.writer,o=i.mapper.toViewElement(t.item);null!==t.attributeNewValue?(n.setStyle("width",t.attributeNewValue,o),n.addClass("image_resized",o)):(n.removeStyle("width",o),n.removeClass("image_resized",o))})))),t.conversion.for("dataDowncast").attributeToAttribute({model:{name:e,key:"resizedHeight"},view:e=>({key:"style",value:{height:e}})}),t.conversion.for("editingDowncast").add((t=>t.on(`attribute:resizedHeight:${e}`,((t,n,o)=>{if(!o.consumable.consume(n.item,t.name))return;const a=o.writer,s=o.mapper.toViewElement(n.item),r="imageInline"===e?i.findViewImgElement(s):s;null!==n.attributeNewValue?a.setStyle("height",n.attributeNewValue,r):a.removeStyle("height",r)})))),t.conversion.for("upcast").attributeToAttribute({view:{name:"imageBlock"===e?"figure":"img",styles:{width:/.+/}},model:{key:"resizedWidth",value:e=>u(e)?null:e.getStyle("width")}}),t.conversion.for("upcast").attributeToAttribute({view:{name:"imageBlock"===e?"figure":"img",styles:{height:/.+/}},model:{key:"resizedHeight",value:e=>u(e)?null:e.getStyle("height")}})}}const Pe={small:e.icons.objectSizeSmall,medium:e.icons.objectSizeMedium,large:e.icons.objectSizeLarge,original:e.icons.objectSizeFull};class Ne extends e.Plugin{static get requires(){return[Re]}static get pluginName(){return"ImageResizeButtons"}constructor(e){super(e),this._resizeUnit=e.config.get("image.resizeUnit")}init(){const e=this.editor,t=e.config.get("image.resizeOptions"),i=e.commands.get("resizeImage");this.bind("isEnabled").to(i);for(const e of t)this._registerImageResizeButton(e);this._registerImageResizeDropdown(t)}_registerImageResizeButton(e){const t=this.editor,{name:i,value:n,icon:o}=e,a=n?n+this._resizeUnit:null;t.ui.componentFactory.add(i,(i=>{const n=new v.ButtonView(i),s=t.commands.get("resizeImage"),l=this._getOptionLabelValue(e,!0);if(!Pe[o])throw new r.CKEditorError("imageresizebuttons-missing-icon",t,e);return n.set({label:l,icon:Pe[o],tooltip:l,isToggleable:!0}),n.bind("isEnabled").to(this),n.bind("isOn").to(s,"value",Oe(a)),this.listenTo(n,"execute",(()=>{t.execute("resizeImage",{width:a})})),n}))}_registerImageResizeDropdown(e){const t=this.editor,i=t.t,n=e.find((e=>!e.value)),o=o=>{const a=t.commands.get("resizeImage"),s=(0,v.createDropdown)(o,v.DropdownButtonView),r=s.buttonView,l=i("Resize image");return r.set({tooltip:l,commandValue:n.value,icon:Pe.medium,isToggleable:!0,label:this._getOptionLabelValue(n),withText:!0,class:"ck-resize-image-button",ariaLabel:l,ariaLabelledBy:void 0}),r.bind("label").to(a,"value",(e=>e&&e.width?e.width:this._getOptionLabelValue(n))),s.bind("isEnabled").to(this),(0,v.addListToDropdown)(s,(()=>this._getResizeDropdownListItemDefinitions(e,a)),{ariaLabel:i("Image resize list"),role:"menu"}),this.listenTo(s,"execute",(e=>{t.execute(e.source.commandName,{width:e.source.commandValue}),t.editing.view.focus()})),s};t.ui.componentFactory.add("resizeImage",o),t.ui.componentFactory.add("imageResize",o)}_getOptionLabelValue(e,t=!1){const i=this.editor.t;return e.label?e.label:t?e.value?i("Resize image to %0",e.value+this._resizeUnit):i("Resize image to the original size"):e.value?e.value+this._resizeUnit:i("Original")}_getResizeDropdownListItemDefinitions(e,t){const i=new r.Collection;return e.map((e=>{const n=e.value?e.value+this._resizeUnit:null,o={type:"button",model:new v.Model({commandName:"resizeImage",commandValue:n,label:this._getOptionLabelValue(e),role:"menuitemradio",withText:!0,icon:null})};o.model.bind("isOn").to(t,"value",Oe(n)),i.add(o)})),i}}function Oe(e){return t=>null===e&&t===e||null!==t&&t.width===e}const Le="image_resized";class Fe extends e.Plugin{static get requires(){return[l.WidgetResize,h]}static get pluginName(){return"ImageResizeHandles"}init(){const e=this.editor.commands.get("resizeImage");this.bind("isEnabled").to(e),this._setupResizerCreator()}_setupResizerCreator(){const e=this.editor,t=e.editing.view,i=e.plugins.get("ImageUtils");t.addObserver(R),this.listenTo(t.document,"imageLoaded",((n,o)=>{if(!o.target.matches("figure.image.ck-widget > img,figure.image.ck-widget > picture > img,figure.image.ck-widget > a > img,figure.image.ck-widget > a > picture > img,span.image-inline.ck-widget > img,span.image-inline.ck-widget > picture > img"))return;const a=e.editing.view.domConverter,s=a.domToView(o.target),r=i.getImageWidgetFromImageView(s);let c=this.editor.plugins.get(l.WidgetResize).getResizerByViewElement(r);if(c)return void c.redraw();const g=e.editing.mapper,m=g.toModelElement(r);c=e.plugins.get(l.WidgetResize).attachTo({unit:e.config.get("image.resizeUnit"),modelElement:m,viewElement:r,editor:e,getHandleHost:e=>e.querySelector("img"),getResizeHost:()=>a.mapViewToDom(g.toViewElement(m.parent)),isCentered(){const e=m.getAttribute("imageStyle");return!e||"block"==e||"alignCenter"==e},onCommit(i){t.change((e=>{e.removeClass(Le,r)})),e.execute("resizeImage",{width:i})}}),c.on("updateSize",(()=>{r.hasClass(Le)||t.change((e=>{e.addClass(Le,r)}));const e="imageInline"===m.name?s:r;e.getStyle("height")&&t.change((t=>{t.removeStyle("height",e)}))})),c.bind("isEnabled").to(this)}))}}var De=i(968),je={injectType:"singletonStyleTag",attributes:{"data-cke":!0},insert:"head",singleton:!0};_()(De.Z,je);De.Z.locals;class We extends e.Plugin{static get requires(){return[Re,Fe,Ne]}static get pluginName(){return"ImageResize"}}class Me extends e.Command{constructor(e,t){super(e),this._defaultStyles={imageBlock:!1,imageInline:!1},this._styles=new Map(t.map((e=>{if(e.isDefault)for(const t of e.modelElements)this._defaultStyles[t]=e.name;return[e.name,e]})))}refresh(){const e=this.editor.plugins.get("ImageUtils").getClosestSelectedImageElement(this.editor.model.document.selection);this.isEnabled=!!e,this.isEnabled?e.hasAttribute("imageStyle")?this.value=e.getAttribute("imageStyle"):this.value=this._defaultStyles[e.name]:this.value=!1}execute(e={}){const t=this.editor,i=t.model,n=t.plugins.get("ImageUtils");i.change((t=>{const o=e.value,{setImageSizes:a=!0}=e;let s=n.getClosestSelectedImageElement(i.document.selection);o&&this.shouldConvertImageType(o,s)&&(this.editor.execute(n.isBlockImage(s)?"imageTypeInline":"imageTypeBlock",{setImageSizes:a}),s=n.getClosestSelectedImageElement(i.document.selection)),!o||this._styles.get(o).isDefault?t.removeAttribute("imageStyle",s):t.setAttribute("imageStyle",o,s),a&&n.setImageNaturalSizeAttributes(s)}))}shouldConvertImageType(e,t){return!this._styles.get(e).modelElements.includes(t.name)}}const{objectFullWidth:qe,objectInline:$e,objectLeft:Ze,objectRight:He,objectCenter:Ke,objectBlockLeft:Ge,objectBlockRight:Je}=e.icons,Qe={get inline(){return{name:"inline",title:"In line",icon:$e,modelElements:["imageInline"],isDefault:!0}},get alignLeft(){return{name:"alignLeft",title:"Left aligned image",icon:Ze,modelElements:["imageBlock","imageInline"],className:"image-style-align-left"}},get alignBlockLeft(){return{name:"alignBlockLeft",title:"Left aligned image",icon:Ge,modelElements:["imageBlock"],className:"image-style-block-align-left"}},get alignCenter(){return{name:"alignCenter",title:"Centered image",icon:Ke,modelElements:["imageBlock"],className:"image-style-align-center"}},get alignRight(){return{name:"alignRight",title:"Right aligned image",icon:He,modelElements:["imageBlock","imageInline"],className:"image-style-align-right"}},get alignBlockRight(){return{name:"alignBlockRight",title:"Right aligned image",icon:Je,modelElements:["imageBlock"],className:"image-style-block-align-right"}},get block(){return{name:"block",title:"Centered image",icon:Ke,modelElements:["imageBlock"],isDefault:!0}},get side(){return{name:"side",title:"Side image",icon:He,modelElements:["imageBlock"],className:"image-style-side"}}},Xe={full:qe,left:Ge,right:Je,center:Ke,inlineLeft:Ze,inlineRight:He,inline:$e},Ye=[{name:"imageStyle:wrapText",title:"Wrap text",defaultItem:"imageStyle:alignLeft",items:["imageStyle:alignLeft","imageStyle:alignRight"]},{name:"imageStyle:breakText",title:"Break text",defaultItem:"imageStyle:block",items:["imageStyle:alignBlockLeft","imageStyle:block","imageStyle:alignBlockRight"]}];function et(e){(0,r.logWarning)("image-style-configuration-definition-invalid",e)}const tt={normalizeStyles:function(e){return(e.configuredStyles.options||[]).map((e=>function(e){e="string"==typeof e?Qe[e]?{...Qe[e]}:{name:e}:function(e,t){const i={...t};for(const n in e)Object.prototype.hasOwnProperty.call(t,n)||(i[n]=e[n]);return i}(Qe[e.name],e);"string"==typeof e.icon&&(e.icon=Xe[e.icon]||e.icon);return e}(e))).filter((t=>function(e,{isBlockPluginLoaded:t,isInlinePluginLoaded:i}){const{modelElements:n,name:o}=e;if(!(n&&n.length&&o))return et({style:e}),!1;{const o=[t?"imageBlock":null,i?"imageInline":null];if(!n.some((e=>o.includes(e))))return(0,r.logWarning)("image-style-missing-dependency",{style:e,missingPlugins:n.map((e=>"imageBlock"===e?"ImageBlockEditing":"ImageInlineEditing"))}),!1}return!0}(t,e)))},getDefaultStylesConfiguration:function(e,t){return e&&t?{options:["inline","alignLeft","alignRight","alignCenter","alignBlockLeft","alignBlockRight","block","side"]}:e?{options:["block","side"]}:t?{options:["inline","alignLeft","alignRight"]}:{}},getDefaultDropdownDefinitions:function(e){return e.has("ImageBlockEditing")&&e.has("ImageInlineEditing")?[...Ye]:[]},warnInvalidStyle:et,DEFAULT_OPTIONS:Qe,DEFAULT_ICONS:Xe,DEFAULT_DROPDOWN_DEFINITIONS:Ye};function it(e,t){for(const i of t)if(i.name===e)return i}class nt extends e.Plugin{static get pluginName(){return"ImageStyleEditing"}static get requires(){return[h]}init(){const{normalizeStyles:e,getDefaultStylesConfiguration:t}=tt,i=this.editor,n=i.plugins.has("ImageBlockEditing"),o=i.plugins.has("ImageInlineEditing");i.config.define("image.styles",t(n,o)),this.normalizedStyles=e({configuredStyles:i.config.get("image.styles"),isBlockPluginLoaded:n,isInlinePluginLoaded:o}),this._setupConversion(n,o),this._setupPostFixer(),i.commands.add("imageStyle",new Me(i,this.normalizedStyles))}_setupConversion(e,t){const i=this.editor,n=i.model.schema,o=(a=this.normalizedStyles,(e,t,i)=>{if(!i.consumable.consume(t.item,e.name))return;const n=it(t.attributeNewValue,a),o=it(t.attributeOldValue,a),s=i.mapper.toViewElement(t.item),r=i.writer;o&&r.removeClass(o.className,s),n&&r.addClass(n.className,s)});var a;const s=function(e){const t={imageInline:e.filter((e=>!e.isDefault&&e.modelElements.includes("imageInline"))),imageBlock:e.filter((e=>!e.isDefault&&e.modelElements.includes("imageBlock")))};return(e,i,n)=>{if(!i.modelRange)return;const o=i.viewItem,a=(0,r.first)(i.modelRange.getItems());if(a&&n.schema.checkAttribute(a,"imageStyle"))for(const e of t[a.name])n.consumable.consume(o,{classes:e.className})&&n.writer.setAttribute("imageStyle",e.name,a)}}(this.normalizedStyles);i.editing.downcastDispatcher.on("attribute:imageStyle",o),i.data.downcastDispatcher.on("attribute:imageStyle",o),e&&(n.extend("imageBlock",{allowAttributes:"imageStyle"}),i.data.upcastDispatcher.on("element:figure",s,{priority:"low"})),t&&(n.extend("imageInline",{allowAttributes:"imageStyle"}),i.data.upcastDispatcher.on("element:img",s,{priority:"low"}))}_setupPostFixer(){const e=this.editor,t=e.model.document,i=e.plugins.get(h),n=new Map(this.normalizedStyles.map((e=>[e.name,e])));t.registerPostFixer((e=>{let o=!1;for(const a of t.differ.getChanges())if("insert"==a.type||"attribute"==a.type&&"imageStyle"==a.attributeKey){let t="insert"==a.type?a.position.nodeAfter:a.range.start.nodeAfter;if(t&&t.is("element","paragraph")&&t.childCount>0&&(t=t.getChild(0)),!i.isImage(t))continue;const s=t.getAttribute("imageStyle");if(!s)continue;const r=n.get(s);r&&r.modelElements.includes(t.name)||(e.removeAttribute("imageStyle",t),o=!0)}return o}))}}const ot=function(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)};const at=function(e){return e};var st=i(151),rt={injectType:"singletonStyleTag",attributes:{"data-cke":!0},insert:"head",singleton:!0};_()(st.Z,rt);st.Z.locals;class lt extends e.Plugin{static get requires(){return[nt]}static get pluginName(){return"ImageStyleUI"}get localizedDefaultStylesTitles(){const e=this.editor.t;return{"Wrap text":e("Wrap text"),"Break text":e("Break text"),"In line":e("In line"),"Full size image":e("Full size image"),"Side image":e("Side image"),"Left aligned image":e("Left aligned image"),"Centered image":e("Centered image"),"Right aligned image":e("Right aligned image")}}init(){const e=this.editor.plugins,t=this.editor.config.get("image.toolbar")||[],i=ct(e.get("ImageStyleEditing").normalizedStyles,this.localizedDefaultStylesTitles);for(const e of i)this._createButton(e);const n=ct([...t.filter(ot),...tt.getDefaultDropdownDefinitions(e)],this.localizedDefaultStylesTitles);for(const e of n)this._createDropdown(e,i)}_createDropdown(e,t){const i=this.editor.ui.componentFactory;i.add(e.name,(n=>{let o;const{defaultItem:a,items:s,title:r}=e,l=s.filter((e=>t.find((({name:t})=>gt(t)===e)))).map((e=>{const t=i.create(e);return e===a&&(o=t),t}));s.length!==l.length&&tt.warnInvalidStyle({dropdown:e});const c=(0,v.createDropdown)(n,v.SplitButtonView),g=c.buttonView,m=g.arrowView;return(0,v.addToolbarToDropdown)(c,l,{enableActiveItemFocusOnDropdownOpen:!0}),g.set({label:mt(r,o.label),class:null,tooltip:!0}),m.unbind("label"),m.set({label:r}),g.bind("icon").toMany(l,"isOn",((...e)=>{const t=e.findIndex(at);return t<0?o.icon:l[t].icon})),g.bind("label").toMany(l,"isOn",((...e)=>{const t=e.findIndex(at);return mt(r,t<0?o.label:l[t].label)})),g.bind("isOn").toMany(l,"isOn",((...e)=>e.some(at))),g.bind("class").toMany(l,"isOn",((...e)=>e.some(at)?"ck-splitbutton_flatten":void 0)),g.on("execute",(()=>{l.some((({isOn:e})=>e))?c.isOpen=!c.isOpen:o.fire("execute")})),c.bind("isEnabled").toMany(l,"isEnabled",((...e)=>e.some(at))),this.listenTo(c,"execute",(()=>{this.editor.editing.view.focus()})),c}))}_createButton(e){const t=e.name;this.editor.ui.componentFactory.add(gt(t),(i=>{const n=this.editor.commands.get("imageStyle"),o=new v.ButtonView(i);return o.set({label:e.title,icon:e.icon,tooltip:!0,isToggleable:!0}),o.bind("isEnabled").to(n,"isEnabled"),o.bind("isOn").to(n,"value",(e=>e===t)),o.on("execute",this._executeCommand.bind(this,t)),o}))}_executeCommand(e){this.editor.execute("imageStyle",{value:e}),this.editor.editing.view.focus()}}function ct(e,t){for(const i of e)t[i.title]&&(i.title=t[i.title]);return e}function gt(e){return`imageStyle:${e}`}function mt(e,t){return(e?e+": ":"")+t}class dt extends e.Plugin{static get requires(){return[nt,lt]}static get pluginName(){return"ImageStyle"}}class ut extends e.Plugin{static get requires(){return[l.WidgetToolbarRepository,h]}static get pluginName(){return"ImageToolbar"}afterInit(){const e=this.editor,t=e.t,i=e.plugins.get(l.WidgetToolbarRepository),n=e.plugins.get("ImageUtils");var o;i.register("image",{ariaLabel:t("Image toolbar"),items:(o=e.config.get("image.toolbar")||[],o.map((e=>ot(e)?e.name:e))),getRelatedElement:e=>n.getClosestSelectedImageWidget(e)})}}class pt extends e.Plugin{static get requires(){return[O,h]}static get pluginName(){return"PictureEditing"}afterInit(){const e=this.editor;e.plugins.has("ImageBlockEditing")&&e.model.schema.extend("imageBlock",{allowAttributes:["sources"]}),e.plugins.has("ImageInlineEditing")&&e.model.schema.extend("imageInline",{allowAttributes:["sources"]}),this._setupConversion(),this._setupImageUploadEditingIntegration()}_setupConversion(){const e=this.editor,t=e.conversion,i=e.plugins.get("ImageUtils");t.for("upcast").add(function(e){const t=["srcset","media","type","sizes"],i=(i,n,o)=>{const a=n.viewItem;if(!o.consumable.test(a,{name:!0}))return;const s=new Map;for(const e of a.getChildren())if(e.is("element","source")){const i={};for(const n of t)e.hasAttribute(n)&&o.consumable.test(e,{attributes:n})&&(i[n]=e.getAttribute(n));Object.keys(i).length&&s.set(e,i)}const l=e.findViewImgElement(a);if(!l)return;let c=n.modelCursor.parent;if(!c.is("element","imageBlock")){const e=o.convertItem(l,n.modelCursor);n.modelRange=e.modelRange,n.modelCursor=e.modelCursor,c=(0,r.first)(e.modelRange.getItems())}o.consumable.consume(a,{name:!0});for(const[e,t]of s)o.consumable.consume(e,{attributes:Object.keys(t)});s.size&&o.writer.setAttribute("sources",Array.from(s.values()),c),o.convertChildren(a,c)};return e=>{e.on("element:picture",i)}}(i)),t.for("downcast").add(function(e){const t=(t,i,n)=>{if(!n.consumable.consume(i.item,t.name))return;const o=n.writer,a=n.mapper.toViewElement(i.item),s=e.findViewImgElement(a),r=i.attributeNewValue;if(r&&r.length){const e=o.createContainerElement("picture",null,r.map((e=>o.createEmptyElement("source",e)))),t=[];let i=s.parent;for(;i&&i.is("attributeElement");){const e=i.parent;o.unwrap(o.createRangeOn(s),i),t.unshift(i),i=e}o.insert(o.createPositionBefore(s),e),o.move(o.createRangeOn(s),o.createPositionAt(e,"end"));for(const i of t)o.wrap(o.createRangeOn(e),i)}else if(s.parent.is("element","picture")){const e=s.parent;o.move(o.createRangeOn(s),o.createPositionBefore(e)),o.remove(e)}};return e=>{e.on("attribute:sources:imageBlock",t),e.on("attribute:sources:imageInline",t)}}(i))}_setupImageUploadEditingIntegration(){const e=this.editor;if(!e.plugins.has("ImageUploadEditing"))return;const t=e.plugins.get("ImageUploadEditing");this.listenTo(t,"uploadComplete",((t,{imageElement:i,data:n})=>{const o=n.sources;o&&e.model.change((e=>{e.setAttributes({sources:o},i)}))}))}}})(),(window.CKEditor5=window.CKEditor5||{}).image=n})();;
/* @license GNU-GPL-2.0-or-later https://www.drupal.org/licensing/faq */
!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.CKEditor5=t():(e.CKEditor5=e.CKEditor5||{},e.CKEditor5.drupalImage=t())}(globalThis,(()=>(()=>{var e={"ckeditor5/src/core.js":(e,t,i)=>{e.exports=i("dll-reference CKEditor5.dll")("./src/core.js")},"ckeditor5/src/engine.js":(e,t,i)=>{e.exports=i("dll-reference CKEditor5.dll")("./src/engine.js")},"ckeditor5/src/ui.js":(e,t,i)=>{e.exports=i("dll-reference CKEditor5.dll")("./src/ui.js")},"ckeditor5/src/upload.js":(e,t,i)=>{e.exports=i("dll-reference CKEditor5.dll")("./src/upload.js")},"ckeditor5/src/utils.js":(e,t,i)=>{e.exports=i("dll-reference CKEditor5.dll")("./src/utils.js")},"dll-reference CKEditor5.dll":e=>{"use strict";e.exports=CKEditor5.dll}},t={};function i(r){var s=t[r];if(void 0!==s)return s.exports;var n=t[r]={exports:{}};return e[r](n,n.exports,i),n.exports}i.d=(e,t)=>{for(var r in t)i.o(t,r)&&!i.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},i.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t);var r={};return(()=>{"use strict";i.d(r,{default:()=>B});var e=i("ckeditor5/src/core.js");function t(e,t,i){if(t.attributes)for(const[r,s]of Object.entries(t.attributes))e.setAttribute(r,s,i);t.styles&&e.setStyle(t.styles,i),t.classes&&e.addClass(t.classes,i)}var s=i("ckeditor5/src/engine.js");class n extends s.Observer{observe(e){this.listenTo(e,"load",((e,t)=>{const i=t.target;this.checkShouldIgnoreEventFromTarget(i)||"IMG"==i.tagName&&this._fireEvents(t)}),{useCapture:!0})}stopObserving(e){this.stopListening(e)}_fireEvents(e){this.isEnabled&&(this.document.fire("layoutChanged"),this.document.fire("imageLoaded",e))}}function o(e){return e.createEmptyElement("img")}function a(e){const t=parseFloat(e);return!Number.isNaN(t)&&e===String(t)}function l(e){return"string"==typeof e&&e.endsWith("%")?e:`${parseInt(e,10)}`}const u=[{modelValue:"alignCenter",dataValue:"center"},{modelValue:"alignRight",dataValue:"right"},{modelValue:"alignLeft",dataValue:"left"}];class d extends e.Plugin{static get requires(){return["ImageUtils"]}static get pluginName(){return"DrupalImageEditing"}init(){const{editor:e}=this,{conversion:i}=e,{schema:r}=e.model;if(r.isRegistered("imageInline")&&r.extend("imageInline",{allowAttributes:["dataEntityUuid","dataEntityType","isDecorative"]}),r.isRegistered("imageBlock")&&r.extend("imageBlock",{allowAttributes:["dataEntityUuid","dataEntityType","isDecorative"]}),i.for("upcast").add(function(e){function t(t,i,r){const{viewItem:s}=i,{writer:n,consumable:o,safeInsert:a,updateConversionResult:l,schema:d}=r,c=[];let m;if(!o.test(s,{name:!0,attributes:"src"}))return;const g=o.test(s,{name:!0,attributes:"data-caption"});if(m=d.checkChild(i.modelCursor,"imageInline")&&!g?n.createElement("imageInline",{src:s.getAttribute("src")}):n.createElement("imageBlock",{src:s.getAttribute("src")}),e.plugins.has("ImageStyleEditing")&&o.test(s,{name:!0,attributes:"data-align"})){const e=s.getAttribute("data-align"),t=u.find((t=>t.dataValue===e));t&&(n.setAttribute("imageStyle",t.modelValue,m),c.push("data-align"))}if(g){const t=n.createElement("caption"),i=e.data.processor.toView(s.getAttribute("data-caption"));r.consumable.constructor.createFrom(i,r.consumable),r.convertChildren(i,t),n.append(t,m),c.push("data-caption")}o.test(s,{name:!0,attributes:"data-entity-uuid"})&&(n.setAttribute("dataEntityUuid",s.getAttribute("data-entity-uuid"),m),c.push("data-entity-uuid")),o.test(s,{name:!0,attributes:"data-entity-type"})&&(n.setAttribute("dataEntityType",s.getAttribute("data-entity-type"),m),c.push("data-entity-type")),a(m,i.modelCursor)&&(o.consume(s,{name:!0,attributes:c}),l(m,i))}return e=>{e.on("element:img",t,{priority:"high"})}}(e)).attributeToAttribute({view:{name:"img",key:"width"},model:{key:"resizedWidth",value:e=>a(e.getAttribute("width"))?`${parseInt(e.getAttribute("width"),10)}px`:e.getAttribute("width").trim()}}).attributeToAttribute({view:{name:"img",key:"height"},model:{key:"resizedHeight",value:e=>a(e.getAttribute("height"))?`${parseInt(e.getAttribute("height"),10)}px`:e.getAttribute("height").trim()}}),e.plugins.has("DataFilter")){const t=e.plugins.get("DataFilter");i.for("upcast").add(function(e){function t(t,i,r){if(!i.modelRange)return;const s=i.viewItem.parent;if(!s.is("element","a"))return;if(!i.modelRange.getContainedElement().is("element","imageBlock"))return;const n=e.processViewAttributes(s,r);n&&r.writer.setAttribute("htmlLinkAttributes",n,i.modelRange)}return e=>{e.on("element:img",t,{priority:"high"})}}(t))}i.for("downcast").add(function(){function e(e,t,i){const{item:r}=t,{consumable:s,writer:n}=i;if(!s.consume(r,e.name))return;const o=i.mapper.toViewElement(r),a=Array.from(o.getChildren()).find((e=>"img"===e.name));n.setAttribute("data-entity-uuid",t.attributeNewValue,a||o)}return t=>{t.on("attribute:dataEntityUuid",e)}}()).add(function(){function e(e,t,i){const{item:r}=t,{consumable:s,writer:n}=i;if(!s.consume(r,e.name))return;const o=i.mapper.toViewElement(r),a=Array.from(o.getChildren()).find((e=>"img"===e.name));n.setAttribute("data-entity-type",t.attributeNewValue,a||o)}return t=>{t.on("attribute:dataEntityType",e)}}()),i.for("dataDowncast").add(function(e){return t=>{t.on("insert:caption",((t,i,r)=>{const{consumable:s,writer:n,mapper:o}=r;if(!e.plugins.get("ImageUtils").isImage(i.item.parent)||!s.consume(i.item,"insert"))return;const a=e.model.createRangeIn(i.item),l=n.createDocumentFragment();o.bindElements(i.item,l);for(const{item:t}of Array.from(a)){const i={item:t,range:e.model.createRangeOn(t)},s=`insert:${t.name||"$text"}`;e.data.downcastDispatcher.fire(s,i,r);for(const s of t.getAttributeKeys())Object.assign(i,{attributeKey:s,attributeOldValue:null,attributeNewValue:i.item.getAttribute(s)}),e.data.downcastDispatcher.fire(`attribute:${s}`,i,r)}for(const e of n.createRangeIn(l).getItems())o.unbindViewElement(e);o.unbindViewElement(l);const u=e.data.processor.toData(l);if(u){const e=o.toViewElement(i.item.parent);n.setAttribute("data-caption",u,e)}}),{priority:"high"})}}(e)).elementToElement({model:"imageBlock",view:(e,{writer:t})=>o(t),converterPriority:"high"}).elementToElement({model:"imageInline",view:(e,{writer:t})=>o(t),converterPriority:"high"}).add(function(){function e(e,t,i){const{item:r}=t,{consumable:s,writer:n}=i,o=u.find((e=>e.modelValue===t.attributeNewValue));if(!o||!s.consume(r,e.name))return;const a=i.mapper.toViewElement(r),l=Array.from(a.getChildren()).find((e=>"img"===e.name));n.setAttribute("data-align",o.dataValue,l||a)}return t=>{t.on("attribute:imageStyle",e,{priority:"high"})}}()).add(function(){function e(e,i,r){if(!r.consumable.consume(i.item,e.name))return;const s=r.mapper.toViewElement(i.item),n=r.writer,o=n.createContainerElement("a",{href:i.attributeNewValue});n.insert(n.createPositionBefore(s),o),n.move(n.createRangeOn(s),n.createPositionAt(o,0)),r.consumable.consume(i.item,"attribute:htmlLinkAttributes:imageBlock")&&t(r.writer,i.item.getAttribute("htmlLinkAttributes"),o)}return t=>{t.on("attribute:linkHref:imageBlock",e,{priority:"high"})}}()).attributeToAttribute({model:{name:"imageBlock",key:"resizedWidth"},view:e=>({key:"width",value:l(e)}),converterPriority:"high"}).attributeToAttribute({model:{name:"imageInline",key:"resizedWidth"},view:e=>({key:"width",value:l(e)}),converterPriority:"high"}).attributeToAttribute({model:{name:"imageBlock",key:"resizedHeight"},view:e=>({key:"height",value:l(e)}),converterPriority:"high"}).attributeToAttribute({model:{name:"imageInline",key:"resizedHeight"},view:e=>({key:"height",value:l(e)}),converterPriority:"high"}).attributeToAttribute({model:{name:"imageBlock",key:"width"},view:(e,{consumable:t},i)=>i.item.hasAttribute("resizedWidth")?(t.consume(i.item,"attribute:width"),null):{key:"width",value:e},converterPriority:"high"}).attributeToAttribute({model:{name:"imageInline",key:"width"},view:(e,{consumable:t},i)=>i.item.hasAttribute("resizedWidth")?(t.consume(i.item,"attribute:width"),null):{key:"width",value:e},converterPriority:"high"}).attributeToAttribute({model:{name:"imageBlock",key:"height"},view:(e,t,i)=>{if(i.item.hasAttribute("resizedWidth")){if(i.item.getAttribute("resizedWidth").endsWith("%"))return{key:"height",value:i.item.getAttribute("resizedWidth")};const t=parseInt(i.item.getAttribute("resizedWidth"),10),r=parseInt(i.item.getAttribute("width"),10)/parseInt(e,10);return{key:"height",value:`${Math.round(t/r)}`}}return{key:"height",value:e}},converterPriority:"high"}).attributeToAttribute({model:{name:"imageInline",key:"height"},view:(e,t,i)=>{if(i.item.hasAttribute("resizedWidth")){if(i.item.getAttribute("resizedWidth").endsWith("%"))return{key:"height",value:i.item.getAttribute("resizedWidth")};const t=parseInt(i.item.getAttribute("resizedWidth"),10),r=parseInt(i.item.getAttribute("width"),10)/parseInt(e,10);return{key:"height",value:`${Math.round(t/r)}`}}return{key:"height",value:e}},converterPriority:"high"}),e.editing.view.addObserver(n);const s=e.plugins.get("ImageUtils");e.editing.view.document.on("imageLoaded",((t,i)=>{const r=e.editing.view.domConverter.mapDomToView(i.target);if(!r)return;const n=s.getImageWidgetFromImageView(r);if(!n)return;const o=e.editing.mapper.toModelElement(n);o&&e.model.enqueueChange({isUndoable:!1},(()=>{s.setImageNaturalSizeAttributes(o)}))}))}}class c extends e.Command{refresh(){const e=this.editor.plugins.get("ImageUtils").getClosestSelectedImageElement(this.editor.model.document.selection);this.isEnabled=!!e,this.isEnabled&&e.hasAttribute("alt")?this.value=e.getAttribute("alt"):this.value=!1}execute(e){const t=this.editor,i=t.plugins.get("ImageUtils"),r=t.model,s=i.getClosestSelectedImageElement(r.document.selection);r.change((t=>{t.setAttribute("alt",e.newValue,s)}))}}class m extends e.Plugin{static get requires(){return["ImageUtils"]}static get pluginName(){return"DrupalImageAlternativeTextEditing"}constructor(e){super(e),this._missingAltTextViewReferences=new Set}init(){const e=this.editor;e.conversion.for("editingDowncast").add(this._imageEditingDowncastConverter("attribute:alt",e)).add(this._imageEditingDowncastConverter("attribute:src",e)),e.commands.add("imageTextAlternative",new c(this.editor)),e.editing.view.on("render",(()=>{for(const e of this._missingAltTextViewReferences)e.button.element.isConnected||(e.destroy(),this._missingAltTextViewReferences.delete(e))}))}_imageEditingDowncastConverter(e){const t=(e,t,i)=>{const r=this.editor;if(!r.plugins.get("ImageUtils").isImage(t.item))return;const s=i.mapper.toViewElement(t.item),n=Array.from(s.getChildren()).find((e=>e.getCustomProperty("drupalImageMissingAltWarning")));if(t.item.hasAttribute("alt"))return void(n&&i.writer.remove(n));if(n)return;const o=r.ui.componentFactory.create("drupalImageAlternativeTextMissing");o.listenTo(r.ui,"update",(()=>{const e=r.model.document.selection.getFirstRange(),i=r.model.createRangeOn(t.item);o.set({isSelected:e.containsRange(i)||e.isIntersecting(i)})})),o.render(),this._missingAltTextViewReferences.add(o);const a=i.writer.createUIElement("span",{class:"image-alternative-text-missing-wrapper"},(function(e){const t=this.toDomElement(e);return t.appendChild(o.element),t}));i.writer.setCustomProperty("drupalImageMissingAltWarning",!0,a),i.writer.insert(i.writer.createPositionAt(s,"end"),a)};return i=>{i.on(e,t,{priority:"low"})}}}var g=i("ckeditor5/src/ui.js");function h(e){const t=e.plugins.get("ContextualBalloon");if(e.plugins.get("ImageUtils").getClosestSelectedImageWidget(e.editing.view.document.selection)){const i=p(e);t.updatePosition(i)}}function p(e){const t=e.editing.view,i=g.BalloonPanelView.defaultPositions,r=e.plugins.get("ImageUtils");return{target:t.domConverter.mapViewToDom(r.getClosestSelectedImageWidget(t.document.selection)),positions:[i.northArrowSouth,i.northArrowSouthWest,i.northArrowSouthEast,i.southArrowNorth,i.southArrowNorthWest,i.southArrowNorthEast,i.viewportStickyNorth]}}var b=i("ckeditor5/src/utils.js");class f extends g.View{constructor(t){super(t),this.focusTracker=new b.FocusTracker,this.keystrokes=new b.KeystrokeHandler,this.decorativeToggle=this._decorativeToggleView(),this.labeledInput=this._createLabeledInputView(),this.saveButtonView=this._createButton(Drupal.t("Save"),e.icons.check,"ck-button-save"),this.saveButtonView.type="submit",this.saveButtonView.bind("isEnabled").to(this.decorativeToggle,"isOn",this.labeledInput,"isEmpty",((e,t)=>e||!t)),this.cancelButtonView=this._createButton(Drupal.t("Cancel"),e.icons.cancel,"ck-button-cancel","cancel"),this._focusables=new g.ViewCollection,this._focusCycler=new g.FocusCycler({focusables:this._focusables,focusTracker:this.focusTracker,keystrokeHandler:this.keystrokes,actions:{focusPrevious:"shift + tab",focusNext:"tab"}}),this.setTemplate({tag:"form",attributes:{class:["ck","ck-text-alternative-form","ck-text-alternative-form--with-decorative-toggle","ck-responsive-form"],tabindex:"-1"},children:[{tag:"div",attributes:{class:["ck","ck-text-alternative-form__decorative-toggle"]},children:[this.decorativeToggle]},this.labeledInput,this.saveButtonView,this.cancelButtonView]}),(0,g.injectCssTransitionDisabler)(this)}render(){super.render(),this.keystrokes.listenTo(this.element),(0,g.submitHandler)({view:this}),[this.decorativeToggle,this.labeledInput,this.saveButtonView,this.cancelButtonView].forEach((e=>{this._focusables.add(e),this.focusTracker.add(e.element)}))}destroy(){super.destroy(),this.focusTracker.destroy(),this.keystrokes.destroy()}_createButton(e,t,i,r){const s=new g.ButtonView(this.locale);return s.set({label:e,icon:t,tooltip:!0}),s.extendTemplate({attributes:{class:i}}),r&&s.delegate("execute").to(this,r),s}_createLabeledInputView(){const e=new g.LabeledFieldView(this.locale,g.createLabeledInputText);return e.bind("class").to(this.decorativeToggle,"isOn",(e=>e?"ck-hidden":"")),e.label=Drupal.t("Text alternative"),e}_decorativeToggleView(){const e=new g.SwitchButtonView(this.locale);return e.set({withText:!0,label:Drupal.t("Decorative image")}),e.on("execute",(()=>{e.set("isOn",!e.isOn)})),e}}class w extends g.View{constructor(e){super(e);const t=this.bindTemplate;this.set("isVisible"),this.set("isSelected");const i=Drupal.t("Add missing alternative text");this.button=new g.ButtonView(e),this.button.set({label:i,tooltip:!1,withText:!0}),this.setTemplate({tag:"span",attributes:{class:["image-alternative-text-missing",t.to("isVisible",(e=>e?"":"ck-hidden"))],title:i},children:[this.button]})}}class v extends e.Plugin{static get requires(){return[g.ContextualBalloon]}static get pluginName(){return"DrupalImageTextAlternativeUI"}init(){this._createButton(),this._createForm(),this._createMissingAltTextComponent();const e=()=>{this.editor.plugins.get("ImageUtils").getClosestSelectedImageWidget(this.editor.editing.view.document.selection)&&this._showForm()};if(this.editor.commands.get("insertImage")){this.editor.commands.get("insertImage").on("execute",e)}if(this.editor.plugins.has("ImageUploadEditing")){this.editor.plugins.get("ImageUploadEditing").on("uploadComplete",e)}}_createMissingAltTextComponent(){this.editor.ui.componentFactory.add("drupalImageAlternativeTextMissing",(e=>{const t=new w(e);return t.listenTo(t.button,"execute",(()=>{this._isInBalloon&&this._balloon.remove(this._form),this._showForm()})),t.listenTo(this.editor.ui,"update",(()=>{t.set({isVisible:!this._isVisible||!t.isSelected})})),t}))}destroy(){super.destroy(),this._form.destroy()}_createButton(){const t=this.editor;t.ui.componentFactory.add("drupalImageAlternativeText",(i=>{const r=t.commands.get("imageTextAlternative"),s=new g.ButtonView(i);return s.set({label:Drupal.t("Change image alternative text"),icon:e.icons.lowVision,tooltip:!0}),s.bind("isEnabled").to(r,"isEnabled"),this.listenTo(s,"execute",(()=>{this._showForm()})),s}))}_createForm(){const e=this.editor,t=e.editing.view.document,i=e.plugins.get("ImageUtils");this._balloon=this.editor.plugins.get("ContextualBalloon"),this._form=new f(e.locale),this._form.render(),this.listenTo(this._form,"submit",(()=>{e.execute("imageTextAlternative",{newValue:this._form.decorativeToggle.isOn?"":this._form.labeledInput.fieldView.element.value}),this._hideForm(!0)})),this.listenTo(this._form,"cancel",(()=>{this._hideForm(!0)})),this.listenTo(this._form.decorativeToggle,"execute",(()=>{h(e)})),this._form.keystrokes.set("Esc",((e,t)=>{this._hideForm(!0),t()})),this.listenTo(e.ui,"update",(()=>{i.getClosestSelectedImageWidget(t.selection)?this._isVisible&&h(e):this._hideForm(!0)})),(0,g.clickOutsideHandler)({emitter:this._form,activator:()=>this._isVisible,contextElements:[this._balloon.view.element],callback:()=>this._hideForm()})}_showForm(){if(this._isVisible)return;const e=this.editor,t=e.commands.get("imageTextAlternative"),i=this._form.decorativeToggle,r=this._form.labeledInput;this._form.disableCssTransitions(),this._isInBalloon||this._balloon.add({view:this._form,position:p(e)}),i.isOn=""===t.value,r.fieldView.element.value=t.value||"",r.fieldView.value=r.fieldView.element.value,i.isOn?i.focus():r.fieldView.select(),this._form.enableCssTransitions()}_hideForm(e){this._isInBalloon&&(this._form.focusTracker.isFocused&&this._form.saveButtonView.focus(),this._balloon.remove(this._form),e&&this.editor.editing.view.focus())}get _isVisible(){return this._balloon.visibleView===this._form}get _isInBalloon(){return this._balloon.hasView(this._form)}}class y extends e.Plugin{static get requires(){return[m,v]}static get pluginName(){return"DrupalImageAlternativeText"}}class I extends e.Plugin{static get requires(){return[d,y]}static get pluginName(){return"DrupalImage"}}const A=I;class k extends e.Plugin{init(){const{editor:e}=this;e.plugins.get("ImageUploadEditing").on("uploadComplete",((t,{data:i,imageElement:r})=>{e.model.change((e=>{e.setAttribute("dataEntityUuid",i.response.uuid,r),e.setAttribute("dataEntityType",i.response.entity_type,r)}))}))}static get pluginName(){return"DrupalImageUploadEditing"}}var x=i("ckeditor5/src/upload.js");class _{constructor(e,t){this.loader=e,this.options=t}upload(){return this.loader.file.then((e=>new Promise(((t,i)=>{this._initRequest(),this._initListeners(t,i,e),this._sendRequest(e)}))))}abort(){this.xhr&&this.xhr.abort()}_initRequest(){this.xhr=new XMLHttpRequest,this.xhr.open("POST",this.options.uploadUrl,!0),this.xhr.responseType="json"}_initListeners(e,t,i){const r=this.xhr,s=this.loader,n=`Couldn't upload file: ${i.name}.`;r.addEventListener("error",(()=>t(n))),r.addEventListener("abort",(()=>t())),r.addEventListener("load",(()=>{const i=r.response;if(!i||i.error)return t(i&&i.error&&i.error.message?i.error.message:n);e({response:i,urls:{default:i.url}})})),r.upload&&r.upload.addEventListener("progress",(e=>{e.lengthComputable&&(s.uploadTotal=e.total,s.uploaded=e.loaded)}))}_sendRequest(e){const t=this.options.headers||{},i=this.options.withCredentials||!1;Object.keys(t).forEach((e=>{this.xhr.setRequestHeader(e,t[e])})),this.xhr.withCredentials=i;const r=new FormData;r.append("upload",e),this.xhr.send(r)}}class T extends e.Plugin{static get requires(){return[x.FileRepository]}static get pluginName(){return"DrupalFileRepository"}init(){const e=this.editor.config.get("drupalImageUpload");e&&(e.uploadUrl?this.editor.plugins.get(x.FileRepository).createUploadAdapter=t=>new _(t,e):(0,b.logWarning)("simple-upload-adapter-missing-uploadurl"))}}class E extends e.Plugin{static get requires(){return[T,k]}static get pluginName(){return"DrupalImageUpload"}}const V=E;class C extends e.Plugin{init(){const{editor:e}=this;e.ui.componentFactory.add("drupalInsertImage",(()=>e.ui.componentFactory.create("insertImage")))}static get pluginName(){return"DrupalInsertImage"}}const B={DrupalImage:A,DrupalImageUpload:V,DrupalInsertImage:C}})(),r=r.default})()));;
/* @license GNU-GPL-2.0-or-later https://raw.githubusercontent.com/ckeditor/ckeditor5/v40.2.0/LICENSE.md */
/*!
 * @license Copyright (c) 2003-2023, CKSource Holding sp. z o.o. All rights reserved.
 * For licensing, see LICENSE.md.
 */(()=>{var t={704:(t,e,o)=>{t.exports=o(79)("./src/core.js")},492:(t,e,o)=>{t.exports=o(79)("./src/engine.js")},181:(t,e,o)=>{t.exports=o(79)("./src/typing.js")},209:(t,e,o)=>{t.exports=o(79)("./src/utils.js")},79:t=>{"use strict";t.exports=CKEditor5.dll}},e={};function o(i){var s=e[i];if(void 0!==s)return s.exports;var n=e[i]={exports:{}};return t[i](n,n.exports,o),n.exports}o.d=(t,e)=>{for(var i in e)o.o(e,i)&&!o.o(t,i)&&Object.defineProperty(t,i,{enumerable:!0,get:e[i]})},o.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),o.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})};var i={};(()=>{"use strict";o.r(i),o.d(i,{Autoformat:()=>c});var t=o(704),e=o(181),s=o(492),n=o(209);function r(t,e,o,i){let r,d=null;"function"==typeof i?r=i:(d=t.commands.get(i),r=()=>{t.execute(i)}),t.model.document.on("change:data",((a,c)=>{if(d&&!d.isEnabled||!e.isEnabled)return;const l=(0,n.first)(t.model.document.selection.getRanges());if(!l.isCollapsed)return;if(c.isUndo||!c.isLocal)return;const u=Array.from(t.model.document.differ.getChanges()),h=u[0];if(1!=u.length||"insert"!==h.type||"$text"!=h.name||1!=h.length)return;const g=h.position.parent;if(g.is("element","codeBlock"))return;if(g.is("element","listItem")&&"function"!=typeof i&&!["numberedList","bulletedList","todoList"].includes(i))return;if(d&&!0===d.value)return;const m=g.getChild(0),f=t.model.createRangeOn(m);if(!f.containsRange(l)&&!l.end.isEqual(f.end))return;const p=o.exec(m.data.substr(0,l.end.offset));p&&t.model.enqueueChange((e=>{const o=e.createPositionAt(g,0),i=e.createPositionAt(g,p[0].length),n=new s.LiveRange(o,i);if(!1!==r({match:p})){e.remove(n);const o=t.model.document.selection.getFirstRange(),i=e.createRangeIn(g);!g.isEmpty||i.isEqual(o)||i.containsRange(o,!0)||e.remove(g)}n.detach(),t.model.enqueueChange((()=>{t.plugins.get("Delete").requestUndoOnBackspace()}))}))}))}function d(t,e,o,i){let s,n;o instanceof RegExp?s=o:n=o,n=n||(t=>{let e;const o=[],i=[];for(;null!==(e=s.exec(t))&&!(e&&e.length<4);){let{index:t,1:s,2:n,3:r}=e;const d=s+n+r;t+=e[0].length-d.length;const a=[t,t+s.length],c=[t+s.length+n.length,t+s.length+n.length+r.length];o.push(a),o.push(c),i.push([t+s.length,t+s.length+n.length])}return{remove:o,format:i}}),t.model.document.on("change:data",((o,s)=>{if(s.isUndo||!s.isLocal||!e.isEnabled)return;const r=t.model,d=r.document.selection;if(!d.isCollapsed)return;const c=Array.from(r.document.differ.getChanges()),l=c[0];if(1!=c.length||"insert"!==l.type||"$text"!=l.name||1!=l.length)return;const u=d.focus,h=u.parent,{text:g,range:m}=function(t,e){let o=t.start;const i=Array.from(t.getItems()).reduce(((t,i)=>!i.is("$text")&&!i.is("$textProxy")||i.getAttribute("code")?(o=e.createPositionAfter(i),""):t+i.data),"");return{text:i,range:e.createRange(o,t.end)}}(r.createRange(r.createPositionAt(h,0),u),r),f=n(g),p=a(m.start,f.format,r),x=a(m.start,f.remove,r);p.length&&x.length&&r.enqueueChange((e=>{if(!1!==i(e,p)){for(const t of x.reverse())e.remove(t);r.enqueueChange((()=>{t.plugins.get("Delete").requestUndoOnBackspace()}))}}))}))}function a(t,e,o){return e.filter((t=>void 0!==t[0]&&void 0!==t[1])).map((e=>o.createRange(t.getShiftedBy(e[0]),t.getShiftedBy(e[1]))))}class c extends t.Plugin{static get requires(){return[e.Delete]}static get pluginName(){return"Autoformat"}afterInit(){this._addListAutoformats(),this._addBasicStylesAutoformats(),this._addHeadingAutoformats(),this._addBlockQuoteAutoformats(),this._addCodeBlockAutoformats(),this._addHorizontalLineAutoformats()}_addListAutoformats(){const t=this.editor.commands;t.get("bulletedList")&&r(this.editor,this,/^[*-]\s$/,"bulletedList"),t.get("numberedList")&&r(this.editor,this,/^1[.|)]\s$/,"numberedList"),t.get("todoList")&&r(this.editor,this,/^\[\s?\]\s$/,"todoList"),t.get("checkTodoList")&&r(this.editor,this,/^\[\s?x\s?\]\s$/,(()=>{this.editor.execute("todoList"),this.editor.execute("checkTodoList")}))}_addBasicStylesAutoformats(){const t=this.editor.commands;if(t.get("bold")){const t=l(this.editor,"bold");d(this.editor,this,/(?:^|\s)(\*\*)([^*]+)(\*\*)$/g,t),d(this.editor,this,/(?:^|\s)(__)([^_]+)(__)$/g,t)}if(t.get("italic")){const t=l(this.editor,"italic");d(this.editor,this,/(?:^|\s)(\*)([^*_]+)(\*)$/g,t),d(this.editor,this,/(?:^|\s)(_)([^_]+)(_)$/g,t)}if(t.get("code")){const t=l(this.editor,"code");d(this.editor,this,/(`)([^`]+)(`)$/g,t)}if(t.get("strikethrough")){const t=l(this.editor,"strikethrough");d(this.editor,this,/(~~)([^~]+)(~~)$/g,t)}}_addHeadingAutoformats(){const t=this.editor.commands.get("heading");t&&t.modelElements.filter((t=>t.match(/^heading[1-6]$/))).forEach((e=>{const o=e[7],i=new RegExp(`^(#{${o}})\\s$`);r(this.editor,this,i,(()=>{if(!t.isEnabled||t.value===e)return!1;this.editor.execute("heading",{value:e})}))}))}_addBlockQuoteAutoformats(){this.editor.commands.get("blockQuote")&&r(this.editor,this,/^>\s$/,"blockQuote")}_addCodeBlockAutoformats(){const t=this.editor,e=t.model.document.selection;t.commands.get("codeBlock")&&r(t,this,/^```$/,(()=>{if(e.getFirstPosition().parent.is("element","listItem"))return!1;this.editor.execute("codeBlock",{usePreviousLanguageChoice:!0})}))}_addHorizontalLineAutoformats(){this.editor.commands.get("horizontalLine")&&r(this.editor,this,/^---$/,"horizontalLine")}}function l(t,e){return(o,i)=>{if(!t.commands.get(e).isEnabled)return!1;const s=t.model.schema.getValidRanges(i,e);for(const t of s)o.setAttribute(e,!0,t);o.removeSelectionAttribute(e)}}})(),(window.CKEditor5=window.CKEditor5||{}).autoformat=i})();;
!function(e){const t=e.en=e.en||{};t.dictionary=Object.assign(t.dictionary||{},{"Block quote":"Block quote"})}(window.CKEDITOR_TRANSLATIONS||(window.CKEDITOR_TRANSLATIONS={})),
/*!
 * @license Copyright (c) 2003-2023, CKSource Holding sp. z o.o. All rights reserved.
 * For licensing, see LICENSE.md.
 */(()=>{var e={93:(e,t,o)=>{"use strict";o.d(t,{Z:()=>i});var n=o(609),r=o.n(n)()((function(e){return e[1]}));r.push([e.id,".ck-content blockquote{border-left:5px solid #ccc;font-style:italic;margin-left:0;margin-right:0;overflow:hidden;padding-left:1.5em;padding-right:1.5em}.ck-content[dir=rtl] blockquote{border-left:0;border-right:5px solid #ccc}",""]);const i=r},609:e=>{"use strict";e.exports=function(e){var t=[];return t.toString=function(){return this.map((function(t){var o=e(t);return t[2]?"@media ".concat(t[2]," {").concat(o,"}"):o})).join("")},t.i=function(e,o,n){"string"==typeof e&&(e=[[null,e,""]]);var r={};if(n)for(var i=0;i<this.length;i++){var c=this[i][0];null!=c&&(r[c]=!0)}for(var s=0;s<e.length;s++){var l=[].concat(e[s]);n&&r[l[0]]||(o&&(l[2]?l[2]="".concat(o," and ").concat(l[2]):l[2]=o),t.push(l))}},t}},62:(e,t,o)=>{"use strict";var n,r=function(){return void 0===n&&(n=Boolean(window&&document&&document.all&&!window.atob)),n},i=function(){var e={};return function(t){if(void 0===e[t]){var o=document.querySelector(t);if(window.HTMLIFrameElement&&o instanceof window.HTMLIFrameElement)try{o=o.contentDocument.head}catch(e){o=null}e[t]=o}return e[t]}}(),c=[];function s(e){for(var t=-1,o=0;o<c.length;o++)if(c[o].identifier===e){t=o;break}return t}function l(e,t){for(var o={},n=[],r=0;r<e.length;r++){var i=e[r],l=t.base?i[0]+t.base:i[0],a=o[l]||0,u="".concat(l," ").concat(a);o[l]=a+1;var d=s(u),f={css:i[1],media:i[2],sourceMap:i[3]};-1!==d?(c[d].references++,c[d].updater(f)):c.push({identifier:u,updater:v(f,t),references:1}),n.push(u)}return n}function a(e){var t=document.createElement("style"),n=e.attributes||{};if(void 0===n.nonce){var r=o.nc;r&&(n.nonce=r)}if(Object.keys(n).forEach((function(e){t.setAttribute(e,n[e])})),"function"==typeof e.insert)e.insert(t);else{var c=i(e.insert||"head");if(!c)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");c.appendChild(t)}return t}var u,d=(u=[],function(e,t){return u[e]=t,u.filter(Boolean).join("\n")});function f(e,t,o,n){var r=o?"":n.media?"@media ".concat(n.media," {").concat(n.css,"}"):n.css;if(e.styleSheet)e.styleSheet.cssText=d(t,r);else{var i=document.createTextNode(r),c=e.childNodes;c[t]&&e.removeChild(c[t]),c.length?e.insertBefore(i,c[t]):e.appendChild(i)}}function p(e,t,o){var n=o.css,r=o.media,i=o.sourceMap;if(r?e.setAttribute("media",r):e.removeAttribute("media"),i&&"undefined"!=typeof btoa&&(n+="\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(encodeURIComponent(JSON.stringify(i))))," */")),e.styleSheet)e.styleSheet.cssText=n;else{for(;e.firstChild;)e.removeChild(e.firstChild);e.appendChild(document.createTextNode(n))}}var m=null,h=0;function v(e,t){var o,n,r;if(t.singleton){var i=h++;o=m||(m=a(t)),n=f.bind(null,o,i,!1),r=f.bind(null,o,i,!0)}else o=a(t),n=p.bind(null,o,t),r=function(){!function(e){if(null===e.parentNode)return!1;e.parentNode.removeChild(e)}(o)};return n(e),function(t){if(t){if(t.css===e.css&&t.media===e.media&&t.sourceMap===e.sourceMap)return;n(e=t)}else r()}}e.exports=function(e,t){(t=t||{}).singleton||"boolean"==typeof t.singleton||(t.singleton=r());var o=l(e=e||[],t);return function(e){if(e=e||[],"[object Array]"===Object.prototype.toString.call(e)){for(var n=0;n<o.length;n++){var r=s(o[n]);c[r].references--}for(var i=l(e,t),a=0;a<o.length;a++){var u=s(o[a]);0===c[u].references&&(c[u].updater(),c.splice(u,1))}o=i}}}},704:(e,t,o)=>{e.exports=o(79)("./src/core.js")},331:(e,t,o)=>{e.exports=o(79)("./src/enter.js")},181:(e,t,o)=>{e.exports=o(79)("./src/typing.js")},273:(e,t,o)=>{e.exports=o(79)("./src/ui.js")},209:(e,t,o)=>{e.exports=o(79)("./src/utils.js")},79:e=>{"use strict";e.exports=CKEditor5.dll}},t={};function o(n){var r=t[n];if(void 0!==r)return r.exports;var i=t[n]={id:n,exports:{}};return e[n](i,i.exports,o),i.exports}o.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return o.d(t,{a:t}),t},o.d=(e,t)=>{for(var n in t)o.o(t,n)&&!o.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},o.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),o.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},o.nc=void 0;var n={};(()=>{"use strict";o.r(n),o.d(n,{BlockQuote:()=>g,BlockQuoteEditing:()=>u,BlockQuoteUI:()=>v});var e=o(704),t=o(331),r=o(181),i=o(209);class c extends e.Command{refresh(){this.value=this._getValue(),this.isEnabled=this._checkEnabled()}execute(e={}){const t=this.editor.model,o=t.schema,n=t.document.selection,r=Array.from(n.getSelectedBlocks()),i=void 0===e.forceValue?!this.value:e.forceValue;t.change((e=>{if(i){const t=r.filter((e=>s(e)||a(o,e)));this._applyQuote(e,t)}else this._removeQuote(e,r.filter(s))}))}_getValue(){const e=this.editor.model.document.selection,t=(0,i.first)(e.getSelectedBlocks());return!(!t||!s(t))}_checkEnabled(){if(this.value)return!0;const e=this.editor.model.document.selection,t=this.editor.model.schema,o=(0,i.first)(e.getSelectedBlocks());return!!o&&a(t,o)}_removeQuote(e,t){l(e,t).reverse().forEach((t=>{if(t.start.isAtStart&&t.end.isAtEnd)return void e.unwrap(t.start.parent);if(t.start.isAtStart){const o=e.createPositionBefore(t.start.parent);return void e.move(t,o)}t.end.isAtEnd||e.split(t.end);const o=e.createPositionAfter(t.end.parent);e.move(t,o)}))}_applyQuote(e,t){const o=[];l(e,t).reverse().forEach((t=>{let n=s(t.start);n||(n=e.createElement("blockQuote"),e.wrap(t,n)),o.push(n)})),o.reverse().reduce(((t,o)=>t.nextSibling==o?(e.merge(e.createPositionAfter(t)),t):o))}}function s(e){return"blockQuote"==e.parent.name?e.parent:null}function l(e,t){let o,n=0;const r=[];for(;n<t.length;){const i=t[n],c=t[n+1];o||(o=e.createPositionBefore(i)),c&&i.nextSibling==c||(r.push(e.createRange(o,e.createPositionAfter(i))),o=null),n++}return r}function a(e,t){const o=e.checkChild(t.parent,"blockQuote"),n=e.checkChild(["$root","blockQuote"],t);return o&&n}class u extends e.Plugin{static get pluginName(){return"BlockQuoteEditing"}static get requires(){return[t.Enter,r.Delete]}init(){const e=this.editor,t=e.model.schema;e.commands.add("blockQuote",new c(e)),t.register("blockQuote",{inheritAllFrom:"$container"}),e.conversion.elementToElement({model:"blockQuote",view:"blockquote"}),e.model.document.registerPostFixer((o=>{const n=e.model.document.differ.getChanges();for(const e of n)if("insert"==e.type){const n=e.position.nodeAfter;if(!n)continue;if(n.is("element","blockQuote")&&n.isEmpty)return o.remove(n),!0;if(n.is("element","blockQuote")&&!t.checkChild(e.position,n))return o.unwrap(n),!0;if(n.is("element")){const e=o.createRangeIn(n);for(const n of e.getItems())if(n.is("element","blockQuote")&&!t.checkChild(o.createPositionBefore(n),n))return o.unwrap(n),!0}}else if("remove"==e.type){const t=e.position.parent;if(t.is("element","blockQuote")&&t.isEmpty)return o.remove(t),!0}return!1}));const o=this.editor.editing.view.document,n=e.model.document.selection,r=e.commands.get("blockQuote");this.listenTo(o,"enter",((t,o)=>{if(!n.isCollapsed||!r.value)return;n.getLastPosition().parent.isEmpty&&(e.execute("blockQuote"),e.editing.view.scrollToTheSelection(),o.preventDefault(),t.stop())}),{context:"blockquote"}),this.listenTo(o,"delete",((t,o)=>{if("backward"!=o.direction||!n.isCollapsed||!r.value)return;const i=n.getLastPosition().parent;i.isEmpty&&!i.previousSibling&&(e.execute("blockQuote"),e.editing.view.scrollToTheSelection(),o.preventDefault(),t.stop())}),{context:"blockquote"})}}var d=o(273),f=o(62),p=o.n(f),m=o(93),h={injectType:"singletonStyleTag",attributes:{"data-cke":!0},insert:"head",singleton:!0};p()(m.Z,h);m.Z.locals;class v extends e.Plugin{static get pluginName(){return"BlockQuoteUI"}init(){const t=this.editor,o=t.t;t.ui.componentFactory.add("blockQuote",(n=>{const r=t.commands.get("blockQuote"),i=new d.ButtonView(n);return i.set({label:o("Block quote"),icon:e.icons.quote,tooltip:!0,isToggleable:!0}),i.bind("isOn","isEnabled").to(r,"value","isEnabled"),this.listenTo(i,"execute",(()=>{t.execute("blockQuote"),t.editing.view.focus()})),i}))}}class g extends e.Plugin{static get requires(){return[u,v]}static get pluginName(){return"BlockQuote"}}})(),(window.CKEditor5=window.CKEditor5||{}).blockQuote=n})();;
!function(e){const t=e.en=e.en||{};t.dictionary=Object.assign(t.dictionary||{},{Downloadable:"Downloadable","Edit link":"Edit link",Link:"Link","Link image":"Link image","Link URL":"Link URL","Open in a new tab":"Open in a new tab","Open link in new tab":"Open link in new tab","This link has no URL":"This link has no URL",Unlink:"Unlink"})}(window.CKEDITOR_TRANSLATIONS||(window.CKEDITOR_TRANSLATIONS={})),
/*!
 * @license Copyright (c) 2003-2023, CKSource Holding sp. z o.o. All rights reserved.
 * For licensing, see LICENSE.md.
 */(()=>{var e={487:(e,t,i)=>{"use strict";i.d(t,{Z:()=>s});var n=i(609),o=i.n(n)()((function(e){return e[1]}));o.push([e.id,'.ck-vertical-form .ck-button:after{bottom:-1px;content:"";position:absolute;right:-1px;top:-1px;width:0;z-index:1}.ck-vertical-form .ck-button:focus:after{display:none}@media screen and (max-width:600px){.ck.ck-responsive-form .ck-button:after{bottom:-1px;content:"";position:absolute;right:-1px;top:-1px;width:0;z-index:1}.ck.ck-responsive-form .ck-button:focus:after{display:none}}.ck-vertical-form>.ck-button:nth-last-child(2):after{border-right:1px solid var(--ck-color-base-border)}.ck.ck-responsive-form{padding:var(--ck-spacing-large)}.ck.ck-responsive-form:focus{outline:none}[dir=ltr] .ck.ck-responsive-form>:not(:first-child),[dir=rtl] .ck.ck-responsive-form>:not(:last-child){margin-left:var(--ck-spacing-standard)}@media screen and (max-width:600px){.ck.ck-responsive-form{padding:0;width:calc(var(--ck-input-width)*.8)}.ck.ck-responsive-form .ck-labeled-field-view{margin:var(--ck-spacing-large) var(--ck-spacing-large) 0}.ck.ck-responsive-form .ck-labeled-field-view .ck-input-text{min-width:0;width:100%}.ck.ck-responsive-form .ck-labeled-field-view .ck-labeled-field-view__error{white-space:normal}.ck.ck-responsive-form>.ck-button:nth-last-child(2):after{border-right:1px solid var(--ck-color-base-border)}.ck.ck-responsive-form>.ck-button:last-child,.ck.ck-responsive-form>.ck-button:nth-last-child(2){border-radius:0;margin-top:var(--ck-spacing-large);padding:var(--ck-spacing-standard)}.ck.ck-responsive-form>.ck-button:last-child:not(:focus),.ck.ck-responsive-form>.ck-button:nth-last-child(2):not(:focus){border-top:1px solid var(--ck-color-base-border)}[dir=ltr] .ck.ck-responsive-form>.ck-button:last-child,[dir=ltr] .ck.ck-responsive-form>.ck-button:nth-last-child(2),[dir=rtl] .ck.ck-responsive-form>.ck-button:last-child,[dir=rtl] .ck.ck-responsive-form>.ck-button:nth-last-child(2){margin-left:0}[dir=rtl] .ck.ck-responsive-form>.ck-button:last-child:last-of-type,[dir=rtl] .ck.ck-responsive-form>.ck-button:nth-last-child(2):last-of-type{border-right:1px solid var(--ck-color-base-border)}}',""]);const s=o},713:(e,t,i)=>{"use strict";i.d(t,{Z:()=>s});var n=i(609),o=i.n(n)()((function(e){return e[1]}));o.push([e.id,".ck .ck-link_selected{background:var(--ck-color-link-selected-background)}.ck .ck-link_selected span.image-inline{outline:var(--ck-widget-outline-thickness) solid var(--ck-color-link-selected-background)}.ck .ck-fake-link-selection{background:var(--ck-color-link-fake-selection)}.ck .ck-fake-link-selection_collapsed{border-right:1px solid var(--ck-color-base-text);height:100%;margin-right:-1px;outline:1px solid hsla(0,0%,100%,.5)}",""]);const s=o},321:(e,t,i)=>{"use strict";i.d(t,{Z:()=>s});var n=i(609),o=i.n(n)()((function(e){return e[1]}));o.push([e.id,".ck.ck-link-actions{display:flex;flex-direction:row;flex-wrap:nowrap}.ck.ck-link-actions .ck-link-actions__preview{display:inline-block}.ck.ck-link-actions .ck-link-actions__preview .ck-button__label{overflow:hidden}@media screen and (max-width:600px){.ck.ck-link-actions{flex-wrap:wrap}.ck.ck-link-actions .ck-link-actions__preview{flex-basis:100%}.ck.ck-link-actions .ck-button:not(.ck-link-actions__preview){flex-basis:50%}}.ck.ck-link-actions .ck-button.ck-link-actions__preview{padding-left:0;padding-right:0}.ck.ck-link-actions .ck-button.ck-link-actions__preview .ck-button__label{color:var(--ck-color-link-default);cursor:pointer;max-width:var(--ck-input-width);min-width:3em;padding:0 var(--ck-spacing-medium);text-align:center;text-overflow:ellipsis}.ck.ck-link-actions .ck-button.ck-link-actions__preview .ck-button__label:hover{text-decoration:underline}.ck.ck-link-actions .ck-button.ck-link-actions__preview,.ck.ck-link-actions .ck-button.ck-link-actions__preview:active,.ck.ck-link-actions .ck-button.ck-link-actions__preview:focus,.ck.ck-link-actions .ck-button.ck-link-actions__preview:hover{background:none}.ck.ck-link-actions .ck-button.ck-link-actions__preview:active{box-shadow:none}.ck.ck-link-actions .ck-button.ck-link-actions__preview:focus .ck-button__label{text-decoration:underline}[dir=ltr] .ck.ck-link-actions .ck-button:not(:first-child),[dir=rtl] .ck.ck-link-actions .ck-button:not(:last-child){margin-left:var(--ck-spacing-standard)}@media screen and (max-width:600px){.ck.ck-link-actions .ck-button.ck-link-actions__preview{margin:var(--ck-spacing-standard) var(--ck-spacing-standard) 0}.ck.ck-link-actions .ck-button.ck-link-actions__preview .ck-button__label{max-width:100%;min-width:0}[dir=ltr] .ck.ck-link-actions .ck-button:not(.ck-link-actions__preview),[dir=rtl] .ck.ck-link-actions .ck-button:not(.ck-link-actions__preview){margin-left:0}}",""]);const s=o},626:(e,t,i)=>{"use strict";i.d(t,{Z:()=>s});var n=i(609),o=i.n(n)()((function(e){return e[1]}));o.push([e.id,".ck.ck-link-form{display:flex}.ck.ck-link-form .ck-label{display:none}@media screen and (max-width:600px){.ck.ck-link-form{flex-wrap:wrap}.ck.ck-link-form .ck-labeled-field-view{flex-basis:100%}.ck.ck-link-form .ck-button{flex-basis:50%}}.ck.ck-link-form_layout-vertical{display:block}.ck.ck-link-form_layout-vertical .ck-button.ck-button-cancel,.ck.ck-link-form_layout-vertical .ck-button.ck-button-save{margin-top:var(--ck-spacing-medium)}.ck.ck-link-form_layout-vertical{min-width:var(--ck-input-width);padding:0}.ck.ck-link-form_layout-vertical .ck-labeled-field-view{margin:var(--ck-spacing-large) var(--ck-spacing-large) var(--ck-spacing-small)}.ck.ck-link-form_layout-vertical .ck-labeled-field-view .ck-input-text{min-width:0;width:100%}.ck.ck-link-form_layout-vertical>.ck-button{border-radius:0;margin:0;padding:var(--ck-spacing-standard);width:50%}.ck.ck-link-form_layout-vertical>.ck-button:not(:focus){border-top:1px solid var(--ck-color-base-border)}[dir=ltr] .ck.ck-link-form_layout-vertical>.ck-button,[dir=rtl] .ck.ck-link-form_layout-vertical>.ck-button{margin-left:0}[dir=rtl] .ck.ck-link-form_layout-vertical>.ck-button:last-of-type{border-right:1px solid var(--ck-color-base-border)}.ck.ck-link-form_layout-vertical .ck.ck-list{margin:var(--ck-spacing-standard) var(--ck-spacing-large)}.ck.ck-link-form_layout-vertical .ck.ck-list .ck-button.ck-switchbutton{padding:0;width:100%}.ck.ck-link-form_layout-vertical .ck.ck-list .ck-button.ck-switchbutton:hover{background:none}",""]);const s=o},883:(e,t,i)=>{"use strict";i.d(t,{Z:()=>s});var n=i(609),o=i.n(n)()((function(e){return e[1]}));o.push([e.id,'.ck.ck-editor__editable a span.image-inline:after,.ck.ck-editor__editable figure.image>a:after{display:block;position:absolute}:root{--ck-link-image-indicator-icon-size:20;--ck-link-image-indicator-icon-is-visible:clamp(0px,100% - 50px,1px)}.ck.ck-editor__editable a span.image-inline:after,.ck.ck-editor__editable figure.image>a:after{background-color:rgba(0,0,0,.4);background-image:url("data:image/svg+xml;base64,PHN2ZyB2aWV3Qm94PSIwIDAgMjAgMjAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZmlsbD0iI2ZmZiIgZD0ibTExLjA3NyAxNSAuOTkxLTEuNDE2YS43NS43NSAwIDEgMSAxLjIyOS44NmwtMS4xNDggMS42NGEuNzQ4Ljc0OCAwIDAgMS0uMjE3LjIwNiA1LjI1MSA1LjI1MSAwIDAgMS04LjUwMy01Ljk1NS43NDEuNzQxIDAgMCAxIC4xMi0uMjc0bDEuMTQ3LTEuNjM5YS43NS43NSAwIDEgMSAxLjIyOC44Nkw0LjkzMyAxMC43bC4wMDYuMDAzYTMuNzUgMy43NSAwIDAgMCA2LjEzMiA0LjI5NGwuMDA2LjAwNHptNS40OTQtNS4zMzVhLjc0OC43NDggMCAwIDEtLjEyLjI3NGwtMS4xNDcgMS42MzlhLjc1Ljc1IDAgMSAxLTEuMjI4LS44NmwuODYtMS4yM2EzLjc1IDMuNzUgMCAwIDAtNi4xNDQtNC4zMDFsLS44NiAxLjIyOWEuNzUuNzUgMCAwIDEtMS4yMjktLjg2bDEuMTQ4LTEuNjRhLjc0OC43NDggMCAwIDEgLjIxNy0uMjA2IDUuMjUxIDUuMjUxIDAgMCAxIDguNTAzIDUuOTU1em0tNC41NjMtMi41MzJhLjc1Ljc1IDAgMCAxIC4xODQgMS4wNDVsLTMuMTU1IDQuNTA1YS43NS43NSAwIDEgMS0xLjIyOS0uODZsMy4xNTUtNC41MDZhLjc1Ljc1IDAgMCAxIDEuMDQ1LS4xODR6Ii8+PC9zdmc+");background-position:50%;background-repeat:no-repeat;background-size:14px;border-radius:100%;content:"";height:calc(var(--ck-link-image-indicator-icon-is-visible)*var(--ck-link-image-indicator-icon-size));overflow:hidden;right:min(var(--ck-spacing-medium),6%);top:min(var(--ck-spacing-medium),6%);width:calc(var(--ck-link-image-indicator-icon-is-visible)*var(--ck-link-image-indicator-icon-size))}',""]);const s=o},609:e=>{"use strict";e.exports=function(e){var t=[];return t.toString=function(){return this.map((function(t){var i=e(t);return t[2]?"@media ".concat(t[2]," {").concat(i,"}"):i})).join("")},t.i=function(e,i,n){"string"==typeof e&&(e=[[null,e,""]]);var o={};if(n)for(var s=0;s<this.length;s++){var r=this[s][0];null!=r&&(o[r]=!0)}for(var a=0;a<e.length;a++){var c=[].concat(e[a]);n&&o[c[0]]||(i&&(c[2]?c[2]="".concat(i," and ").concat(c[2]):c[2]=i),t.push(c))}},t}},62:(e,t,i)=>{"use strict";var n,o=function(){return void 0===n&&(n=Boolean(window&&document&&document.all&&!window.atob)),n},s=function(){var e={};return function(t){if(void 0===e[t]){var i=document.querySelector(t);if(window.HTMLIFrameElement&&i instanceof window.HTMLIFrameElement)try{i=i.contentDocument.head}catch(e){i=null}e[t]=i}return e[t]}}(),r=[];function a(e){for(var t=-1,i=0;i<r.length;i++)if(r[i].identifier===e){t=i;break}return t}function c(e,t){for(var i={},n=[],o=0;o<e.length;o++){var s=e[o],c=t.base?s[0]+t.base:s[0],l=i[c]||0,u="".concat(c," ").concat(l);i[c]=l+1;var d=a(u),k={css:s[1],media:s[2],sourceMap:s[3]};-1!==d?(r[d].references++,r[d].updater(k)):r.push({identifier:u,updater:g(k,t),references:1}),n.push(u)}return n}function l(e){var t=document.createElement("style"),n=e.attributes||{};if(void 0===n.nonce){var o=i.nc;o&&(n.nonce=o)}if(Object.keys(n).forEach((function(e){t.setAttribute(e,n[e])})),"function"==typeof e.insert)e.insert(t);else{var r=s(e.insert||"head");if(!r)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");r.appendChild(t)}return t}var u,d=(u=[],function(e,t){return u[e]=t,u.filter(Boolean).join("\n")});function k(e,t,i,n){var o=i?"":n.media?"@media ".concat(n.media," {").concat(n.css,"}"):n.css;if(e.styleSheet)e.styleSheet.cssText=d(t,o);else{var s=document.createTextNode(o),r=e.childNodes;r[t]&&e.removeChild(r[t]),r.length?e.insertBefore(s,r[t]):e.appendChild(s)}}function h(e,t,i){var n=i.css,o=i.media,s=i.sourceMap;if(o?e.setAttribute("media",o):e.removeAttribute("media"),s&&"undefined"!=typeof btoa&&(n+="\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(encodeURIComponent(JSON.stringify(s))))," */")),e.styleSheet)e.styleSheet.cssText=n;else{for(;e.firstChild;)e.removeChild(e.firstChild);e.appendChild(document.createTextNode(n))}}var f=null,m=0;function g(e,t){var i,n,o;if(t.singleton){var s=m++;i=f||(f=l(t)),n=k.bind(null,i,s,!1),o=k.bind(null,i,s,!0)}else i=l(t),n=h.bind(null,i,t),o=function(){!function(e){if(null===e.parentNode)return!1;e.parentNode.removeChild(e)}(i)};return n(e),function(t){if(t){if(t.css===e.css&&t.media===e.media&&t.sourceMap===e.sourceMap)return;n(e=t)}else o()}}e.exports=function(e,t){(t=t||{}).singleton||"boolean"==typeof t.singleton||(t.singleton=o());var i=c(e=e||[],t);return function(e){if(e=e||[],"[object Array]"===Object.prototype.toString.call(e)){for(var n=0;n<i.length;n++){var o=a(i[n]);r[o].references--}for(var s=c(e,t),l=0;l<i.length;l++){var u=a(i[l]);0===r[u].references&&(r[u].updater(),r.splice(u,1))}i=s}}}},945:(e,t,i)=>{e.exports=i(79)("./src/clipboard.js")},704:(e,t,i)=>{e.exports=i(79)("./src/core.js")},492:(e,t,i)=>{e.exports=i(79)("./src/engine.js")},181:(e,t,i)=>{e.exports=i(79)("./src/typing.js")},273:(e,t,i)=>{e.exports=i(79)("./src/ui.js")},209:(e,t,i)=>{e.exports=i(79)("./src/utils.js")},995:(e,t,i)=>{e.exports=i(79)("./src/widget.js")},79:e=>{"use strict";e.exports=CKEditor5.dll}},t={};function i(n){var o=t[n];if(void 0!==o)return o.exports;var s=t[n]={id:n,exports:{}};return e[n](s,s.exports,i),s.exports}i.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return i.d(t,{a:t}),t},i.d=(e,t)=>{for(var n in t)i.o(t,n)&&!i.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},i.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),i.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},i.nc=void 0;var n={};(()=>{"use strict";i.r(n),i.d(n,{AutoLink:()=>Ce,Link:()=>Me,LinkCommand:()=>oe,LinkEditing:()=>fe,LinkImage:()=>Oe,LinkImageEditing:()=>je,LinkImageUI:()=>He,LinkUI:()=>Ee,UnlinkCommand:()=>re});var e=i(704),t=i(181),o=i(945),s=i(209);class r{constructor(){this._definitions=new Set}get length(){return this._definitions.size}add(e){Array.isArray(e)?e.forEach((e=>this._definitions.add(e))):this._definitions.add(e)}getDispatcher(){return e=>{e.on("attribute:linkHref",((e,t,i)=>{if(!i.consumable.test(t.item,"attribute:linkHref"))return;if(!t.item.is("selection")&&!i.schema.isInline(t.item))return;const n=i.writer,o=n.document.selection;for(const e of this._definitions){const s=n.createAttributeElement("a",e.attributes,{priority:5});e.classes&&n.addClass(e.classes,s);for(const t in e.styles)n.setStyle(t,e.styles[t],s);n.setCustomProperty("link",!0,s),e.callback(t.attributeNewValue)?t.item.is("selection")?n.wrap(o.getFirstRange(),s):n.wrap(i.mapper.toViewRange(t.range),s):n.unwrap(i.mapper.toViewRange(t.range),s)}}),{priority:"high"})}}getDispatcherForLinkedImage(){return e=>{e.on("attribute:linkHref:imageBlock",((e,t,{writer:i,mapper:n})=>{const o=n.toViewElement(t.item),r=Array.from(o.getChildren()).find((e=>e.is("element","a")));for(const e of this._definitions){const n=(0,s.toMap)(e.attributes);if(e.callback(t.attributeNewValue)){for(const[e,t]of n)"class"===e?i.addClass(t,r):i.setAttribute(e,t,r);e.classes&&i.addClass(e.classes,r);for(const t in e.styles)i.setStyle(t,e.styles[t],r)}else{for(const[e,t]of n)"class"===e?i.removeClass(t,r):i.removeAttribute(e,r);e.classes&&i.removeClass(e.classes,r);for(const t in e.styles)i.removeStyle(t,r)}}}))}}}const a=function(e,t,i){var n=-1,o=e.length;t<0&&(t=-t>o?0:o+t),(i=i>o?o:i)<0&&(i+=o),o=t>i?0:i-t>>>0,t>>>=0;for(var s=Array(o);++n<o;)s[n]=e[n+t];return s};const c=function(e,t,i){var n=e.length;return i=void 0===i?n:i,!t&&i>=n?e:a(e,t,i)};var l=RegExp("[\\u200d\\ud800-\\udfff\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff\\ufe0e\\ufe0f]");const u=function(e){return l.test(e)};const d=function(e){return e.split("")};var k="\\ud800-\\udfff",h="["+k+"]",f="[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]",m="\\ud83c[\\udffb-\\udfff]",g="[^"+k+"]",b="(?:\\ud83c[\\udde6-\\uddff]){2}",p="[\\ud800-\\udbff][\\udc00-\\udfff]",w="(?:"+f+"|"+m+")"+"?",v="[\\ufe0e\\ufe0f]?",_=v+w+("(?:\\u200d(?:"+[g,b,p].join("|")+")"+v+w+")*"),y="(?:"+[g+f+"?",f,b,p,h].join("|")+")",A=RegExp(m+"(?="+m+")|"+y+_,"g");const x=function(e){return e.match(A)||[]};const I=function(e){return u(e)?x(e):d(e)};const V="object"==typeof global&&global&&global.Object===Object&&global;var S="object"==typeof self&&self&&self.Object===Object&&self;const E=(V||S||Function("return this")()).Symbol;const T=function(e,t){for(var i=-1,n=null==e?0:e.length,o=Array(n);++i<n;)o[i]=t(e[i],i,e);return o};const L=Array.isArray;var C=Object.prototype,D=C.hasOwnProperty,M=C.toString,j=E?E.toStringTag:void 0;const B=function(e){var t=D.call(e,j),i=e[j];try{e[j]=void 0;var n=!0}catch(e){}var o=M.call(e);return n&&(t?e[j]=i:delete e[j]),o};var N=Object.prototype.toString;const H=function(e){return N.call(e)};var P=E?E.toStringTag:void 0;const U=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":P&&P in Object(e)?B(e):H(e)};const O=function(e){return null!=e&&"object"==typeof e};const F=function(e){return"symbol"==typeof e||O(e)&&"[object Symbol]"==U(e)};var R=E?E.prototype:void 0,z=R?R.toString:void 0;const Z=function e(t){if("string"==typeof t)return t;if(L(t))return T(t,e)+"";if(F(t))return z?z.call(t):"";var i=t+"";return"0"==i&&1/t==-Infinity?"-0":i};const q=function(e){return null==e?"":Z(e)};const K=function(e){return function(t){t=q(t);var i=u(t)?I(t):void 0,n=i?i[0]:t.charAt(0),o=i?c(i,1).join(""):t.slice(1);return n[e]()+o}}("toUpperCase"),$=/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205f\u3000]/g,Q=/^(?:(?:https?|ftps?|mailto):|[^a-z]|[a-z+.-]+(?:[^a-z+.:-]|$))/i,W=/^[\S]+@((?![-_])(?:[-\w\u00a1-\uffff]{0,63}[^-_]\.))+(?:[a-z\u00a1-\uffff]{2,})$/i,Y=/^((\w+:(\/{2,})?)|(\W))/i,G="Ctrl+K";function J(e,{writer:t}){const i=t.createAttributeElement("a",{href:e},{priority:5});return t.setCustomProperty("link",!0,i),i}function X(e){const t=String(e);return function(e){const t=e.replace($,"");return!!t.match(Q)}(t)?t:"#"}function ee(e,t){return!!e&&t.checkAttribute(e.name,"linkHref")}function te(e,t){const i=(n=e,W.test(n)?"mailto:":t);var n;const o=!!i&&!ie(e);return e&&o?i+e:e}function ie(e){return Y.test(e)}function ne(e){window.open(e,"_blank","noopener")}class oe extends e.Command{constructor(){super(...arguments),this.manualDecorators=new s.Collection,this.automaticDecorators=new r}restoreManualDecoratorStates(){for(const e of this.manualDecorators)e.value=this._getDecoratorStateFromModel(e.id)}refresh(){const e=this.editor.model,t=e.document.selection,i=t.getSelectedElement()||(0,s.first)(t.getSelectedBlocks());ee(i,e.schema)?(this.value=i.getAttribute("linkHref"),this.isEnabled=e.schema.checkAttribute(i,"linkHref")):(this.value=t.getAttribute("linkHref"),this.isEnabled=e.schema.checkAttributeInSelection(t,"linkHref"));for(const e of this.manualDecorators)e.value=this._getDecoratorStateFromModel(e.id)}execute(e,i={}){const n=this.editor.model,o=n.document.selection,r=[],a=[];for(const e in i)i[e]?r.push(e):a.push(e);n.change((i=>{if(o.isCollapsed){const c=o.getFirstPosition();if(o.hasAttribute("linkHref")){const s=se(o);let l=(0,t.findAttributeRange)(c,"linkHref",o.getAttribute("linkHref"),n);o.getAttribute("linkHref")===s&&(l=this._updateLinkContent(n,i,l,e)),i.setAttribute("linkHref",e,l),r.forEach((e=>{i.setAttribute(e,!0,l)})),a.forEach((e=>{i.removeAttribute(e,l)})),i.setSelection(i.createPositionAfter(l.end.nodeBefore))}else if(""!==e){const t=(0,s.toMap)(o.getAttributes());t.set("linkHref",e),r.forEach((e=>{t.set(e,!0)}));const{end:a}=n.insertContent(i.createText(e,t),c);i.setSelection(a)}["linkHref",...r,...a].forEach((e=>{i.removeSelectionAttribute(e)}))}else{const t=n.schema.getValidRanges(o.getRanges(),"linkHref"),s=[];for(const e of o.getSelectedBlocks())n.schema.checkAttribute(e,"linkHref")&&s.push(i.createRangeOn(e));const c=s.slice();for(const e of t)this._isRangeToUpdate(e,s)&&c.push(e);for(const t of c){let s=t;if(1===c.length){const r=se(o);o.getAttribute("linkHref")===r&&(s=this._updateLinkContent(n,i,t,e),i.setSelection(i.createSelection(s)))}i.setAttribute("linkHref",e,s),r.forEach((e=>{i.setAttribute(e,!0,s)})),a.forEach((e=>{i.removeAttribute(e,s)}))}}}))}_getDecoratorStateFromModel(e){const t=this.editor.model,i=t.document.selection,n=i.getSelectedElement();return ee(n,t.schema)?n.getAttribute(e):i.getAttribute(e)}_isRangeToUpdate(e,t){for(const i of t)if(i.containsRange(e))return!1;return!0}_updateLinkContent(e,t,i,n){const o=t.createText(n,{linkHref:n});return e.insertContent(o,i)}}function se(e){if(e.isCollapsed){const t=e.getFirstPosition();return t.textNode&&t.textNode.data}{const t=Array.from(e.getFirstRange().getItems());if(t.length>1)return null;const i=t[0];return i.is("$text")||i.is("$textProxy")?i.data:null}}class re extends e.Command{refresh(){const e=this.editor.model,t=e.document.selection,i=t.getSelectedElement();ee(i,e.schema)?this.isEnabled=e.schema.checkAttribute(i,"linkHref"):this.isEnabled=e.schema.checkAttributeInSelection(t,"linkHref")}execute(){const e=this.editor,i=this.editor.model,n=i.document.selection,o=e.commands.get("link");i.change((e=>{const s=n.isCollapsed?[(0,t.findAttributeRange)(n.getFirstPosition(),"linkHref",n.getAttribute("linkHref"),i)]:i.schema.getValidRanges(n.getRanges(),"linkHref");for(const t of s)if(e.removeAttribute("linkHref",t),o)for(const i of o.manualDecorators)e.removeAttribute(i.id,t)}))}}class ae extends((0,s.ObservableMixin)()){constructor({id:e,label:t,attributes:i,classes:n,styles:o,defaultValue:s}){super(),this.id=e,this.set("value",void 0),this.defaultValue=s,this.label=t,this.attributes=i,this.classes=n,this.styles=o}_createPattern(){return{attributes:this.attributes,classes:this.classes,styles:this.styles}}}var ce=i(62),le=i.n(ce),ue=i(713),de={injectType:"singletonStyleTag",attributes:{"data-cke":!0},insert:"head",singleton:!0};le()(ue.Z,de);ue.Z.locals;const ke="automatic",he=/^(https?:)?\/\//;class fe extends e.Plugin{static get pluginName(){return"LinkEditing"}static get requires(){return[t.TwoStepCaretMovement,t.Input,o.ClipboardPipeline]}constructor(e){super(e),e.config.define("link",{addTargetToExternalLinks:!1})}init(){const e=this.editor;e.model.schema.extend("$text",{allowAttributes:"linkHref"}),e.conversion.for("dataDowncast").attributeToElement({model:"linkHref",view:J}),e.conversion.for("editingDowncast").attributeToElement({model:"linkHref",view:(e,t)=>J(X(e),t)}),e.conversion.for("upcast").elementToAttribute({view:{name:"a",attributes:{href:!0}},model:{key:"linkHref",value:e=>e.getAttribute("href")}}),e.commands.add("link",new oe(e)),e.commands.add("unlink",new re(e));const i=function(e,t){const i={"Open in a new tab":e("Open in a new tab"),Downloadable:e("Downloadable")};return t.forEach((e=>("label"in e&&i[e.label]&&(e.label=i[e.label]),e))),t}(e.t,function(e){const t=[];if(e)for(const[i,n]of Object.entries(e)){const e=Object.assign({},n,{id:`link${K(i)}`});t.push(e)}return t}(e.config.get("link.decorators")));this._enableAutomaticDecorators(i.filter((e=>e.mode===ke))),this._enableManualDecorators(i.filter((e=>"manual"===e.mode)));e.plugins.get(t.TwoStepCaretMovement).registerAttribute("linkHref"),(0,t.inlineHighlight)(e,"linkHref","a","ck-link_selected"),this._enableLinkOpen(),this._enableSelectionAttributesFixer(),this._enableClipboardIntegration()}_enableAutomaticDecorators(e){const t=this.editor,i=t.commands.get("link").automaticDecorators;t.config.get("link.addTargetToExternalLinks")&&i.add({id:"linkIsExternal",mode:ke,callback:e=>!!e&&he.test(e),attributes:{target:"_blank",rel:"noopener noreferrer"}}),i.add(e),i.length&&t.conversion.for("downcast").add(i.getDispatcher())}_enableManualDecorators(e){if(!e.length)return;const t=this.editor,i=t.commands.get("link").manualDecorators;e.forEach((e=>{t.model.schema.extend("$text",{allowAttributes:e.id});const n=new ae(e);i.add(n),t.conversion.for("downcast").attributeToElement({model:n.id,view:(e,{writer:t,schema:i},{item:o})=>{if((o.is("selection")||i.isInline(o))&&e){const e=t.createAttributeElement("a",n.attributes,{priority:5});n.classes&&t.addClass(n.classes,e);for(const i in n.styles)t.setStyle(i,n.styles[i],e);return t.setCustomProperty("link",!0,e),e}}}),t.conversion.for("upcast").elementToAttribute({view:{name:"a",...n._createPattern()},model:{key:n.id}})}))}_enableLinkOpen(){const e=this.editor,t=e.editing.view.document;this.listenTo(t,"click",((e,t)=>{if(!(s.env.isMac?t.domEvent.metaKey:t.domEvent.ctrlKey))return;let i=t.domTarget;if("a"!=i.tagName.toLowerCase()&&(i=i.closest("a")),!i)return;const n=i.getAttribute("href");n&&(e.stop(),t.preventDefault(),ne(n))}),{context:"$capture"}),this.listenTo(t,"keydown",((t,i)=>{const n=e.commands.get("link").value;!!n&&i.keyCode===s.keyCodes.enter&&i.altKey&&(t.stop(),ne(n))}))}_enableSelectionAttributesFixer(){const e=this.editor.model,t=e.document.selection;this.listenTo(t,"change:attribute",((i,{attributeKeys:n})=>{n.includes("linkHref")&&!t.hasAttribute("linkHref")&&e.change((t=>{var i;!function(e,t){e.removeSelectionAttribute("linkHref");for(const i of t)e.removeSelectionAttribute(i)}(t,(i=e.schema,i.getDefinition("$text").allowAttributes.filter((e=>e.startsWith("link")))))}))}))}_enableClipboardIntegration(){const e=this.editor,t=e.model,i=this.editor.config.get("link.defaultProtocol");i&&this.listenTo(e.plugins.get("ClipboardPipeline"),"contentInsertion",((e,n)=>{t.change((e=>{const t=e.createRangeIn(n.content);for(const n of t.getItems())if(n.hasAttribute("linkHref")){const t=te(n.getAttribute("linkHref"),i);e.setAttribute("linkHref",t,n)}}))}))}}var me=i(492),ge=i(273),be=i(995),pe=i(487),we={injectType:"singletonStyleTag",attributes:{"data-cke":!0},insert:"head",singleton:!0};le()(pe.Z,we);pe.Z.locals;var ve=i(626),_e={injectType:"singletonStyleTag",attributes:{"data-cke":!0},insert:"head",singleton:!0};le()(ve.Z,_e);ve.Z.locals;class ye extends ge.View{constructor(t,i){super(t),this.focusTracker=new s.FocusTracker,this.keystrokes=new s.KeystrokeHandler,this._focusables=new ge.ViewCollection;const n=t.t;this.urlInputView=this._createUrlInput(),this.saveButtonView=this._createButton(n("Save"),e.icons.check,"ck-button-save"),this.saveButtonView.type="submit",this.cancelButtonView=this._createButton(n("Cancel"),e.icons.cancel,"ck-button-cancel","cancel"),this._manualDecoratorSwitches=this._createManualDecoratorSwitches(i),this.children=this._createFormChildren(i.manualDecorators),this._focusCycler=new ge.FocusCycler({focusables:this._focusables,focusTracker:this.focusTracker,keystrokeHandler:this.keystrokes,actions:{focusPrevious:"shift + tab",focusNext:"tab"}});const o=["ck","ck-link-form","ck-responsive-form"];i.manualDecorators.length&&o.push("ck-link-form_layout-vertical","ck-vertical-form"),this.setTemplate({tag:"form",attributes:{class:o,tabindex:"-1"},children:this.children})}getDecoratorSwitchesState(){return Array.from(this._manualDecoratorSwitches).reduce(((e,t)=>(e[t.name]=t.isOn,e)),{})}render(){super.render(),(0,ge.submitHandler)({view:this});[this.urlInputView,...this._manualDecoratorSwitches,this.saveButtonView,this.cancelButtonView].forEach((e=>{this._focusables.add(e),this.focusTracker.add(e.element)})),this.keystrokes.listenTo(this.element)}destroy(){super.destroy(),this.focusTracker.destroy(),this.keystrokes.destroy()}focus(){this._focusCycler.focusFirst()}_createUrlInput(){const e=this.locale.t,t=new ge.LabeledFieldView(this.locale,ge.createLabeledInputText);return t.label=e("Link URL"),t}_createButton(e,t,i,n){const o=new ge.ButtonView(this.locale);return o.set({label:e,icon:t,tooltip:!0}),o.extendTemplate({attributes:{class:i}}),n&&o.delegate("execute").to(this,n),o}_createManualDecoratorSwitches(e){const t=this.createCollection();for(const i of e.manualDecorators){const n=new ge.SwitchButtonView(this.locale);n.set({name:i.id,label:i.label,withText:!0}),n.bind("isOn").toMany([i,e],"value",((e,t)=>void 0===t&&void 0===e?!!i.defaultValue:!!e)),n.on("execute",(()=>{i.set("value",!n.isOn)})),t.add(n)}return t}_createFormChildren(e){const t=this.createCollection();if(t.add(this.urlInputView),e.length){const e=new ge.View;e.setTemplate({tag:"ul",children:this._manualDecoratorSwitches.map((e=>({tag:"li",children:[e],attributes:{class:["ck","ck-list__item"]}}))),attributes:{class:["ck","ck-reset","ck-list"]}}),t.add(e)}return t.add(this.saveButtonView),t.add(this.cancelButtonView),t}}var Ae=i(321),xe={injectType:"singletonStyleTag",attributes:{"data-cke":!0},insert:"head",singleton:!0};le()(Ae.Z,xe);Ae.Z.locals;class Ie extends ge.View{constructor(t){super(t),this.focusTracker=new s.FocusTracker,this.keystrokes=new s.KeystrokeHandler,this._focusables=new ge.ViewCollection;const i=t.t;this.previewButtonView=this._createPreviewButton(),this.unlinkButtonView=this._createButton(i("Unlink"),'<svg viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="m11.077 15 .991-1.416a.75.75 0 1 1 1.229.86l-1.148 1.64a.748.748 0 0 1-.217.206 5.251 5.251 0 0 1-8.503-5.955.741.741 0 0 1 .12-.274l1.147-1.639a.75.75 0 1 1 1.228.86L4.933 10.7l.006.003a3.75 3.75 0 0 0 6.132 4.294l.006.004zm5.494-5.335a.748.748 0 0 1-.12.274l-1.147 1.639a.75.75 0 1 1-1.228-.86l.86-1.23a3.75 3.75 0 0 0-6.144-4.301l-.86 1.229a.75.75 0 0 1-1.229-.86l1.148-1.64a.748.748 0 0 1 .217-.206 5.251 5.251 0 0 1 8.503 5.955zm-4.563-2.532a.75.75 0 0 1 .184 1.045l-3.155 4.505a.75.75 0 1 1-1.229-.86l3.155-4.506a.75.75 0 0 1 1.045-.184zm4.919 10.562-1.414 1.414a.75.75 0 1 1-1.06-1.06l1.414-1.415-1.415-1.414a.75.75 0 0 1 1.061-1.06l1.414 1.414 1.414-1.415a.75.75 0 0 1 1.061 1.061l-1.414 1.414 1.414 1.415a.75.75 0 0 1-1.06 1.06l-1.415-1.414z"/></svg>',"unlink"),this.editButtonView=this._createButton(i("Edit link"),e.icons.pencil,"edit"),this.set("href",void 0),this._focusCycler=new ge.FocusCycler({focusables:this._focusables,focusTracker:this.focusTracker,keystrokeHandler:this.keystrokes,actions:{focusPrevious:"shift + tab",focusNext:"tab"}}),this.setTemplate({tag:"div",attributes:{class:["ck","ck-link-actions","ck-responsive-form"],tabindex:"-1"},children:[this.previewButtonView,this.editButtonView,this.unlinkButtonView]})}render(){super.render();[this.previewButtonView,this.editButtonView,this.unlinkButtonView].forEach((e=>{this._focusables.add(e),this.focusTracker.add(e.element)})),this.keystrokes.listenTo(this.element)}destroy(){super.destroy(),this.focusTracker.destroy(),this.keystrokes.destroy()}focus(){this._focusCycler.focusFirst()}_createButton(e,t,i){const n=new ge.ButtonView(this.locale);return n.set({label:e,icon:t,tooltip:!0}),n.delegate("execute").to(this,i),n}_createPreviewButton(){const e=new ge.ButtonView(this.locale),t=this.bindTemplate,i=this.t;return e.set({withText:!0,tooltip:i("Open link in new tab")}),e.extendTemplate({attributes:{class:["ck","ck-link-actions__preview"],href:t.to("href",(e=>e&&X(e))),target:"_blank",rel:"noopener noreferrer"}}),e.bind("label").to(this,"href",(e=>e||i("This link has no URL"))),e.bind("isEnabled").to(this,"href",(e=>!!e)),e.template.tag="a",e.template.eventListeners={},e}}const Ve='<svg viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="m11.077 15 .991-1.416a.75.75 0 1 1 1.229.86l-1.148 1.64a.748.748 0 0 1-.217.206 5.251 5.251 0 0 1-8.503-5.955.741.741 0 0 1 .12-.274l1.147-1.639a.75.75 0 1 1 1.228.86L4.933 10.7l.006.003a3.75 3.75 0 0 0 6.132 4.294l.006.004zm5.494-5.335a.748.748 0 0 1-.12.274l-1.147 1.639a.75.75 0 1 1-1.228-.86l.86-1.23a3.75 3.75 0 0 0-6.144-4.301l-.86 1.229a.75.75 0 0 1-1.229-.86l1.148-1.64a.748.748 0 0 1 .217-.206 5.251 5.251 0 0 1 8.503 5.955zm-4.563-2.532a.75.75 0 0 1 .184 1.045l-3.155 4.505a.75.75 0 1 1-1.229-.86l3.155-4.506a.75.75 0 0 1 1.045-.184z"/></svg>',Se="link-ui";class Ee extends e.Plugin{constructor(){super(...arguments),this.actionsView=null,this.formView=null}static get requires(){return[ge.ContextualBalloon]}static get pluginName(){return"LinkUI"}init(){const e=this.editor;e.editing.view.addObserver(me.ClickObserver),this._balloon=e.plugins.get(ge.ContextualBalloon),this._createToolbarLinkButton(),this._enableBalloonActivators(),e.conversion.for("editingDowncast").markerToHighlight({model:Se,view:{classes:["ck-fake-link-selection"]}}),e.conversion.for("editingDowncast").markerToElement({model:Se,view:{name:"span",classes:["ck-fake-link-selection","ck-fake-link-selection_collapsed"]}})}destroy(){super.destroy(),this.formView&&this.formView.destroy(),this.actionsView&&this.actionsView.destroy()}_createViews(){this.actionsView=this._createActionsView(),this.formView=this._createFormView(),this._enableUserBalloonInteractions()}_createActionsView(){const e=this.editor,t=new Ie(e.locale),i=e.commands.get("link"),n=e.commands.get("unlink");return t.bind("href").to(i,"value"),t.editButtonView.bind("isEnabled").to(i),t.unlinkButtonView.bind("isEnabled").to(n),this.listenTo(t,"edit",(()=>{this._addFormView()})),this.listenTo(t,"unlink",(()=>{e.execute("unlink"),this._hideUI()})),t.keystrokes.set("Esc",((e,t)=>{this._hideUI(),t()})),t.keystrokes.set(G,((e,t)=>{this._addFormView(),t()})),t}_createFormView(){const e=this.editor,t=e.commands.get("link"),i=e.config.get("link.defaultProtocol"),n=new((0,ge.CssTransitionDisablerMixin)(ye))(e.locale,t);return n.urlInputView.fieldView.bind("value").to(t,"value"),n.urlInputView.bind("isEnabled").to(t,"isEnabled"),n.saveButtonView.bind("isEnabled").to(t),this.listenTo(n,"submit",(()=>{const{value:t}=n.urlInputView.fieldView.element,o=te(t,i);e.execute("link",o,n.getDecoratorSwitchesState()),this._closeFormView()})),this.listenTo(n,"cancel",(()=>{this._closeFormView()})),n.keystrokes.set("Esc",((e,t)=>{this._closeFormView(),t()})),n}_createToolbarLinkButton(){const e=this.editor,t=e.commands.get("link"),i=e.t;e.ui.componentFactory.add("link",(e=>{const n=new ge.ButtonView(e);return n.isEnabled=!0,n.label=i("Link"),n.icon=Ve,n.keystroke=G,n.tooltip=!0,n.isToggleable=!0,n.bind("isEnabled").to(t,"isEnabled"),n.bind("isOn").to(t,"value",(e=>!!e)),this.listenTo(n,"execute",(()=>this._showUI(!0))),n}))}_enableBalloonActivators(){const e=this.editor,t=e.editing.view.document;this.listenTo(t,"click",(()=>{this._getSelectedLinkElement()&&this._showUI()})),e.keystrokes.set(G,((t,i)=>{i(),e.commands.get("link").isEnabled&&this._showUI(!0)}))}_enableUserBalloonInteractions(){this.editor.keystrokes.set("Tab",((e,t)=>{this._areActionsVisible&&!this.actionsView.focusTracker.isFocused&&(this.actionsView.focus(),t())}),{priority:"high"}),this.editor.keystrokes.set("Esc",((e,t)=>{this._isUIVisible&&(this._hideUI(),t())})),(0,ge.clickOutsideHandler)({emitter:this.formView,activator:()=>this._isUIInPanel,contextElements:()=>[this._balloon.view.element],callback:()=>this._hideUI()})}_addActionsView(){this.actionsView||this._createViews(),this._areActionsInPanel||this._balloon.add({view:this.actionsView,position:this._getBalloonPositionData()})}_addFormView(){if(this.formView||this._createViews(),this._isFormInPanel)return;const e=this.editor.commands.get("link");this.formView.disableCssTransitions(),this._balloon.add({view:this.formView,position:this._getBalloonPositionData()}),this._balloon.visibleView===this.formView&&this.formView.urlInputView.fieldView.select(),this.formView.enableCssTransitions(),this.formView.urlInputView.fieldView.element.value=e.value||""}_closeFormView(){const e=this.editor.commands.get("link");e.restoreManualDecoratorStates(),void 0!==e.value?this._removeFormView():this._hideUI()}_removeFormView(){this._isFormInPanel&&(this.formView.saveButtonView.focus(),this._balloon.remove(this.formView),this.editor.editing.view.focus(),this._hideFakeVisualSelection())}_showUI(e=!1){this.formView||this._createViews(),this._getSelectedLinkElement()?(this._areActionsVisible?this._addFormView():this._addActionsView(),e&&this._balloon.showStack("main")):(this._showFakeVisualSelection(),this._addActionsView(),e&&this._balloon.showStack("main"),this._addFormView()),this._startUpdatingUI()}_hideUI(){if(!this._isUIInPanel)return;const e=this.editor;this.stopListening(e.ui,"update"),this.stopListening(this._balloon,"change:visibleView"),e.editing.view.focus(),this._removeFormView(),this._balloon.remove(this.actionsView),this._hideFakeVisualSelection()}_startUpdatingUI(){const e=this.editor,t=e.editing.view.document;let i=this._getSelectedLinkElement(),n=s();const o=()=>{const e=this._getSelectedLinkElement(),t=s();i&&!e||!i&&t!==n?this._hideUI():this._isUIVisible&&this._balloon.updatePosition(this._getBalloonPositionData()),i=e,n=t};function s(){return t.selection.focus.getAncestors().reverse().find((e=>e.is("element")))}this.listenTo(e.ui,"update",o),this.listenTo(this._balloon,"change:visibleView",o)}get _isFormInPanel(){return!!this.formView&&this._balloon.hasView(this.formView)}get _areActionsInPanel(){return!!this.actionsView&&this._balloon.hasView(this.actionsView)}get _areActionsVisible(){return!!this.actionsView&&this._balloon.visibleView===this.actionsView}get _isUIInPanel(){return this._isFormInPanel||this._areActionsInPanel}get _isUIVisible(){const e=this._balloon.visibleView;return!!this.formView&&e==this.formView||this._areActionsVisible}_getBalloonPositionData(){const e=this.editor.editing.view,t=this.editor.model,i=e.document;let n;if(t.markers.has(Se)){const t=Array.from(this.editor.editing.mapper.markerNameToElements(Se)),i=e.createRange(e.createPositionBefore(t[0]),e.createPositionAfter(t[t.length-1]));n=e.domConverter.viewRangeToDom(i)}else n=()=>{const t=this._getSelectedLinkElement();return t?e.domConverter.mapViewToDom(t):e.domConverter.viewRangeToDom(i.selection.getFirstRange())};return{target:n}}_getSelectedLinkElement(){const e=this.editor.editing.view,t=e.document.selection,i=t.getSelectedElement();if(t.isCollapsed||i&&(0,be.isWidget)(i))return Te(t.getFirstPosition());{const i=t.getFirstRange().getTrimmed(),n=Te(i.start),o=Te(i.end);return n&&n==o&&e.createRangeIn(n).getTrimmed().isEqual(i)?n:null}}_showFakeVisualSelection(){const e=this.editor.model;e.change((t=>{const i=e.document.selection.getFirstRange();if(e.markers.has(Se))t.updateMarker(Se,{range:i});else if(i.start.isAtEnd){const n=i.start.getLastMatchingPosition((({item:t})=>!e.schema.isContent(t)),{boundaries:i});t.addMarker(Se,{usingOperation:!1,affectsData:!1,range:t.createRange(n,i.end)})}else t.addMarker(Se,{usingOperation:!1,affectsData:!1,range:i})}))}_hideFakeVisualSelection(){const e=this.editor.model;e.markers.has(Se)&&e.change((e=>{e.removeMarker(Se)}))}}function Te(e){return e.getAncestors().find((e=>{return(t=e).is("attributeElement")&&!!t.getCustomProperty("link");var t}))||null}const Le=new RegExp("(^|\\s)(((?:(?:(?:https?|ftp):)?\\/\\/)(?:\\S+(?::\\S*)?@)?(?:(?:[1-9]\\d?|1\\d\\d|2[01]\\d|22[0-3])(?:\\.(?:1?\\d{1,2}|2[0-4]\\d|25[0-5])){2}(?:\\.(?:[1-9]\\d?|1\\d\\d|2[0-4]\\d|25[0-4]))|(((?!www\\.)|(www\\.))(?![-_])(?:[-_a-z0-9\\u00a1-\\uffff]{1,63}\\.)+(?:[a-z\\u00a1-\\uffff]{2,63})))(?::\\d{2,5})?(?:[/?#]\\S*)?)|((www.|(\\S+@))((?![-_])(?:[-_a-z0-9\\u00a1-\\uffff]{1,63}\\.))+(?:[a-z\\u00a1-\\uffff]{2,63})))$","i");class Ce extends e.Plugin{static get requires(){return[t.Delete,fe]}static get pluginName(){return"AutoLink"}init(){const e=this.editor.model.document.selection;e.on("change:range",(()=>{this.isEnabled=!e.anchor.parent.is("element","codeBlock")})),this._enableTypingHandling()}afterInit(){this._enableEnterHandling(),this._enableShiftEnterHandling(),this._enablePasteLinking()}_expandLinkRange(e,i){return i.textNode&&i.textNode.hasAttribute("linkHref")?(0,t.findAttributeRange)(i,"linkHref",i.textNode.getAttribute("linkHref"),e):null}_selectEntireLinks(e,t){const i=this.editor.model,n=i.document.selection,o=n.getFirstPosition(),s=n.getLastPosition();let r=t.getJoined(this._expandLinkRange(i,o)||t);r&&(r=r.getJoined(this._expandLinkRange(i,s)||t)),r&&(r.start.isBefore(o)||r.end.isAfter(s))&&e.setSelection(r)}_enablePasteLinking(){const e=this.editor,t=e.model,i=t.document.selection,n=e.plugins.get("ClipboardPipeline"),o=e.commands.get("link");n.on("inputTransformation",((e,n)=>{if(!this.isEnabled||!o.isEnabled||i.isCollapsed)return;if(i.rangeCount>1)return;const s=i.getFirstRange(),r=n.dataTransfer.getData("text/plain");if(!r)return;const a=r.match(Le);a&&a[2]===r&&(t.change((e=>{this._selectEntireLinks(e,s),o.execute(r)})),e.stop())}),{priority:"high"})}_enableTypingHandling(){const e=this.editor,i=new t.TextWatcher(e.model,(e=>{if(!function(e){return e.length>4&&" "===e[e.length-1]&&" "!==e[e.length-2]}(e))return;const t=De(e.substr(0,e.length-1));return t?{url:t}:void 0}));i.on("matched:data",((t,i)=>{const{batch:n,range:o,url:s}=i;if(!n.isTyping)return;const r=o.end.getShiftedBy(-1),a=r.getShiftedBy(-s.length),c=e.model.createRange(a,r);this._applyAutoLink(s,c)})),i.bind("isEnabled").to(this)}_enableEnterHandling(){const e=this.editor,t=e.model,i=e.commands.get("enter");i&&i.on("execute",(()=>{const e=t.document.selection.getFirstPosition();if(!e.parent.previousSibling)return;const i=t.createRangeIn(e.parent.previousSibling);this._checkAndApplyAutoLinkOnRange(i)}))}_enableShiftEnterHandling(){const e=this.editor,t=e.model,i=e.commands.get("shiftEnter");i&&i.on("execute",(()=>{const e=t.document.selection.getFirstPosition(),i=t.createRange(t.createPositionAt(e.parent,0),e.getShiftedBy(-1));this._checkAndApplyAutoLinkOnRange(i)}))}_checkAndApplyAutoLinkOnRange(e){const i=this.editor.model,{text:n,range:o}=(0,t.getLastTextLine)(e,i),s=De(n);if(s){const e=i.createRange(o.end.getShiftedBy(-s.length),o.end);this._applyAutoLink(s,e)}}_applyAutoLink(e,t){const i=this.editor.model,n=te(e,this.editor.config.get("link.defaultProtocol"));this.isEnabled&&function(e,t){return t.schema.checkAttributeInSelection(t.createSelection(e),"linkHref")}(t,i)&&ie(n)&&!function(e){const t=e.start.nodeAfter;return!!t&&t.hasAttribute("linkHref")}(t)&&this._persistAutoLink(n,t)}_persistAutoLink(e,t){const i=this.editor.model,n=this.editor.plugins.get("Delete");i.enqueueChange((o=>{o.setAttribute("linkHref",e,t),i.enqueueChange((()=>{n.requestUndoOnBackspace()}))}))}}function De(e){const t=Le.exec(e);return t?t[2]:null}class Me extends e.Plugin{static get requires(){return[fe,Ee,Ce]}static get pluginName(){return"Link"}}class je extends e.Plugin{static get requires(){return["ImageEditing","ImageUtils",fe]}static get pluginName(){return"LinkImageEditing"}init(){const e=this.editor,t=e.model.schema;e.plugins.has("ImageBlockEditing")&&t.extend("imageBlock",{allowAttributes:["linkHref"]}),e.conversion.for("upcast").add(function(e){const t=e.plugins.has("ImageInlineEditing"),i=e.plugins.get("ImageUtils");return e=>{e.on("element:a",((e,n,o)=>{const s=n.viewItem,r=i.findViewImgElement(s);if(!r)return;const a=r.findAncestor((e=>i.isBlockImageView(e)));if(t&&!a)return;const c={attributes:["href"]};if(!o.consumable.consume(s,c))return;const l=s.getAttribute("href");if(!l)return;let u=n.modelCursor.parent;if(!u.is("element","imageBlock")){const e=o.convertItem(r,n.modelCursor);n.modelRange=e.modelRange,n.modelCursor=e.modelCursor,u=n.modelCursor.nodeBefore}u&&u.is("element","imageBlock")&&o.writer.setAttribute("linkHref",l,u)}),{priority:"high"})}}(e)),e.conversion.for("downcast").add(function(e){const t=e.plugins.get("ImageUtils");return e=>{e.on("attribute:linkHref:imageBlock",((e,i,n)=>{if(!n.consumable.consume(i.item,e.name))return;const o=n.mapper.toViewElement(i.item),s=n.writer,r=Array.from(o.getChildren()).find((e=>e.is("element","a"))),a=t.findViewImgElement(o),c=a.parent.is("element","picture")?a.parent:a;if(r)i.attributeNewValue?s.setAttribute("href",i.attributeNewValue,r):(s.move(s.createRangeOn(c),s.createPositionAt(o,0)),s.remove(r));else{const e=s.createContainerElement("a",{href:i.attributeNewValue});s.insert(s.createPositionAt(o,0),e),s.move(s.createRangeOn(c),s.createPositionAt(e,0))}}),{priority:"high"})}}(e)),this._enableAutomaticDecorators(),this._enableManualDecorators()}_enableAutomaticDecorators(){const e=this.editor,t=e.commands.get("link").automaticDecorators;t.length&&e.conversion.for("downcast").add(t.getDispatcherForLinkedImage())}_enableManualDecorators(){const e=this.editor,t=e.commands.get("link");for(const i of t.manualDecorators)e.plugins.has("ImageBlockEditing")&&e.model.schema.extend("imageBlock",{allowAttributes:i.id}),e.plugins.has("ImageInlineEditing")&&e.model.schema.extend("imageInline",{allowAttributes:i.id}),e.conversion.for("downcast").add(Be(i)),e.conversion.for("upcast").add(Ne(e,i))}}function Be(e){return t=>{t.on(`attribute:${e.id}:imageBlock`,((t,i,n)=>{const o=n.mapper.toViewElement(i.item),r=Array.from(o.getChildren()).find((e=>e.is("element","a")));if(r){for(const[t,i]of(0,s.toMap)(e.attributes))n.writer.setAttribute(t,i,r);e.classes&&n.writer.addClass(e.classes,r);for(const t in e.styles)n.writer.setStyle(t,e.styles[t],r)}}))}}function Ne(e,t){const i=e.plugins.has("ImageInlineEditing"),n=e.plugins.get("ImageUtils");return e=>{e.on("element:a",((e,o,s)=>{const r=o.viewItem,a=n.findViewImgElement(r);if(!a)return;const c=a.findAncestor((e=>n.isBlockImageView(e)));if(i&&!c)return;const l=new me.Matcher(t._createPattern()).match(r);if(!l)return;if(!s.consumable.consume(r,l.match))return;const u=o.modelCursor.nodeBefore||o.modelCursor.parent;s.writer.setAttribute(t.id,!0,u)}),{priority:"high"})}}class He extends e.Plugin{static get requires(){return[fe,Ee,"ImageBlockEditing"]}static get pluginName(){return"LinkImageUI"}init(){const e=this.editor,t=e.editing.view.document;this.listenTo(t,"click",((t,i)=>{this._isSelectedLinkedImage(e.model.document.selection)&&(i.preventDefault(),t.stop())}),{priority:"high"}),this._createToolbarLinkImageButton()}_createToolbarLinkImageButton(){const e=this.editor,t=e.t;e.ui.componentFactory.add("linkImage",(i=>{const n=new ge.ButtonView(i),o=e.plugins.get("LinkUI"),s=e.commands.get("link");return n.set({isEnabled:!0,label:t("Link image"),icon:Ve,keystroke:G,tooltip:!0,isToggleable:!0}),n.bind("isEnabled").to(s,"isEnabled"),n.bind("isOn").to(s,"value",(e=>!!e)),this.listenTo(n,"execute",(()=>{this._isSelectedLinkedImage(e.model.document.selection)?o._addActionsView():o._showUI(!0)})),n}))}_isSelectedLinkedImage(e){const t=e.getSelectedElement();return this.editor.plugins.get("ImageUtils").isImage(t)&&t.hasAttribute("linkHref")}}var Pe=i(883),Ue={injectType:"singletonStyleTag",attributes:{"data-cke":!0},insert:"head",singleton:!0};le()(Pe.Z,Ue);Pe.Z.locals;class Oe extends e.Plugin{static get requires(){return[je,He]}static get pluginName(){return"LinkImage"}}})(),(window.CKEditor5=window.CKEditor5||{}).link=n})();;
!function(t){const e=t.en=t.en||{};e.dictionary=Object.assign(e.dictionary||{},{"Bulleted List":"Bulleted List","Bulleted list styles toolbar":"Bulleted list styles toolbar",Circle:"Circle",Decimal:"Decimal","Decimal with leading zero":"Decimal with leading zero",Disc:"Disc","List properties":"List properties","Lower-latin":"Lower-latin","Lower–roman":"Lower–roman","Numbered List":"Numbered List","Numbered list styles toolbar":"Numbered list styles toolbar","Reversed order":"Reversed order",Square:"Square","Start at":"Start at","Start index must be greater than 0.":"Start index must be greater than 0.","To-do List":"To-do List","Toggle the circle list style":"Toggle the circle list style","Toggle the decimal list style":"Toggle the decimal list style","Toggle the decimal with leading zero list style":"Toggle the decimal with leading zero list style","Toggle the disc list style":"Toggle the disc list style","Toggle the lower–latin list style":"Toggle the lower–latin list style","Toggle the lower–roman list style":"Toggle the lower–roman list style","Toggle the square list style":"Toggle the square list style","Toggle the upper–latin list style":"Toggle the upper–latin list style","Toggle the upper–roman list style":"Toggle the upper–roman list style","Upper-latin":"Upper-latin","Upper-roman":"Upper-roman"})}(window.CKEDITOR_TRANSLATIONS||(window.CKEDITOR_TRANSLATIONS={})),
/*!
 * @license Copyright (c) 2003-2023, CKSource Holding sp. z o.o. All rights reserved.
 * For licensing, see LICENSE.md.
 */(()=>{var t={118:(t,e,i)=>{"use strict";i.d(e,{Z:()=>r});var n=i(609),s=i.n(n)()((function(t){return t[1]}));s.push([t.id,".ck-editor__editable .ck-list-bogus-paragraph{display:block}",""]);const r=s},479:(t,e,i)=>{"use strict";i.d(e,{Z:()=>r});var n=i(609),s=i.n(n)()((function(t){return t[1]}));s.push([t.id,".ck-content ol{list-style-type:decimal}.ck-content ol ol{list-style-type:lower-latin}.ck-content ol ol ol{list-style-type:lower-roman}.ck-content ol ol ol ol{list-style-type:upper-latin}.ck-content ol ol ol ol ol{list-style-type:upper-roman}.ck-content ul{list-style-type:disc}.ck-content ul ul{list-style-type:circle}.ck-content ul ul ul,.ck-content ul ul ul ul{list-style-type:square}",""]);const r=s},463:(t,e,i)=>{"use strict";i.d(e,{Z:()=>r});var n=i(609),s=i.n(n)()((function(t){return t[1]}));s.push([t.id,".ck.ck-list-properties.ck-list-properties_without-styles{padding:var(--ck-spacing-large)}.ck.ck-list-properties.ck-list-properties_without-styles>*{min-width:14em}.ck.ck-list-properties.ck-list-properties_without-styles>*+*{margin-top:var(--ck-spacing-standard)}.ck.ck-list-properties.ck-list-properties_with-numbered-properties>.ck-list-styles-list{grid-template-columns:repeat(4,auto)}.ck.ck-list-properties.ck-list-properties_with-numbered-properties>.ck-collapsible{border-top:1px solid var(--ck-color-base-border)}.ck.ck-list-properties.ck-list-properties_with-numbered-properties>.ck-collapsible>.ck-collapsible__children>*{width:100%}.ck.ck-list-properties.ck-list-properties_with-numbered-properties>.ck-collapsible>.ck-collapsible__children>*+*{margin-top:var(--ck-spacing-standard)}.ck.ck-list-properties .ck.ck-numbered-list-properties__start-index .ck-input{min-width:auto;width:100%}.ck.ck-list-properties .ck.ck-numbered-list-properties__reversed-order{background:transparent;margin-bottom:calc(var(--ck-spacing-tiny)*-1);padding-left:0;padding-right:0}.ck.ck-list-properties .ck.ck-numbered-list-properties__reversed-order:active,.ck.ck-list-properties .ck.ck-numbered-list-properties__reversed-order:hover{background:none;border-color:transparent;box-shadow:none}",""]);const r=s},989:(t,e,i)=>{"use strict";i.d(e,{Z:()=>r});var n=i(609),s=i.n(n)()((function(t){return t[1]}));s.push([t.id,".ck.ck-list-styles-list{display:grid}:root{--ck-list-style-button-size:44px}.ck.ck-list-styles-list{column-gap:var(--ck-spacing-medium);grid-template-columns:repeat(3,auto);padding:var(--ck-spacing-large);row-gap:var(--ck-spacing-medium)}.ck.ck-list-styles-list .ck-button{box-sizing:content-box;margin:0;padding:0}.ck.ck-list-styles-list .ck-button,.ck.ck-list-styles-list .ck-button .ck-icon{height:var(--ck-list-style-button-size);width:var(--ck-list-style-button-size)}",""]);const r=s},528:(t,e,i)=>{"use strict";i.d(e,{Z:()=>r});var n=i(609),s=i.n(n)()((function(t){return t[1]}));s.push([t.id,':root{--ck-todo-list-checkmark-size:16px}.ck-content .todo-list{list-style:none}.ck-content .todo-list li{margin-bottom:5px;position:relative}.ck-content .todo-list li .todo-list{margin-top:5px}.ck-content .todo-list .todo-list__label>input{-webkit-appearance:none;border:0;display:inline-block;height:var(--ck-todo-list-checkmark-size);left:-25px;margin-left:0;margin-right:-15px;position:relative;right:0;vertical-align:middle;width:var(--ck-todo-list-checkmark-size)}.ck-content[dir=rtl] .todo-list .todo-list__label>input{left:0;margin-left:-15px;margin-right:0;right:-25px}.ck-content .todo-list .todo-list__label>input:before{border:1px solid #333;border-radius:2px;box-sizing:border-box;content:"";display:block;height:100%;position:absolute;transition:box-shadow .25s ease-in-out;width:100%}.ck-content .todo-list .todo-list__label>input:after{border-color:transparent;border-style:solid;border-width:0 calc(var(--ck-todo-list-checkmark-size)/8) calc(var(--ck-todo-list-checkmark-size)/8) 0;box-sizing:content-box;content:"";display:block;height:calc(var(--ck-todo-list-checkmark-size)/2.6);left:calc(var(--ck-todo-list-checkmark-size)/3);pointer-events:none;position:absolute;top:calc(var(--ck-todo-list-checkmark-size)/5.3);transform:rotate(45deg);width:calc(var(--ck-todo-list-checkmark-size)/5.3)}.ck-content .todo-list .todo-list__label>input[checked]:before{background:#26ab33;border-color:#26ab33}.ck-content .todo-list .todo-list__label>input[checked]:after{border-color:#fff}.ck-content .todo-list .todo-list__label .todo-list__label__description{vertical-align:middle}.ck-content .todo-list .todo-list__label.todo-list__label_without-description input[type=checkbox]{position:absolute}.ck-editor__editable.ck-content .todo-list .todo-list__label>input,.ck-editor__editable.ck-content .todo-list .todo-list__label>span[contenteditable=false]>input{cursor:pointer}.ck-editor__editable.ck-content .todo-list .todo-list__label>input:hover:before,.ck-editor__editable.ck-content .todo-list .todo-list__label>span[contenteditable=false]>input:hover:before{box-shadow:0 0 0 5px rgba(0,0,0,.1)}.ck-editor__editable.ck-content .todo-list .todo-list__label>span[contenteditable=false]>input{-webkit-appearance:none;border:0;display:inline-block;height:var(--ck-todo-list-checkmark-size);left:-25px;margin-left:0;margin-right:-15px;position:relative;right:0;vertical-align:middle;width:var(--ck-todo-list-checkmark-size)}.ck-editor__editable.ck-content[dir=rtl] .todo-list .todo-list__label>span[contenteditable=false]>input{left:0;margin-left:-15px;margin-right:0;right:-25px}.ck-editor__editable.ck-content .todo-list .todo-list__label>span[contenteditable=false]>input:before{border:1px solid #333;border-radius:2px;box-sizing:border-box;content:"";display:block;height:100%;position:absolute;transition:box-shadow .25s ease-in-out;width:100%}.ck-editor__editable.ck-content .todo-list .todo-list__label>span[contenteditable=false]>input:after{border-color:transparent;border-style:solid;border-width:0 calc(var(--ck-todo-list-checkmark-size)/8) calc(var(--ck-todo-list-checkmark-size)/8) 0;box-sizing:content-box;content:"";display:block;height:calc(var(--ck-todo-list-checkmark-size)/2.6);left:calc(var(--ck-todo-list-checkmark-size)/3);pointer-events:none;position:absolute;top:calc(var(--ck-todo-list-checkmark-size)/5.3);transform:rotate(45deg);width:calc(var(--ck-todo-list-checkmark-size)/5.3)}.ck-editor__editable.ck-content .todo-list .todo-list__label>span[contenteditable=false]>input[checked]:before{background:#26ab33;border-color:#26ab33}.ck-editor__editable.ck-content .todo-list .todo-list__label>span[contenteditable=false]>input[checked]:after{border-color:#fff}.ck-editor__editable.ck-content .todo-list .todo-list__label.todo-list__label_without-description input[type=checkbox]{position:absolute}',""]);const r=s},609:t=>{"use strict";t.exports=function(t){var e=[];return e.toString=function(){return this.map((function(e){var i=t(e);return e[2]?"@media ".concat(e[2]," {").concat(i,"}"):i})).join("")},e.i=function(t,i,n){"string"==typeof t&&(t=[[null,t,""]]);var s={};if(n)for(var r=0;r<this.length;r++){var o=this[r][0];null!=o&&(s[o]=!0)}for(var l=0;l<t.length;l++){var a=[].concat(t[l]);n&&s[a[0]]||(i&&(a[2]?a[2]="".concat(i," and ").concat(a[2]):a[2]=i),e.push(a))}},e}},62:(t,e,i)=>{"use strict";var n,s=function(){return void 0===n&&(n=Boolean(window&&document&&document.all&&!window.atob)),n},r=function(){var t={};return function(e){if(void 0===t[e]){var i=document.querySelector(e);if(window.HTMLIFrameElement&&i instanceof window.HTMLIFrameElement)try{i=i.contentDocument.head}catch(t){i=null}t[e]=i}return t[e]}}(),o=[];function l(t){for(var e=-1,i=0;i<o.length;i++)if(o[i].identifier===t){e=i;break}return e}function a(t,e){for(var i={},n=[],s=0;s<t.length;s++){var r=t[s],a=e.base?r[0]+e.base:r[0],c=i[a]||0,d="".concat(a," ").concat(c);i[a]=c+1;var u=l(d),m={css:r[1],media:r[2],sourceMap:r[3]};-1!==u?(o[u].references++,o[u].updater(m)):o.push({identifier:d,updater:g(m,e),references:1}),n.push(d)}return n}function c(t){var e=document.createElement("style"),n=t.attributes||{};if(void 0===n.nonce){var s=i.nc;s&&(n.nonce=s)}if(Object.keys(n).forEach((function(t){e.setAttribute(t,n[t])})),"function"==typeof t.insert)t.insert(e);else{var o=r(t.insert||"head");if(!o)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");o.appendChild(e)}return e}var d,u=(d=[],function(t,e){return d[t]=e,d.filter(Boolean).join("\n")});function m(t,e,i,n){var s=i?"":n.media?"@media ".concat(n.media," {").concat(n.css,"}"):n.css;if(t.styleSheet)t.styleSheet.cssText=u(e,s);else{var r=document.createTextNode(s),o=t.childNodes;o[e]&&t.removeChild(o[e]),o.length?t.insertBefore(r,o[e]):t.appendChild(r)}}function p(t,e,i){var n=i.css,s=i.media,r=i.sourceMap;if(s?t.setAttribute("media",s):t.removeAttribute("media"),r&&"undefined"!=typeof btoa&&(n+="\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(encodeURIComponent(JSON.stringify(r))))," */")),t.styleSheet)t.styleSheet.cssText=n;else{for(;t.firstChild;)t.removeChild(t.firstChild);t.appendChild(document.createTextNode(n))}}var h=null,f=0;function g(t,e){var i,n,s;if(e.singleton){var r=f++;i=h||(h=c(e)),n=m.bind(null,i,r,!1),s=m.bind(null,i,r,!0)}else i=c(e),n=p.bind(null,i,e),s=function(){!function(t){if(null===t.parentNode)return!1;t.parentNode.removeChild(t)}(i)};return n(t),function(e){if(e){if(e.css===t.css&&e.media===t.media&&e.sourceMap===t.sourceMap)return;n(t=e)}else s()}}t.exports=function(t,e){(e=e||{}).singleton||"boolean"==typeof e.singleton||(e.singleton=s());var i=a(t=t||[],e);return function(t){if(t=t||[],"[object Array]"===Object.prototype.toString.call(t)){for(var n=0;n<i.length;n++){var s=l(i[n]);o[s].references--}for(var r=a(t,e),c=0;c<i.length;c++){var d=l(i[c]);0===o[d].references&&(o[d].updater(),o.splice(d,1))}i=r}}}},945:(t,e,i)=>{t.exports=i(79)("./src/clipboard.js")},704:(t,e,i)=>{t.exports=i(79)("./src/core.js")},492:(t,e,i)=>{t.exports=i(79)("./src/engine.js")},331:(t,e,i)=>{t.exports=i(79)("./src/enter.js")},181:(t,e,i)=>{t.exports=i(79)("./src/typing.js")},273:(t,e,i)=>{t.exports=i(79)("./src/ui.js")},209:(t,e,i)=>{t.exports=i(79)("./src/utils.js")},79:t=>{"use strict";t.exports=CKEditor5.dll}},e={};function i(n){var s=e[n];if(void 0!==s)return s.exports;var r=e[n]={id:n,exports:{}};return t[n](r,r.exports,i),r.exports}i.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return i.d(e,{a:e}),e},i.d=(t,e)=>{for(var n in e)i.o(e,n)&&!i.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:e[n]})},i.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),i.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},i.nc=void 0;var n={};(()=>{"use strict";i.r(n),i.d(n,{AdjacentListsSupport:()=>wt,DocumentList:()=>vt,DocumentListEditing:()=>tt,DocumentListIndentCommand:()=>S,DocumentListProperties:()=>Kt,DocumentListPropertiesEditing:()=>Mt,DocumentListPropertiesUtils:()=>Bt,DocumentListUtils:()=>V,IndentCommand:()=>$t,List:()=>ue,ListEditing:()=>ce,ListProperties:()=>Ie,ListPropertiesEditing:()=>ge,ListPropertiesUI:()=>jt,ListUI:()=>yt,ListUtils:()=>Gt,TodoDocumentList:()=>je,TodoDocumentListEditing:()=>Re,TodoList:()=>Be,TodoListEditing:()=>Ee,TodoListUI:()=>Pe});var t=i(704),e=i(181),s=i(331),r=i(209);class o{constructor(t,e){this._startElement=t,this._referenceIndent=t.getAttribute("listIndent"),this._isForward="forward"==e.direction,this._includeSelf=!!e.includeSelf,this._sameAttributes=(0,r.toArray)(e.sameAttributes||[]),this._sameIndent=!!e.sameIndent,this._lowerIndent=!!e.lowerIndent,this._higherIndent=!!e.higherIndent}static first(t,e){const i=new this(t,e)[Symbol.iterator]();return(0,r.first)(i)}*[Symbol.iterator](){const t=[];for(const{node:e}of l(this._getStartNode(),this._isForward?"forward":"backward")){const i=e.getAttribute("listIndent");if(i<this._referenceIndent){if(!this._lowerIndent)break;this._referenceIndent=i}else if(i>this._referenceIndent){if(!this._higherIndent)continue;if(!this._isForward){t.push(e);continue}}else{if(!this._sameIndent){if(this._higherIndent){t.length&&(yield*t,t.length=0);break}continue}if(this._sameAttributes.some((t=>e.getAttribute(t)!==this._startElement.getAttribute(t))))break}t.length&&(yield*t,t.length=0),yield e}}_getStartNode(){return this._includeSelf?this._startElement:this._isForward?this._startElement.nextSibling:this._startElement.previousSibling}}function*l(t,e="forward"){const i="forward"==e,n=[];let s=null;for(;d(t);){let e=null;if(s){const i=t.getAttribute("listIndent"),r=s.getAttribute("listIndent");i>r?n[r]=s:i<r?(e=n[i],n.length=i):e=s}yield{node:t,previous:s,previousNodeInList:e},s=t,t=i?t.nextSibling:t.previousSibling}}class a{constructor(t){this._listHead=t}[Symbol.iterator](){return l(this._listHead,"forward")}}class c{static next(){return(0,r.uid)()}}function d(t){return!!t&&t.is("element")&&t.hasAttribute("listItemId")}function u(t,e={}){return[...m(t,{...e,direction:"backward"}),...m(t,{...e,direction:"forward"})]}function m(t,e={}){const i="forward"==e.direction,n=Array.from(new o(t,{...e,includeSelf:i,sameIndent:!0,sameAttributes:"listItemId"}));return i?n:n.reverse()}function p(t){const e=new o(t,{sameIndent:!0,sameAttributes:"listType"}),i=new o(t,{sameIndent:!0,sameAttributes:"listType",includeSelf:!0,direction:"forward"});return[...Array.from(e).reverse(),...i]}function h(t){return!o.first(t,{sameIndent:!0,sameAttributes:"listItemId"})}function f(t){return!o.first(t,{direction:"forward",sameIndent:!0,sameAttributes:"listItemId"})}function g(t,e={}){t=(0,r.toArray)(t);const i=!1!==e.withNested,n=new Set;for(const e of t)for(const t of u(e,{higherIndent:i}))n.add(t);return k(n)}function b(t){t=(0,r.toArray)(t);const e=new Set;for(const i of t)for(const t of p(i))e.add(t);return k(e)}function y(t,e){const i=m(t,{direction:"forward"}),n=c.next();for(const t of i)e.setAttribute("listItemId",n,t);return i}function v(t,e,i){const n={};for(const[t,i]of e.getAttributes())t.startsWith("list")&&(n[t]=i);const s=m(t,{direction:"forward"});for(const t of s)i.setAttributes(n,t);return s}function w(t,e,{expand:i,indentBy:n=1}={}){t=(0,r.toArray)(t);const s=i?g(t):t;for(const t of s){const i=t.getAttribute("listIndent")+n;i<0?A(t,e):e.setAttribute("listIndent",i,t)}return s}function A(t,e){t=(0,r.toArray)(t);for(const i of t)i.is("element","listItem")&&e.rename(i,"paragraph");for(const i of t)for(const t of i.getAttributeKeys())t.startsWith("list")&&e.removeAttribute(t,i);return t}function I(t){if(!t.length)return!1;const e=t[0].getAttribute("listItemId");return!!e&&!t.some((t=>t.getAttribute("listItemId")!=e))}function k(t){return Array.from(t).filter((t=>"$graveyard"!==t.root.rootName)).sort(((t,e)=>t.index-e.index))}function T(t){const e=t.document.selection.getSelectedElement();return e&&t.schema.isObject(e)&&t.schema.isBlock(e)?e:null}function x(t,e){return e.checkChild(t.parent,"listItem")&&e.checkChild(t,"$text")&&!e.isObject(t)}function _(t,e,i){return m(e,{direction:"forward"}).pop().index>t.index?v(t,e,i):[]}class S extends t.Command{constructor(t,e){super(t),this._direction=e}refresh(){this.isEnabled=this._checkEnabled()}execute(){const t=this.editor.model,e=C(t.document.selection);t.change((t=>{const i=[];I(e)&&!h(e[0])?("forward"==this._direction&&i.push(...w(e,t)),i.push(...y(e[0],t))):"forward"==this._direction?i.push(...w(e,t,{expand:!0})):i.push(...function(t,e){const i=g(t=(0,r.toArray)(t)),n=new Set,s=Math.min(...i.map((t=>t.getAttribute("listIndent")))),l=new Map;for(const t of i)l.set(t,o.first(t,{lowerIndent:!0}));for(const t of i){if(n.has(t))continue;n.add(t);const i=t.getAttribute("listIndent")-1;if(i<0)A(t,e);else{if(t.getAttribute("listIndent")==s){const i=_(t,l.get(t),e);for(const t of i)n.add(t);if(i.length)continue}e.setAttribute("listIndent",i,t)}}return k(n)}(e,t));for(const e of i){if(!e.hasAttribute("listType"))continue;const i=o.first(e,{sameIndent:!0});i&&t.setAttribute("listType",i.getAttribute("listType"),e)}this._fireAfterExecute(i)}))}_fireAfterExecute(t){this.fire("afterExecute",k(new Set(t)))}_checkEnabled(){let t=C(this.editor.model.document.selection),e=t[0];if(!e)return!1;if("backward"==this._direction)return!0;if(I(t)&&!h(t[0]))return!0;t=g(t),e=t[0];const i=o.first(e,{sameIndent:!0});return!!i&&i.getAttribute("listType")==e.getAttribute("listType")}}function C(t){const e=Array.from(t.getSelectedBlocks()),i=e.findIndex((t=>!d(t)));return-1!=i&&(e.length=i),e}class L extends t.Command{constructor(t,e){super(t),this.type=e}refresh(){this.value=this._getValue(),this.isEnabled=this._checkEnabled()}execute(t={}){const e=this.editor.model,i=e.document,n=T(e),s=Array.from(i.selection.getSelectedBlocks()).filter((t=>e.schema.checkAttribute(t,"listType")||x(t,e.schema))),r=void 0!==t.forceValue?!t.forceValue:this.value;e.change((t=>{if(r){const e=s[s.length-1],i=m(e,{direction:"forward"}),n=[];i.length>1&&n.push(...y(i[1],t)),n.push(...A(s,t)),n.push(...function(t,e){const i=[];let n=Number.POSITIVE_INFINITY;for(const{node:s}of l(t.nextSibling,"forward")){const t=s.getAttribute("listIndent");if(0==t)break;t<n&&(n=t);const r=t-n;e.setAttribute("listIndent",r,s),i.push(s)}return i}(e,t)),this._fireAfterExecute(n)}else if((n||i.selection.isCollapsed)&&d(s[0])){const e=p(n||s[0]);for(const i of e)t.setAttribute("listType",this.type,i);this._fireAfterExecute(e)}else{const i=[];for(const n of s)if(n.hasAttribute("listType"))for(const e of g(n,{withNested:!1}))e.getAttribute("listType")!=this.type&&(t.setAttribute("listType",this.type,e),i.push(e));else!n.is("element","listItem")&&x(n,e.schema)&&t.rename(n,"listItem"),t.setAttributes({listIndent:0,listItemId:c.next(),listType:this.type},n),i.push(n);this._fireAfterExecute(i)}}))}_fireAfterExecute(t){this.fire("afterExecute",k(new Set(t)))}_getValue(){const t=this.editor.model.document.selection,e=Array.from(t.getSelectedBlocks());if(!e.length)return!1;for(const t of e)if(t.getAttribute("listType")!=this.type)return!1;return!0}_checkEnabled(){const t=this.editor.model,e=t.schema,i=t.document.selection,n=Array.from(i.getSelectedBlocks());if(!n.length)return!1;if(this.value)return!0;for(const t of n)if(e.checkAttribute(t,"listType")||x(t,e))return!0;return!1}}class E extends t.Command{constructor(t,e){super(t),this._direction=e}refresh(){this.isEnabled=this._checkEnabled()}execute({shouldMergeOnBlocksContentLevel:t=!1}={}){const e=this.editor.model,i=e.document.selection,n=[];e.change((s=>{const{firstElement:r,lastElement:l}=this._getMergeSubjectElements(i,t),a=r.getAttribute("listIndent")||0,c=l.getAttribute("listIndent"),d=l.getAttribute("listItemId");if(a!=c){const t=(u=l,Array.from(new o(u,{direction:"forward",higherIndent:!0})));n.push(...w([l,...t],s,{indentBy:a-c,expand:a<c}))}var u;if(t){let t=i;i.isCollapsed&&(t=s.createSelection(s.createRange(s.createPositionAt(r,"end"),s.createPositionAt(l,0)))),e.deleteContent(t,{doNotResetEntireContent:i.isCollapsed});const o=t.getLastPosition().parent,a=o.nextSibling;n.push(o),a&&a!==l&&a.getAttribute("listItemId")==d&&n.push(...v(a,o,s))}else n.push(...v(l,r,s));this._fireAfterExecute(n)}))}_fireAfterExecute(t){this.fire("afterExecute",k(new Set(t)))}_checkEnabled(){const t=this.editor.model,e=t.document.selection,i=T(t);if(e.isCollapsed||i){const t=i||e.getFirstPosition().parent;if(!d(t))return!1;const n="backward"==this._direction?t.previousSibling:t.nextSibling;if(!n)return!1;if(I([t,n]))return!1}else{const t=e.getLastPosition(),i=e.getFirstPosition();if(t.parent===i.parent)return!1;if(!d(t.parent))return!1}return!0}_getMergeSubjectElements(t,e){const i=T(this.editor.model);let n,s;if(t.isCollapsed||i){const r=i||t.getFirstPosition().parent,l=h(r);"backward"==this._direction?(s=r,n=l&&!e?o.first(r,{sameIndent:!0,lowerIndent:!0}):r.previousSibling):(n=r,s=r.nextSibling)}else n=t.getFirstPosition().parent,s=t.getLastPosition().parent;return{firstElement:n,lastElement:s}}}class P extends t.Command{constructor(t,e){super(t),this._direction=e}refresh(){this.isEnabled=this._checkEnabled()}execute(){this.editor.model.change((t=>{const e=y(this._getStartBlock(),t);this._fireAfterExecute(e)}))}_fireAfterExecute(t){this.fire("afterExecute",k(new Set(t)))}_checkEnabled(){const t=this.editor.model.document.selection,e=this._getStartBlock();return t.isCollapsed&&d(e)&&!h(e)}_getStartBlock(){const t=this.editor.model.document.selection.getFirstPosition().parent;return"before"==this._direction?t:t.nextSibling}}class V extends t.Plugin{static get pluginName(){return"DocumentListUtils"}expandListBlocksToCompleteList(t){return b(t)}isFirstBlockOfListItem(t){return h(t)}isListItemBlock(t){return d(t)}expandListBlocksToCompleteItems(t,e={}){return g(t,e)}}var z=i(492);function B(t){return t.is("element","ol")||t.is("element","ul")}function N(t){return t.is("element","li")}function M(t,e,i,n=O(i,e)){return t.createAttributeElement(R(i),null,{priority:2*e/100-100,id:n})}function D(t,e,i){return t.createAttributeElement("li",null,{priority:(2*e+1)/100-100,id:i})}function R(t){return"numbered"==t?"ol":"ul"}function O(t,e){return`list-${t}-${e}`}function F(t,e){const i=t.nodeBefore;if(d(i)){let t=i;for(const{node:i}of l(t,"backward"))if(t=i,e.has(t))return;e.set(i,t)}else{const i=t.nodeAfter;d(i)&&e.set(i,i)}}function H(){return(t,e,i)=>{const{writer:n,schema:s}=i;if(!e.modelRange)return;const r=Array.from(e.modelRange.getItems({shallow:!0})).filter((t=>s.checkAttribute(t,"listItemId")));if(!r.length)return;const o=c.next(),l=function(t){let e=0,i=t.parent;for(;i;){if(N(i))e++;else{const t=i.previousSibling;t&&N(t)&&e++}i=i.parent}return e}(e.viewItem);let a=e.viewItem.parent&&e.viewItem.parent.is("element","ol")?"numbered":"bulleted";const d=r[0].getAttribute("listType");d&&(a=d);const u={listItemId:o,listIndent:l,listType:a};for(const t of r)t.hasAttribute("listItemId")||n.setAttributes(u,t);r.length>1&&r[1].getAttribute("listItemId")!=u.listItemId&&i.keepEmptyElement(r[0])}}function j(){return(t,e,i)=>{if(!i.consumable.test(e.viewItem,{name:!0}))return;const n=new z.UpcastWriter(e.viewItem.document);for(const t of Array.from(e.viewItem.getChildren()))N(t)||B(t)||n.remove(t)}}function U(t,e,i,{dataPipeline:n}={}){const s=function(t){return(e,i)=>{const n=[];for(const i of t)e.hasAttribute(i)&&n.push(`attribute:${i}`);return!!n.every((t=>!1!==i.test(e,t)))&&(n.forEach((t=>i.consume(e,t))),!0)}}(t);return(r,l,a)=>{const{writer:c,mapper:d,consumable:u}=a,m=l.item;if(!t.includes(l.attributeKey))return;if(!s(m,u))return;const p=function(t,e,i){const n=i.createRangeOn(t),s=e.toViewRange(n).getTrimmed();return s.end.nodeBefore}(m,d,i);!function(t,e,i){for(;t.parent.is("attributeElement")&&t.parent.getCustomProperty("listItemWrapper");)e.unwrap(e.createRangeIn(t.parent),t.parent);const n=e.createPositionBefore(t).getWalker({direction:"backward"}),s=[];for(const{item:t}of n){if(t.is("element")&&i.toModelElement(t))break;t.is("element")&&t.getCustomProperty("listItemMarker")&&s.push(t)}for(const t of s)e.remove(t)}(p,c,d),function(t,e){let i=t.parent;for(;i.is("attributeElement")&&["ul","ol","li"].includes(i.name);){const n=i.parent;e.unwrap(e.createRangeOn(t),i),i=n}}(p,c);const f=function(t,e,i,n,{dataPipeline:s}){let r=n.createRangeOn(e);if(!h(t))return r;for(const o of i){if("itemMarker"!=o.scope)continue;const i=o.createElement(n,t,{dataPipeline:s});if(!i)continue;if(n.setCustomProperty("listItemMarker",!0,i),n.insert(r.start,i),r=n.createRange(n.createPositionBefore(i),n.createPositionAfter(e)),!o.createWrapperElement||!o.canWrapElement)continue;const l=o.createWrapperElement(n,t,{dataPipeline:s});n.setCustomProperty("listItemWrapper",!0,l),o.canWrapElement(t)?r=n.wrap(r,l):(r=n.wrap(n.createRangeOn(i),l),r=n.createRange(r.start,n.createPositionAfter(e)))}return r}(m,p,e,c,{dataPipeline:n});!function(t,e,i,n){if(!t.hasAttribute("listIndent"))return;const s=t.getAttribute("listIndent");let r=t;for(let t=s;t>=0;t--){const s=D(n,t,r.getAttribute("listItemId")),l=M(n,t,r.getAttribute("listType"));for(const t of i)"list"!=t.scope&&"item"!=t.scope||!r.hasAttribute(t.attributeName)||t.setAttributeOnDowncast(n,r.getAttribute(t.attributeName),"list"==t.scope?l:s);if(e=n.wrap(e,s),e=n.wrap(e,l),0==t)break;if(r=o.first(r,{lowerIndent:!0}),!r)break}}(m,f,e,c)}}function K(t,{dataPipeline:e}={}){return(i,{writer:n})=>{if(!$(i,t))return null;if(!e)return n.createContainerElement("span",{class:"ck-list-bogus-paragraph"});const s=n.createContainerElement("p");return n.setCustomProperty("dataPipeline:transparentRendering",!0,s),s}}function $(t,e,i=u(t)){if(!d(t))return!1;for(const i of t.getAttributeKeys())if(!i.startsWith("selection:")&&!e.includes(i))return!1;return i.length<2}var q=i(945),W=i(62),Z=i.n(W),G=i(118),Y={injectType:"singletonStyleTag",attributes:{"data-cke":!0},insert:"head",singleton:!0};Z()(G.Z,Y);G.Z.locals;var J=i(479),Q={injectType:"singletonStyleTag",attributes:{"data-cke":!0},insert:"head",singleton:!0};Z()(J.Z,Q);J.Z.locals;const X=["listType","listIndent","listItemId"];class tt extends t.Plugin{static get pluginName(){return"DocumentListEditing"}static get requires(){return[s.Enter,e.Delete,V,q.ClipboardPipeline]}constructor(t){super(t),this._downcastStrategies=[],t.config.define("list.multiBlock",!0)}init(){const t=this.editor,e=t.model,i=t.config.get("list.multiBlock");if(t.plugins.has("ListEditing"))throw new r.CKEditorError("document-list-feature-conflict",this,{conflictPlugin:"ListEditing"});e.schema.register("$listItem",{allowAttributes:X}),i?(e.schema.extend("$container",{allowAttributesOf:"$listItem"}),e.schema.extend("$block",{allowAttributesOf:"$listItem"}),e.schema.extend("$blockObject",{allowAttributesOf:"$listItem"})):e.schema.register("listItem",{inheritAllFrom:"$block",allowAttributesOf:"$listItem"});for(const t of X)e.schema.setAttributeProperties(t,{copyOnReplace:!0});t.commands.add("numberedList",new L(t,"numbered")),t.commands.add("bulletedList",new L(t,"bulleted")),t.commands.add("indentList",new S(t,"forward")),t.commands.add("outdentList",new S(t,"backward")),t.commands.add("splitListItemBefore",new P(t,"before")),t.commands.add("splitListItemAfter",new P(t,"after")),i&&(t.commands.add("mergeListItemBackward",new E(t,"backward")),t.commands.add("mergeListItemForward",new E(t,"forward"))),this._setupDeleteIntegration(),this._setupEnterIntegration(),this._setupTabIntegration(),this._setupClipboardIntegration()}afterInit(){const t=this.editor.commands,e=t.get("indent"),i=t.get("outdent");e&&e.registerChildCommand(t.get("indentList"),{priority:"high"}),i&&i.registerChildCommand(t.get("outdentList"),{priority:"lowest"}),this._setupModelPostFixing(),this._setupConversion()}registerDowncastStrategy(t){this._downcastStrategies.push(t)}getListAttributeNames(){return[...X,...this._downcastStrategies.map((t=>t.attributeName))]}_setupDeleteIntegration(){const t=this.editor,e=t.commands.get("mergeListItemBackward"),i=t.commands.get("mergeListItemForward");this.listenTo(t.editing.view.document,"delete",((n,s)=>{const r=t.model.document.selection;T(t.model)||t.model.change((()=>{const l=r.getFirstPosition();if(r.isCollapsed&&"backward"==s.direction){if(!l.isAtStart)return;const i=l.parent;if(!d(i))return;if(o.first(i,{sameAttributes:"listType",sameIndent:!0})||0!==i.getAttribute("listIndent")){if(!e||!e.isEnabled)return;e.execute({shouldMergeOnBlocksContentLevel:et(t.model,"backward")})}else f(i)||t.execute("splitListItemAfter"),t.execute("outdentList");s.preventDefault(),n.stop()}else{if(r.isCollapsed&&!r.getLastPosition().isAtEnd)return;if(!i||!i.isEnabled)return;i.execute({shouldMergeOnBlocksContentLevel:et(t.model,"forward")}),s.preventDefault(),n.stop()}}))}),{context:"li"})}_setupEnterIntegration(){const t=this.editor,e=t.model,i=t.commands,n=i.get("enter");this.listenTo(t.editing.view.document,"enter",((i,n)=>{const s=e.document,r=s.selection.getFirstPosition().parent;if(s.selection.isCollapsed&&d(r)&&r.isEmpty&&!n.isSoft){const e=h(r),s=f(r);e&&s?(t.execute("outdentList"),n.preventDefault(),i.stop()):e&&!s?(t.execute("splitListItemAfter"),n.preventDefault(),i.stop()):s&&(t.execute("splitListItemBefore"),n.preventDefault(),i.stop())}}),{context:"li"}),this.listenTo(n,"afterExecute",(()=>{const e=i.get("splitListItemBefore");if(e.refresh(),!e.isEnabled)return;2===u(t.model.document.selection.getLastPosition().parent).length&&e.execute()}))}_setupTabIntegration(){const t=this.editor;this.listenTo(t.editing.view.document,"tab",((e,i)=>{const n=i.shiftKey?"outdentList":"indentList";this.editor.commands.get(n).isEnabled&&(t.execute(n),i.stopPropagation(),i.preventDefault(),e.stop())}),{context:"li"})}_setupConversion(){const t=this.editor,e=t.model,i=this.getListAttributeNames(),n=t.config.get("list.multiBlock"),s=n?"paragraph":"listItem";t.conversion.for("upcast").elementToElement({view:"li",model:(t,{writer:e})=>e.createElement(s,{listType:""})}).elementToElement({view:"p",model:(t,{writer:e})=>t.parent&&t.parent.is("element","li")?e.createElement(s,{listType:""}):null,converterPriority:"high"}).add((t=>{t.on("element:li",H()),t.on("element:ul",j(),{priority:"high"}),t.on("element:ol",j(),{priority:"high"})})),n||t.conversion.for("downcast").elementToElement({model:"listItem",view:"p"}),t.conversion.for("editingDowncast").elementToElement({model:s,view:K(i),converterPriority:"high"}).add((t=>{t.on("attribute",U(i,this._downcastStrategies,e))})),t.conversion.for("dataDowncast").elementToElement({model:s,view:K(i,{dataPipeline:!0}),converterPriority:"high"}).add((t=>{t.on("attribute",U(i,this._downcastStrategies,e,{dataPipeline:!0}))})),this.listenTo(e.document,"change:data",function(t,e,i,n){return()=>{const n=t.document.differ.getChanges(),o=[],l=new Map,a=new Set;for(const t of n)if("insert"==t.type&&"$text"!=t.name)F(t.position,l),t.attributes.has("listItemId")?a.add(t.position.nodeAfter):F(t.position.getShiftedBy(t.length),l);else if("remove"==t.type&&t.attributes.has("listItemId"))F(t.position,l);else if("attribute"==t.type){const e=t.range.start.nodeAfter;i.includes(t.attributeKey)?(F(t.range.start,l),null===t.attributeNewValue?(F(t.range.start.getShiftedBy(1),l),r(e)&&o.push(e)):a.add(e)):d(e)&&r(e)&&o.push(e)}for(const t of l.values())o.push(...s(t,a));for(const t of new Set(o))e.reconvertItem(t)};function s(t,e){const n=[],s=new Set,a=[];for(const{node:c,previous:d}of l(t,"forward")){if(s.has(c))continue;const t=c.getAttribute("listIndent");d&&t<d.getAttribute("listIndent")&&(a.length=t+1),a[t]=Object.fromEntries(Array.from(c.getAttributes()).filter((([t])=>i.includes(t))));const l=m(c,{direction:"forward"});for(const t of l)s.add(t),(r(t,l)||o(t,a,e))&&n.push(t)}return n}function r(t,s){const r=e.mapper.toViewElement(t);if(!r)return!1;if(n.fire("checkElement",{modelElement:t,viewElement:r}))return!0;if(!t.is("element","paragraph")&&!t.is("element","listItem"))return!1;const o=$(t,i,s);return!(!o||!r.is("element","p"))||!(o||!r.is("element","span"))}function o(t,i,s){if(s.has(t))return!1;const r=e.mapper.toViewElement(t);let o=i.length-1;for(let t=r.parent;!t.is("editableElement");t=t.parent){const e=N(t),s=B(t);if(!s&&!e)continue;const r="checkAttributes:"+(e?"item":"list");if(n.fire(r,{viewElement:t,modelAttributes:i[o]}))break;if(s&&(o--,o<0))return!1}return!0}}(e,t.editing,i,this),{priority:"high"}),this.on("checkAttributes:item",((t,{viewElement:e,modelAttributes:i})=>{e.id!=i.listItemId&&(t.return=!0,t.stop())})),this.on("checkAttributes:list",((t,{viewElement:e,modelAttributes:i})=>{e.name==R(i.listType)&&e.id==O(i.listType,i.listIndent)||(t.return=!0,t.stop())}))}_setupModelPostFixing(){const t=this.editor.model,e=this.getListAttributeNames();t.document.registerPostFixer((i=>function(t,e,i,n){const s=t.document.differ.getChanges(),r=new Map,o=n.editor.config.get("list.multiBlock");let l=!1;for(const n of s){if("insert"==n.type&&"$text"!=n.name){const s=n.position.nodeAfter;if(!t.schema.checkAttribute(s,"listItemId"))for(const t of Array.from(s.getAttributeKeys()))i.includes(t)&&(e.removeAttribute(t,s),l=!0);F(n.position,r),n.attributes.has("listItemId")||F(n.position.getShiftedBy(n.length),r);for(const{item:e,previousPosition:i}of t.createRangeIn(s))d(e)&&F(i,r)}else"remove"==n.type?F(n.position,r):"attribute"==n.type&&i.includes(n.attributeKey)&&(F(n.range.start,r),null===n.attributeNewValue&&F(n.range.start.getShiftedBy(1),r));if(!o&&"attribute"==n.type&&X.includes(n.attributeKey)){const t=n.range.start.nodeAfter;null===n.attributeNewValue&&t&&t.is("element","listItem")?(e.rename(t,"paragraph"),l=!0):null===n.attributeOldValue&&t&&t.is("element")&&"listItem"!=t.name&&(e.rename(t,"listItem"),l=!0)}}const c=new Set;for(const t of r.values())l=n.fire("postFixer",{listNodes:new a(t),listHead:t,writer:e,seenIds:c})||l;return l}(t,i,e,this))),this.on("postFixer",((t,{listNodes:e,writer:i})=>{t.return=function(t,e){let i=0,n=-1,s=null,r=!1;for(const{node:o}of t){const t=o.getAttribute("listIndent");if(t>i){let l;null===s?(s=t-i,l=i):(s>t&&(s=t),l=t-s),l>n+1&&(l=n+1),e.setAttribute("listIndent",l,o),r=!0,n=l}else s=null,i=t+1,n=t}return r}(e,i)||t.return}),{priority:"high"}),this.on("postFixer",((t,{listNodes:e,writer:i,seenIds:n})=>{t.return=function(t,e,i){const n=new Set;let s=!1;for(const{node:r}of t){if(n.has(r))continue;let t=r.getAttribute("listType"),o=r.getAttribute("listItemId");if(e.has(o)&&(o=c.next()),e.add(o),r.is("element","listItem"))r.getAttribute("listItemId")!=o&&(i.setAttribute("listItemId",o,r),s=!0);else for(const e of m(r,{direction:"forward"}))n.add(e),e.getAttribute("listType")!=t&&(o=c.next(),t=e.getAttribute("listType")),e.getAttribute("listItemId")!=o&&(i.setAttribute("listItemId",o,e),s=!0)}return s}(e,n,i)||t.return}),{priority:"high"})}_setupClipboardIntegration(){const t=this.editor.model,e=this.editor.plugins.get("ClipboardPipeline");this.listenTo(t,"insertContent",function(t){return(e,[i,n])=>{const s=i.is("documentFragment")?Array.from(i.getChildren()):[i];if(!s.length)return;const r=(n?t.createSelection(n):t.document.selection).getFirstPosition();let o;if(d(r.parent))o=r.parent;else{if(!d(r.nodeBefore))return;o=r.nodeBefore}t.change((t=>{const e=o.getAttribute("listType"),i=o.getAttribute("listIndent"),n=s[0].getAttribute("listIndent")||0,r=Math.max(i-n,0);for(const i of s){const n=d(i);o.is("element","listItem")&&i.is("element","paragraph")&&t.rename(i,"listItem"),t.setAttributes({listIndent:(n?i.getAttribute("listIndent"):0)+r,listItemId:n?i.getAttribute("listItemId"):c.next(),listType:e},i)}}))}}(t),{priority:"high"}),this.listenTo(e,"outputTransformation",((e,i)=>{t.change((t=>{const e=Array.from(i.content.getChildren()),n=e[e.length-1];if(e.length>1&&n.is("element")&&n.isEmpty){e.slice(0,-1).every(d)&&t.remove(n)}if("copy"==i.method||"cut"==i.method){const e=Array.from(i.content.getChildren());I(e)&&A(e,t)}}))}))}}function et(t,e){const i=t.document.selection;if(!i.isCollapsed)return!T(t);if("forward"===e)return!0;const n=i.getFirstPosition().parent,s=n.previousSibling;return!t.schema.isObject(s)&&(!!s.isEmpty||I([n,s]))}var it=i(273);function nt(t,e){const i=e.mapper,n=e.writer,s="numbered"==t.getAttribute("listType")?"ol":"ul",r=function(t){const e=t.createContainerElement("li");return e.getFillerOffset=ft,e}(n),o=n.createContainerElement(s,null);return n.insert(n.createPositionAt(o,0),r),i.bindElements(t,r),r}function st(t,e,i,n){const s=e.parent,r=i.mapper,o=i.writer;let l=r.toViewPosition(n.createPositionBefore(t));const a=lt(t.previousSibling,{sameIndent:!0,smallerIndent:!0,listIndent:t.getAttribute("listIndent")}),c=t.previousSibling;if(a&&a.getAttribute("listIndent")==t.getAttribute("listIndent")){const t=r.toViewElement(a);l=o.breakContainer(o.createPositionAfter(t))}else if(c&&"listItem"==c.name){l=r.toViewPosition(n.createPositionAt(c,"end"));const t=r.findMappedViewAncestor(l),e=ct(t);l=e?o.createPositionBefore(e):o.createPositionAt(t,"end")}else l=r.toViewPosition(n.createPositionBefore(t));if(l=ot(l),o.insert(l,s),c&&"listItem"==c.name){const t=r.toViewElement(c),i=o.createRange(o.createPositionAt(t,0),l).getWalker({ignoreElementEnd:!0});for(const t of i)if(t.item.is("element","li")){const n=o.breakContainer(o.createPositionBefore(t.item)),s=t.item.parent,r=o.createPositionAt(e,"end");rt(o,r.nodeBefore,r.nodeAfter),o.move(o.createRangeOn(s),r),i._position=n}}else{const i=s.nextSibling;if(i&&(i.is("element","ul")||i.is("element","ol"))){let n=null;for(const e of i.getChildren()){const i=r.toModelElement(e);if(!(i&&i.getAttribute("listIndent")>t.getAttribute("listIndent")))break;n=e}n&&(o.breakContainer(o.createPositionAfter(n)),o.move(o.createRangeOn(n.parent),o.createPositionAt(e,"end")))}}rt(o,s,s.nextSibling),rt(o,s.previousSibling,s)}function rt(t,e,i){return!e||!i||"ul"!=e.name&&"ol"!=e.name||e.name!=i.name||e.getAttribute("class")!==i.getAttribute("class")?null:t.mergeContainers(t.createPositionAfter(e))}function ot(t){return t.getLastMatchingPosition((t=>t.item.is("uiElement")))}function lt(t,e){const i=!!e.sameIndent,n=!!e.smallerIndent,s=e.listIndent;let r=t;for(;r&&"listItem"==r.name;){const t=r.getAttribute("listIndent");if(i&&s==t||n&&s>t)return r;r="forward"===e.direction?r.nextSibling:r.previousSibling}return null}function at(t,e,i,n){t.ui.componentFactory.add(e,(s=>{const r=t.commands.get(e),o=new it.ButtonView(s);return o.set({label:i,icon:n,tooltip:!0,isToggleable:!0}),o.bind("isOn","isEnabled").to(r,"value","isEnabled"),o.on("execute",(()=>{t.execute(e),t.editing.view.focus()})),o}))}function ct(t){for(const e of t.getChildren())if("ul"==e.name||"ol"==e.name)return e;return null}function dt(t,e){const i=[],n=t.parent,s={ignoreElementEnd:!1,startPosition:t,shallow:!0,direction:e},r=n.getAttribute("listIndent"),o=[...new z.TreeWalker(s)].filter((t=>t.item.is("element"))).map((t=>t.item));for(const t of o){if(!t.is("element","listItem"))break;if(t.getAttribute("listIndent")<r)break;if(!(t.getAttribute("listIndent")>r)){if(t.getAttribute("listType")!==n.getAttribute("listType"))break;if(t.getAttribute("listStyle")!==n.getAttribute("listStyle"))break;if(t.getAttribute("listReversed")!==n.getAttribute("listReversed"))break;if(t.getAttribute("listStart")!==n.getAttribute("listStart"))break;"backward"===e?i.unshift(t):i.push(t)}}return i}function ut(t){let e=[...t.document.selection.getSelectedBlocks()].filter((t=>t.is("element","listItem"))).map((e=>{const i=t.change((t=>t.createPositionAt(e,0)));return[...dt(i,"backward"),...dt(i,"forward")]})).flat();return e=[...new Set(e)],e}const mt=["disc","circle","square"],pt=["decimal","decimal-leading-zero","lower-roman","upper-roman","lower-latin","upper-latin"];function ht(t){return mt.includes(t)?"bulleted":pt.includes(t)?"numbered":null}function ft(){const t=!this.isEmpty&&("ul"==this.getChild(0).name||"ol"==this.getChild(0).name);return this.isEmpty||t?0:z.getFillerOffset.call(this)}const gt='<svg viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="M7 5.75c0 .414.336.75.75.75h9.5a.75.75 0 1 0 0-1.5h-9.5a.75.75 0 0 0-.75.75zM3.5 3v5H2V3.7H1v-1h2.5V3zM.343 17.857l2.59-3.257H2.92a.6.6 0 1 0-1.04 0H.302a2 2 0 1 1 3.995 0h-.001c-.048.405-.16.734-.333.988-.175.254-.59.692-1.244 1.312H4.3v1h-4l.043-.043zM7 14.75a.75.75 0 0 1 .75-.75h9.5a.75.75 0 1 1 0 1.5h-9.5a.75.75 0 0 1-.75-.75z"/></svg>',bt='<svg viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="M7 5.75c0 .414.336.75.75.75h9.5a.75.75 0 1 0 0-1.5h-9.5a.75.75 0 0 0-.75.75zm-6 0C1 4.784 1.777 4 2.75 4c.966 0 1.75.777 1.75 1.75 0 .966-.777 1.75-1.75 1.75C1.784 7.5 1 6.723 1 5.75zm6 9c0 .414.336.75.75.75h9.5a.75.75 0 1 0 0-1.5h-9.5a.75.75 0 0 0-.75.75zm-6 0c0-.966.777-1.75 1.75-1.75.966 0 1.75.777 1.75 1.75 0 .966-.777 1.75-1.75 1.75-.966 0-1.75-.777-1.75-1.75z"/></svg>';class yt extends t.Plugin{static get pluginName(){return"ListUI"}init(){const t=this.editor.t;at(this.editor,"numberedList",t("Numbered List"),gt),at(this.editor,"bulletedList",t("Bulleted List"),bt)}}class vt extends t.Plugin{static get requires(){return[tt,yt]}static get pluginName(){return"DocumentList"}}class wt extends t.Plugin{static get pluginName(){return"AdjacentListsSupport"}init(){const t=this.editor;t.model.schema.register("listSeparator",{allowWhere:"$block",isBlock:!0}),t.conversion.for("upcast").add((t=>{t.on("element:ol",At()),t.on("element:ul",At())})).elementToElement({model:"listSeparator",view:"ck-list-separator"}),t.conversion.for("editingDowncast").elementToElement({model:"listSeparator",view:{name:"div",classes:["ck-list-separator","ck-hidden"]}}),t.conversion.for("dataDowncast").elementToElement({model:"listSeparator",view:(t,e)=>{const i=e.writer.createContainerElement("ck-list-separator");return e.writer.setCustomProperty("dataPipeline:transparentRendering",!0,i),i.getFillerOffset=()=>null,i}})}}function At(){return(t,e,i)=>{const n=e.viewItem,s=n.nextSibling;if(!s)return;if(n.name!==s.name)return;e.modelRange||Object.assign(e,i.convertChildren(e.viewItem,e.modelCursor));const r=i.writer,o=r.createElement("listSeparator");if(!i.safeInsert(o,e.modelCursor))return;const l=i.getSplitParts(o);e.modelRange=r.createRange(e.modelRange.start,r.createPositionAfter(l[l.length-1])),i.updateConversionResult(o,e)}}class It extends t.Command{refresh(){const t=this._getValue();this.value=t,this.isEnabled=null!=t}execute({startIndex:t=1}={}){const e=this.editor.model,i=e.document;let n=Array.from(i.selection.getSelectedBlocks()).filter((t=>d(t)&&"numbered"==t.getAttribute("listType")));n=b(n),e.change((e=>{for(const i of n)e.setAttribute("listStart",t>=0?t:1,i)}))}_getValue(){const t=this.editor.model.document,e=(0,r.first)(t.selection.getSelectedBlocks());return e&&d(e)&&"numbered"==e.getAttribute("listType")?e.getAttribute("listStart"):null}}const kt={},Tt={},xt={},_t=[{listStyle:"disc",typeAttribute:"disc",listType:"bulleted"},{listStyle:"circle",typeAttribute:"circle",listType:"bulleted"},{listStyle:"square",typeAttribute:"square",listType:"bulleted"},{listStyle:"decimal",typeAttribute:"1",listType:"numbered"},{listStyle:"decimal-leading-zero",typeAttribute:null,listType:"numbered"},{listStyle:"lower-roman",typeAttribute:"i",listType:"numbered"},{listStyle:"upper-roman",typeAttribute:"I",listType:"numbered"},{listStyle:"lower-alpha",typeAttribute:"a",listType:"numbered"},{listStyle:"upper-alpha",typeAttribute:"A",listType:"numbered"},{listStyle:"lower-latin",typeAttribute:"a",listType:"numbered"},{listStyle:"upper-latin",typeAttribute:"A",listType:"numbered"}];for(const{listStyle:t,typeAttribute:e,listType:i}of _t)kt[t]=i,Tt[t]=e,e&&(xt[e]=t);function St(){return _t.map((t=>t.listStyle))}function Ct(t){return kt[t]||null}function Lt(t){return xt[t]||null}function Et(t){return Tt[t]||null}class Pt extends t.Command{constructor(t,e,i){super(t),this.defaultType=e,this._supportedTypes=i}refresh(){this.value=this._getValue(),this.isEnabled=this._checkEnabled()}execute(t={}){const e=this.editor.model,i=e.document;e.change((e=>{this._tryToConvertItemsToList(t);let n=Array.from(i.selection.getSelectedBlocks()).filter((t=>t.hasAttribute("listType")));if(n.length){n=b(n);for(const i of n)e.setAttribute("listStyle",t.type||this.defaultType,i)}}))}isStyleTypeSupported(t){return!this._supportedTypes||this._supportedTypes.includes(t)}_getValue(){const t=(0,r.first)(this.editor.model.document.selection.getSelectedBlocks());return d(t)?t.getAttribute("listStyle"):null}_checkEnabled(){const t=this.editor,e=t.commands.get("numberedList"),i=t.commands.get("bulletedList");return e.isEnabled||i.isEnabled}_tryToConvertItemsToList(t){if(!t.type)return;const e=Ct(t.type);if(!e)return;const i=this.editor,n=`${e}List`;i.commands.get(n).value||i.execute(n)}}class Vt extends t.Command{refresh(){const t=this._getValue();this.value=t,this.isEnabled=null!=t}execute(t={}){const e=this.editor.model,i=e.document;let n=Array.from(i.selection.getSelectedBlocks()).filter((t=>d(t)&&"numbered"==t.getAttribute("listType")));n=b(n),e.change((e=>{for(const i of n)e.setAttribute("listReversed",!!t.reversed,i)}))}_getValue(){const t=this.editor.model.document,e=(0,r.first)(t.selection.getSelectedBlocks());return d(e)&&"numbered"==e.getAttribute("listType")?e.getAttribute("listReversed"):null}}function zt(t){return(e,i,n)=>{const{writer:s,schema:r,consumable:o}=n;if(!1===o.test(i.viewItem,t.viewConsumables))return;i.modelRange||Object.assign(i,n.convertChildren(i.viewItem,i.modelCursor));let l=!1;for(const e of i.modelRange.getItems({shallow:!0}))r.checkAttribute(e,t.attributeName)&&t.appliesToListItem(e)&&(e.hasAttribute(t.attributeName)||(s.setAttribute(t.attributeName,t.getAttributeOnUpcast(i.viewItem),e),l=!0));l&&o.consume(i.viewItem,t.viewConsumables)}}class Bt extends t.Plugin{static get pluginName(){return"DocumentListPropertiesUtils"}getAllSupportedStyleTypes(){return St()}getListTypeFromListStyleType(t){return Ct(t)}getListStyleTypeFromTypeAttribute(t){return Lt(t)}getTypeAttributeFromListStyleType(t){return Et(t)}}const Nt="default";class Mt extends t.Plugin{static get requires(){return[tt,Bt]}static get pluginName(){return"DocumentListPropertiesEditing"}constructor(t){super(t),t.config.define("list.properties",{styles:!0,startIndex:!1,reversed:!1})}init(){const t=this.editor,e=t.model,i=t.plugins.get(tt),n=function(t){const e=[];if(t.styles){const i="object"==typeof t.styles&&t.styles.useAttribute;e.push({attributeName:"listStyle",defaultValue:Nt,viewConsumables:{styles:"list-style-type"},addCommand(t){let e=St();i&&(e=e.filter((t=>!!Et(t)))),t.commands.add("listStyle",new Pt(t,Nt,e))},appliesToListItem:t=>"numbered"==t.getAttribute("listType")||"bulleted"==t.getAttribute("listType"),hasValidAttribute(t){if(!this.appliesToListItem(t))return!t.hasAttribute("listStyle");if(!t.hasAttribute("listStyle"))return!1;const e=t.getAttribute("listStyle");return e==Nt||Ct(e)==t.getAttribute("listType")},setAttributeOnDowncast(t,e,n){if(e&&e!==Nt){if(!i)return void t.setStyle("list-style-type",e,n);{const i=Et(e);if(i)return void t.setAttribute("type",i,n)}}t.removeStyle("list-style-type",n),t.removeAttribute("type",n)},getAttributeOnUpcast(t){const e=t.getStyle("list-style-type");if(e)return e;const i=t.getAttribute("type");return i?Lt(i):Nt}})}t.reversed&&e.push({attributeName:"listReversed",defaultValue:!1,viewConsumables:{attributes:"reversed"},addCommand(t){t.commands.add("listReversed",new Vt(t))},appliesToListItem:t=>"numbered"==t.getAttribute("listType"),hasValidAttribute(t){return this.appliesToListItem(t)==t.hasAttribute("listReversed")},setAttributeOnDowncast(t,e,i){e?t.setAttribute("reversed","reversed",i):t.removeAttribute("reversed",i)},getAttributeOnUpcast:t=>t.hasAttribute("reversed")});t.startIndex&&e.push({attributeName:"listStart",defaultValue:1,viewConsumables:{attributes:"start"},addCommand(t){t.commands.add("listStart",new It(t))},appliesToListItem:t=>"numbered"==t.getAttribute("listType"),hasValidAttribute(t){return this.appliesToListItem(t)==t.hasAttribute("listStart")},setAttributeOnDowncast(t,e,i){0==e||e>1?t.setAttribute("start",e,i):t.removeAttribute("start",i)},getAttributeOnUpcast(t){const e=t.getAttribute("start");return e>=0?e:1}});return e}(t.config.get("list.properties"));for(const s of n)s.addCommand(t),e.schema.extend("$listItem",{allowAttributes:s.attributeName}),i.registerDowncastStrategy({scope:"list",attributeName:s.attributeName,setAttributeOnDowncast(t,e,i){s.setAttributeOnDowncast(t,e,i)}});t.conversion.for("upcast").add((t=>{for(const e of n)t.on("element:ol",zt(e)),t.on("element:ul",zt(e))})),i.on("checkAttributes:list",((t,{viewElement:e,modelAttributes:i})=>{for(const s of n)s.getAttributeOnUpcast(e)!=i[s.attributeName]&&(t.return=!0,t.stop())})),this.listenTo(t.commands.get("indentList"),"afterExecute",((t,i)=>{e.change((t=>{for(const e of i)for(const i of n)i.appliesToListItem(e)&&t.setAttribute(i.attributeName,i.defaultValue,e)}))})),i.on("postFixer",((t,{listNodes:e,writer:i})=>{for(const{node:s}of e)for(const e of n)e.hasValidAttribute(s)||(e.appliesToListItem(s)?i.setAttribute(e.attributeName,e.defaultValue,s):i.removeAttribute(e.attributeName,s),t.return=!0)})),i.on("postFixer",((t,{listNodes:e,writer:i})=>{for(const{node:s,previousNodeInList:r}of e)if(r&&r.getAttribute("listType")==s.getAttribute("listType"))for(const e of n){const{attributeName:n}=e;if(!e.appliesToListItem(s))continue;const o=r.getAttribute(n);s.getAttribute(n)!=o&&(i.setAttribute(n,o,s),t.return=!0)}}))}}var Dt=i(463),Rt={injectType:"singletonStyleTag",attributes:{"data-cke":!0},insert:"head",singleton:!0};Z()(Dt.Z,Rt);Dt.Z.locals;class Ot extends it.View{constructor(t,{enabledProperties:e,styleButtonViews:i,styleGridAriaLabel:n}){super(t),this.stylesView=null,this.additionalPropertiesCollapsibleView=null,this.startIndexFieldView=null,this.reversedSwitchButtonView=null,this.focusTracker=new r.FocusTracker,this.keystrokes=new r.KeystrokeHandler,this.focusables=new it.ViewCollection;const s=["ck","ck-list-properties"];this.children=this.createCollection(),this.focusCycler=new it.FocusCycler({focusables:this.focusables,focusTracker:this.focusTracker,keystrokeHandler:this.keystrokes,actions:{focusPrevious:"shift + tab",focusNext:"tab"}}),e.styles?(this.stylesView=this._createStylesView(i,n),this.children.add(this.stylesView)):s.push("ck-list-properties_without-styles"),(e.startIndex||e.reversed)&&(this._addNumberedListPropertyViews(e),s.push("ck-list-properties_with-numbered-properties")),this.setTemplate({tag:"div",attributes:{class:s},children:this.children})}render(){if(super.render(),this.stylesView){this.focusables.add(this.stylesView),this.focusTracker.add(this.stylesView.element),(this.startIndexFieldView||this.reversedSwitchButtonView)&&(this.focusables.add(this.children.last.buttonView),this.focusTracker.add(this.children.last.buttonView.element));for(const t of this.stylesView.children)this.stylesView.focusTracker.add(t.element);(0,it.addKeyboardHandlingForGrid)({keystrokeHandler:this.stylesView.keystrokes,focusTracker:this.stylesView.focusTracker,gridItems:this.stylesView.children,numberOfColumns:()=>r.global.window.getComputedStyle(this.stylesView.element).getPropertyValue("grid-template-columns").split(" ").length,uiLanguageDirection:this.locale&&this.locale.uiLanguageDirection})}if(this.startIndexFieldView){this.focusables.add(this.startIndexFieldView),this.focusTracker.add(this.startIndexFieldView.element);const t=t=>t.stopPropagation();this.keystrokes.set("arrowright",t),this.keystrokes.set("arrowleft",t),this.keystrokes.set("arrowup",t),this.keystrokes.set("arrowdown",t)}this.reversedSwitchButtonView&&(this.focusables.add(this.reversedSwitchButtonView),this.focusTracker.add(this.reversedSwitchButtonView.element)),this.keystrokes.listenTo(this.element)}focus(){this.focusCycler.focusFirst()}focusLast(){this.focusCycler.focusLast()}destroy(){super.destroy(),this.focusTracker.destroy(),this.keystrokes.destroy()}_createStylesView(t,e){const i=new it.View(this.locale);return i.children=i.createCollection(),i.children.addMany(t),i.setTemplate({tag:"div",attributes:{"aria-label":e,class:["ck","ck-list-styles-list"]},children:i.children}),i.children.delegate("execute").to(this),i.focus=function(){this.children.first.focus()},i.focusTracker=new r.FocusTracker,i.keystrokes=new r.KeystrokeHandler,i.render(),i.keystrokes.listenTo(i.element),i}_addNumberedListPropertyViews(t){const e=this.locale.t,i=[];t.startIndex&&(this.startIndexFieldView=this._createStartIndexField(),i.push(this.startIndexFieldView)),t.reversed&&(this.reversedSwitchButtonView=this._createReversedSwitchButton(),i.push(this.reversedSwitchButtonView)),t.styles?(this.additionalPropertiesCollapsibleView=new it.CollapsibleView(this.locale,i),this.additionalPropertiesCollapsibleView.set({label:e("List properties"),isCollapsed:!0}),this.additionalPropertiesCollapsibleView.buttonView.bind("isEnabled").toMany(i,"isEnabled",((...t)=>t.some((t=>t)))),this.additionalPropertiesCollapsibleView.buttonView.on("change:isEnabled",((t,e,i)=>{i||(this.additionalPropertiesCollapsibleView.isCollapsed=!0)})),this.children.add(this.additionalPropertiesCollapsibleView)):this.children.addMany(i)}_createStartIndexField(){const t=this.locale.t,e=new it.LabeledFieldView(this.locale,it.createLabeledInputNumber);return e.set({label:t("Start at"),class:"ck-numbered-list-properties__start-index"}),e.fieldView.set({min:0,step:1,value:1,inputMode:"numeric"}),e.fieldView.on("input",(()=>{const i=e.fieldView.element,n=i.valueAsNumber;Number.isNaN(n)||(i.checkValidity()?this.fire("listStart",{startIndex:n}):e.errorText=t("Start index must be greater than 0."))})),e}_createReversedSwitchButton(){const t=this.locale.t,e=new it.SwitchButtonView(this.locale);return e.set({withText:!0,label:t("Reversed order"),class:"ck-numbered-list-properties__reversed-order"}),e.delegate("execute").to(this,"listReversed"),e}}var Ft=i(989),Ht={injectType:"singletonStyleTag",attributes:{"data-cke":!0},insert:"head",singleton:!0};Z()(Ft.Z,Ht);Ft.Z.locals;class jt extends t.Plugin{static get pluginName(){return"ListPropertiesUI"}init(){const t=this.editor,e=t.locale.t,i=t.config.get("list.properties");i.styles&&t.ui.componentFactory.add("bulletedList",Ut({editor:t,parentCommandName:"bulletedList",buttonLabel:e("Bulleted List"),buttonIcon:bt,styleGridAriaLabel:e("Bulleted list styles toolbar"),styleDefinitions:[{label:e("Toggle the disc list style"),tooltip:e("Disc"),type:"disc",icon:'<svg viewBox="0 0 44 44" xmlns="http://www.w3.org/2000/svg"><path d="M35 29a1 1 0 0 1 1 1v1a1 1 0 0 1-1 1H18a1 1 0 0 1-1-1v-1a1 1 0 0 1 1-1h17zm0-9a1 1 0 0 1 1 1v1a1 1 0 0 1-1 1H18a1 1 0 0 1-1-1v-1a1 1 0 0 1 1-1h17zm0-9a1 1 0 0 1 1 1v1a1 1 0 0 1-1 1H18a1 1 0 0 1-1-1v-1a1 1 0 0 1 1-1h17z" fill-opacity=".163"/><path d="M11 27a3 3 0 1 1 0 6 3 3 0 0 1 0-6zm0-9a3 3 0 1 1 0 6 3 3 0 0 1 0-6zm0-9a3 3 0 1 1 0 6 3 3 0 0 1 0-6z"/></svg>'},{label:e("Toggle the circle list style"),tooltip:e("Circle"),type:"circle",icon:'<svg viewBox="0 0 44 44" xmlns="http://www.w3.org/2000/svg"><path d="M35 29a1 1 0 0 1 1 1v1a1 1 0 0 1-1 1H18a1 1 0 0 1-1-1v-1a1 1 0 0 1 1-1h17zm0-9a1 1 0 0 1 1 1v1a1 1 0 0 1-1 1H18a1 1 0 0 1-1-1v-1a1 1 0 0 1 1-1h17zm0-9a1 1 0 0 1 1 1v1a1 1 0 0 1-1 1H18a1 1 0 0 1-1-1v-1a1 1 0 0 1 1-1h17z" fill-opacity=".163"/><path d="M11 27a3 3 0 1 1 0 6 3 3 0 0 1 0-6zm0 1a2 2 0 1 0 0 4 2 2 0 0 0 0-4zm0-10a3 3 0 1 1 0 6 3 3 0 0 1 0-6zm0 1a2 2 0 1 0 0 4 2 2 0 0 0 0-4zm0-10a3 3 0 1 1 0 6 3 3 0 0 1 0-6zm0 1a2 2 0 1 0 0 4 2 2 0 0 0 0-4z"/></svg>'},{label:e("Toggle the square list style"),tooltip:e("Square"),type:"square",icon:'<svg viewBox="0 0 44 44" xmlns="http://www.w3.org/2000/svg"><path d="M35 29a1 1 0 0 1 1 1v1a1 1 0 0 1-1 1H18a1 1 0 0 1-1-1v-1a1 1 0 0 1 1-1h17zm0-9a1 1 0 0 1 1 1v1a1 1 0 0 1-1 1H18a1 1 0 0 1-1-1v-1a1 1 0 0 1 1-1h17zm0-9a1 1 0 0 1 1 1v1a1 1 0 0 1-1 1H18a1 1 0 0 1-1-1v-1a1 1 0 0 1 1-1h17z" fill-opacity=".163"/><path d="M14 27v6H8v-6h6zm0-9v6H8v-6h6zm0-9v6H8V9h6z"/></svg>'}]})),(i.styles||i.startIndex||i.reversed)&&t.ui.componentFactory.add("numberedList",Ut({editor:t,parentCommandName:"numberedList",buttonLabel:e("Numbered List"),buttonIcon:gt,styleGridAriaLabel:e("Numbered list styles toolbar"),styleDefinitions:[{label:e("Toggle the decimal list style"),tooltip:e("Decimal"),type:"decimal",icon:'<svg viewBox="0 0 44 44" xmlns="http://www.w3.org/2000/svg"><path d="M35 29a1 1 0 0 1 1 1v1a1 1 0 0 1-1 1H18a1 1 0 0 1-1-1v-1a1 1 0 0 1 1-1h17zm0-9a1 1 0 0 1 1 1v1a1 1 0 0 1-1 1H18a1 1 0 0 1-1-1v-1a1 1 0 0 1 1-1h17zm0-9a1 1 0 0 1 1 1v1a1 1 0 0 1-1 1H18a1 1 0 0 1-1-1v-1a1 1 0 0 1 1-1h17z" fill-opacity=".163"/><path d="M10.29 15V8.531H9.286c-.14.393-.4.736-.778 1.03-.378.295-.728.495-1.05.6v1.121a4.257 4.257 0 0 0 1.595-.936V15h1.235zm3.343 0v-1.235h-1.235V15h1.235zM11.3 24v-1.147H8.848c.064-.111.148-.226.252-.343.104-.117.351-.354.74-.712.39-.357.66-.631.81-.821.225-.288.39-.562.494-.824.104-.263.156-.539.156-.829 0-.51-.182-.936-.545-1.279-.363-.342-.863-.514-1.499-.514-.58 0-1.063.148-1.45.444-.387.296-.617.784-.69 1.463l1.23.124c.024-.36.112-.619.264-.774.153-.155.358-.233.616-.233.26 0 .465.074.613.***************.36.222.635 0 .25-.085.501-.255.756-.126.185-.468.536-1.024 1.055-.692.641-1.155 1.156-1.389 1.544-.234.389-.375.8-.422 1.233H11.3zm2.333 0v-1.235h-1.235V24h1.235zM9.204 34.11c.615 0 1.129-.2 1.542-.598.413-.398.62-.88.62-1.446 0-.39-.11-.722-.332-.997a1.5 1.5 0 0 0-.886-.532c.619-.337.928-.788.928-1.353 0-.399-.151-.756-.453-1.073-.366-.386-.852-.58-1.459-.58a2.25 2.25 0 0 0-.96.2 1.617 1.617 0 0 0-.668.55c-.16.232-.28.544-.358.933l1.138.194c.032-.282.123-.495.272-.642.15-.146.33-.22.54-.22.215 0 .386.065.515.194s.193.302.193.518c0 .255-.087.46-.263.613-.176.154-.43.227-.765.218l-.136 1.006c.22-.061.409-.092.567-.092.24 0 .444.09.61.272.168.182.251.428.251.739 0 .328-.087.589-.261.782a.833.833 0 0 1-.644.29.841.841 0 0 1-.607-.242c-.167-.16-.27-.394-.307-.698l-1.196.145c.062.542.285.98.668 1.316.384.335.868.503 1.45.503zm4.43-.11v-1.235h-1.236V34h1.235z"/></svg>'},{label:e("Toggle the decimal with leading zero list style"),tooltip:e("Decimal with leading zero"),type:"decimal-leading-zero",icon:'<svg viewBox="0 0 44 44" xmlns="http://www.w3.org/2000/svg"><path d="M35 29a1 1 0 0 1 1 1v1a1 1 0 0 1-1 1H18a1 1 0 0 1-1-1v-1a1 1 0 0 1 1-1h17zm0-9a1 1 0 0 1 1 1v1a1 1 0 0 1-1 1H18a1 1 0 0 1-1-1v-1a1 1 0 0 1 1-1h17zm0-9a1 1 0 0 1 1 1v1a1 1 0 0 1-1 1H18a1 1 0 0 1-1-1v-1a1 1 0 0 1 1-1h17z" fill-opacity=".163"/><path d="M5.714 15.11c.624 0 1.11-.22 1.46-.66.421-.533.632-1.408.632-2.627 0-1.222-.21-2.096-.629-2.624-.351-.445-.839-.668-1.463-.668-.624 0-1.11.22-1.459.66-.422.533-.633 1.406-.633 2.619 0 1.236.192 2.095.576 2.577.384.482.89.723 1.516.723zm0-1.024a.614.614 0 0 1-.398-.14c-.115-.094-.211-.283-.287-.565-.077-.283-.115-.802-.115-1.558s.043-1.294.128-1.613c.064-.246.155-.417.272-.512a.617.617 0 0 1 .4-.143.61.61 0 0 1 .398.143c.116.095.211.284.288.567.076.283.114.802.114 1.558s-.043 1.292-.128 1.608c-.064.246-.155.417-.272.512a.617.617 0 0 1-.4.143zm6.078.914V8.531H10.79c-.14.393-.4.736-.778 1.03-.378.295-.728.495-1.05.6v1.121a4.257 4.257 0 0 0 1.595-.936V15h1.235zm3.344 0v-1.235h-1.235V15h1.235zm-9.422 9.11c.624 0 1.11-.22 1.46-.66.421-.533.632-1.408.632-2.627 0-1.222-.21-2.096-.629-2.624-.351-.445-.839-.668-1.463-.668-.624 0-1.11.22-1.459.66-.422.533-.633 1.406-.633 2.619 0 1.236.192 2.095.576 2.577.384.482.89.723 1.516.723zm0-1.024a.614.614 0 0 1-.398-.14c-.115-.094-.211-.283-.287-.565-.077-.283-.115-.802-.115-1.558s.043-1.294.128-1.613c.064-.246.155-.417.272-.512a.617.617 0 0 1 .4-.143.61.61 0 0 1 .398.143c.116.095.211.284.288.567.076.283.114.802.114 1.558s-.043 1.292-.128 1.608c-.064.246-.155.417-.272.512a.617.617 0 0 1-.4.143zm7.088.914v-1.147H10.35c.065-.111.149-.226.253-.343.104-.117.35-.354.74-.712.39-.357.66-.631.81-.821.225-.288.39-.562.493-.824.104-.263.156-.539.156-.829 0-.51-.181-.936-.544-1.279-.364-.342-.863-.514-1.499-.514-.58 0-1.063.148-1.45.444-.387.296-.617.784-.69 1.463l1.23.124c.024-.36.112-.619.264-.774.152-.155.357-.233.615-.233.261 0 .465.074.613.***************.36.222.635 0 .25-.085.501-.255.756-.126.185-.467.536-1.024 1.055-.691.641-1.154 1.156-1.388 1.544-.235.389-.375.8-.422 1.233h4.328zm2.334 0v-1.235h-1.235V24h1.235zM5.714 34.11c.624 0 1.11-.22 1.46-.66.421-.533.632-1.408.632-2.627 0-1.222-.21-2.096-.629-2.624-.351-.445-.839-.668-1.463-.668-.624 0-1.11.22-1.459.66-.422.533-.633 1.406-.633 2.619 0 1.236.192 2.095.576 2.577.384.482.89.723 1.516.723zm0-1.024a.614.614 0 0 1-.398-.14c-.115-.094-.211-.283-.287-.565-.077-.283-.115-.802-.115-1.558s.043-1.294.128-1.613c.064-.246.155-.417.272-.512a.617.617 0 0 1 .4-.143.61.61 0 0 1 .398.143c.116.095.211.284.288.567.076.283.114.802.114 1.558s-.043 1.292-.128 1.608c-.064.246-.155.417-.272.512a.617.617 0 0 1-.4.143zm4.992 1.024c.616 0 1.13-.2 1.543-.598.413-.398.62-.88.62-1.446 0-.39-.111-.722-.332-.997a1.5 1.5 0 0 0-.886-.532c.618-.337.927-.788.927-1.353 0-.399-.15-.756-.452-1.073-.366-.386-.853-.58-1.46-.58a2.25 2.25 0 0 0-.96.2 1.617 1.617 0 0 0-.667.55c-.16.232-.28.544-.359.933l1.139.194c.032-.282.123-.495.272-.642.15-.146.33-.22.54-.22.214 0 .386.065.515.194s.193.302.193.518c0 .255-.088.46-.264.613-.175.154-.43.227-.764.218l-.136 1.006c.22-.061.408-.092.566-.092.24 0 .444.09.611.272.167.182.25.428.25.739 0 .328-.086.589-.26.782a.833.833 0 0 1-.644.29.841.841 0 0 1-.607-.242c-.167-.16-.27-.394-.308-.698l-1.195.145c.062.542.284.98.668 1.316.384.335.867.503 1.45.503zm4.43-.11v-1.235h-1.235V34h1.235z"/></svg>'},{label:e("Toggle the lower–roman list style"),tooltip:e("Lower–roman"),type:"lower-roman",icon:'<svg viewBox="0 0 44 44" xmlns="http://www.w3.org/2000/svg"><path d="M35 29a1 1 0 0 1 1 1v1a1 1 0 0 1-1 1H18a1 1 0 0 1-1-1v-1a1 1 0 0 1 1-1h17zm0-9a1 1 0 0 1 1 1v1a1 1 0 0 1-1 1H18a1 1 0 0 1-1-1v-1a1 1 0 0 1 1-1h17zm0-9a1 1 0 0 1 1 1v1a1 1 0 0 1-1 1H18a1 1 0 0 1-1-1v-1a1 1 0 0 1 1-1h17z" fill-opacity=".163"/><path d="M11.88 8.7V7.558h-1.234V8.7h1.234zm0 5.3V9.333h-1.234V14h1.234zm2.5 0v-1.235h-1.234V14h1.235zm-4.75 4.7v-1.142H8.395V18.7H9.63zm0 5.3v-4.667H8.395V24H9.63zm2.5-5.3v-1.142h-1.234V18.7h1.235zm0 5.3v-4.667h-1.234V24h1.235zm2.501 0v-1.235h-1.235V24h1.235zM7.38 28.7v-1.142H6.145V28.7H7.38zm0 5.3v-4.667H6.145V34H7.38zm2.5-5.3v-1.142H8.646V28.7H9.88zm0 5.3v-4.667H8.646V34H9.88zm2.5-5.3v-1.142h-1.234V28.7h1.235zm0 5.3v-4.667h-1.234V34h1.235zm2.501 0v-1.235h-1.235V34h1.235z"/></svg>'},{label:e("Toggle the upper–roman list style"),tooltip:e("Upper-roman"),type:"upper-roman",icon:'<svg viewBox="0 0 44 44" xmlns="http://www.w3.org/2000/svg"><path d="M35 29a1 1 0 0 1 1 1v1a1 1 0 0 1-1 1H18a1 1 0 0 1-1-1v-1a1 1 0 0 1 1-1h17zm0-9a1 1 0 0 1 1 1v1a1 1 0 0 1-1 1H18a1 1 0 0 1-1-1v-1a1 1 0 0 1 1-1h17zm0-9a1 1 0 0 1 1 1v1a1 1 0 0 1-1 1H18a1 1 0 0 1-1-1v-1a1 1 0 0 1 1-1h17z" fill-opacity=".163"/><path d="M11.916 15V8.558h-1.301V15h1.3zm2.465 0v-1.235h-1.235V15h1.235zM9.665 25v-6.442h-1.3V25h1.3zm2.5 0v-6.442h-1.3V25h1.3zm2.466 0v-1.235h-1.235V25h1.235zm-7.216 9v-6.442h-1.3V34h1.3zm2.5 0v-6.442h-1.3V34h1.3zm2.501 0v-6.442h-1.3V34h1.3zm2.465 0v-1.235h-1.235V34h1.235z"/></svg>'},{label:e("Toggle the lower–latin list style"),tooltip:e("Lower-latin"),type:"lower-latin",icon:'<svg viewBox="0 0 44 44" xmlns="http://www.w3.org/2000/svg"><path d="M35 29a1 1 0 0 1 1 1v1a1 1 0 0 1-1 1H18a1 1 0 0 1-1-1v-1a1 1 0 0 1 1-1h17zm0-9a1 1 0 0 1 1 1v1a1 1 0 0 1-1 1H18a1 1 0 0 1-1-1v-1a1 1 0 0 1 1-1h17zm0-9a1 1 0 0 1 1 1v1a1 1 0 0 1-1 1H18a1 1 0 0 1-1-1v-1a1 1 0 0 1 1-1h17z" fill-opacity=".163"/><path d="M9.62 14.105c.272 0 .528-.05.768-.153s.466-.257.677-.462c.***************.***************.086.283.119.365h1.221a2.649 2.649 0 0 1-.222-.626c-.04-.195-.059-.498-.059-.908l.013-1.441c0-.536-.055-.905-.165-1.105-.11-.201-.3-.367-.569-.497-.27-.13-.68-.195-1.23-.195-.607 0-1.064.108-1.371.325-.308.217-.525.55-.65 1.002l1.12.202c.076-.217.176-.369.299-.455.123-.086.294-.13.514-.13.325 0 .546.05.663.***************.27.176.508v.123c-.222.093-.622.194-1.2.303-.427.082-.755.178-.982.288-.227.11-.403.268-.53.474a1.327 1.327 0 0 0-.188.706c0 .398.138.728.415.988.277.261.656.391 1.136.391zm.368-.87a.675.675 0 0 1-.492-.189.606.606 0 0 1-.193-.448c0-.176.08-.32.241-.435.106-.07.33-.142.673-.215a7.19 7.19 0 0 0 .751-.19v.247c0 .296-.016.496-.048.602a.773.773 0 0 1-.295.409 1.07 1.07 0 0 1-.637.22zm4.645.765v-1.235h-1.235V14h1.235zM10.2 25.105c.542 0 1.003-.215 1.382-.646.38-.43.57-1.044.57-1.84 0-.771-.187-1.362-.559-1.774a1.82 1.82 0 0 0-1.41-.617c-.522 0-.973.216-1.354.65v-2.32H7.594V25h1.147v-.686a1.9 1.9 0 0 0 .67.592c.26.133.523.2.79.2zm-.299-.975c-.354 0-.638-.164-.852-.492-.153-.232-.229-.59-.229-1.073 0-.468.098-.818.295-1.048a.93.93 0 0 1 .738-.345c.302 0 .55.118.743.354.193.236.29.62.29 1.154 0 .5-.096.868-.288 1.1-.192.233-.424.35-.697.35zm4.478.87v-1.235h-1.234V25h1.234zm-4.017 9.105c.6 0 1.08-.142 1.437-.426.357-.284.599-.704.725-1.261l-1.213-.207c-.061.326-.167.555-.316.688a.832.832 0 0 1-.576.2.916.916 0 0 1-.75-.343c-.185-.228-.278-.62-.278-1.173 0-.498.091-.853.274-1.066.183-.212.429-.318.736-.318.232 0 .42.061.565.***************.306.28.55l1.216-.22c-.146-.501-.387-.874-.722-1.119-.336-.244-.788-.366-1.356-.366-.695 0-1.245.214-1.653.643-.407.43-.61 1.03-.61 1.8 0 .762.202 1.358.608 1.788.406.431.95.646 1.633.646zM14.633 34v-1.235h-1.235V34h1.235z"/></svg>'},{label:e("Toggle the upper–latin list style"),tooltip:e("Upper-latin"),type:"upper-latin",icon:'<svg viewBox="0 0 44 44" xmlns="http://www.w3.org/2000/svg"><path d="M35 29a1 1 0 0 1 1 1v1a1 1 0 0 1-1 1H18a1 1 0 0 1-1-1v-1a1 1 0 0 1 1-1h17zm0-9a1 1 0 0 1 1 1v1a1 1 0 0 1-1 1H18a1 1 0 0 1-1-1v-1a1 1 0 0 1 1-1h17zm0-9a1 1 0 0 1 1 1v1a1 1 0 0 1-1 1H18a1 1 0 0 1-1-1v-1a1 1 0 0 1 1-1h17z" fill-opacity=".163"/><path d="m7.88 15 .532-1.463h2.575L11.549 15h1.415l-2.58-6.442H9.01L6.5 15h1.38zm2.69-2.549H8.811l.87-2.39.887 2.39zM14.88 15v-1.235h-1.234V15h1.234zM9.352 25c.83-.006 1.352-.02 1.569-.044.346-.038.636-.14.872-.305.236-.166.422-.387.558-.664.137-.277.205-.562.205-.855 0-.372-.106-.695-.317-.97-.21-.276-.512-.471-.905-.585a1.51 1.51 0 0 0 .661-.567 1.5 1.5 0 0 0 .244-.83c0-.28-.066-.53-.197-.754a1.654 1.654 0 0 0-.495-.539 1.676 1.676 0 0 0-.672-.266c-.25-.042-.63-.063-1.14-.063H7.158V25h2.193zm.142-3.88H8.46v-1.49h.747c.612 0 .983.007 *************.*************.**************.287.165.486a.68.68 0 0 1-.192.503.86.86 0 0 1-.525.23 11.47 11.47 0 0 1-.944.023h.18zm.17 2.795H8.46v-1.723h1.05c.592 0 .977.03 1.154.092.177.062.313.16.406.295a.84.84 0 0 1 .14.492c0 .228-.06.41-.181.547a.806.806 0 0 1-.473.257c-.126.026-.423.04-.892.04zM14.88 25v-1.235h-1.234V25h1.234zm-5.018 9.11c.691 0 1.262-.17 1.711-.512.45-.341.772-.864.965-1.567l-1.261-.4c-.109.472-.287.818-.536 1.037-.25.22-.547.33-.892.33-.47 0-.85-.173-1.143-.519-.293-.345-.44-.925-.44-1.74 0-.767.15-1.322.447-1.665.297-.343.684-.514 1.162-.514.346 0 .64.096.881.29.242.193.4.457.477.79l1.288-.307c-.147-.516-.367-.911-.66-1.187-.492-.465-1.132-.698-1.92-.698-.902 0-1.63.296-2.184.89-.554.593-.83 1.426-.83 2.498 0 1.014.275 1.813.825 2.397.551.585 1.254.877 2.11.877zM14.88 34v-1.235h-1.234V34h1.234z"/></svg>'}]}))}}function Ut({editor:t,parentCommandName:e,buttonLabel:i,buttonIcon:n,styleGridAriaLabel:s,styleDefinitions:r}){const o=t.commands.get(e);return l=>{const a=(0,it.createDropdown)(l,it.SplitButtonView),c=a.buttonView;return a.bind("isEnabled").to(o),a.class="ck-list-styles-dropdown",c.on("execute",(()=>{t.execute(e),t.editing.view.focus()})),c.set({label:i,icon:n,tooltip:!0,isToggleable:!0}),c.bind("isOn").to(o,"value",(t=>!!t)),a.once("change:isOpen",(()=>{const i=function({editor:t,dropdownView:e,parentCommandName:i,styleDefinitions:n,styleGridAriaLabel:s}){const r=t.locale,o=t.config.get("list.properties");let l=null;"numberedList"!=i&&(o.startIndex=!1,o.reversed=!1);if(o.styles){const e=t.commands.get("listStyle"),s=function({editor:t,listStyleCommand:e,parentCommandName:i}){const n=t.locale,s=t.commands.get(i);return({label:i,type:r,icon:o,tooltip:l})=>{const a=new it.ButtonView(n);return a.set({label:i,icon:o,tooltip:l}),e.on("change:value",(()=>{a.isOn=e.value===r})),a.on("execute",(()=>{s.value?e.value!==r?t.execute("listStyle",{type:r}):t.execute("listStyle",{type:e.defaultType}):t.model.change((()=>{t.execute("listStyle",{type:r})}))})),a}}({editor:t,parentCommandName:i,listStyleCommand:e}),r="function"==typeof e.isStyleTypeSupported?t=>e.isStyleTypeSupported(t.type):()=>!0;l=n.filter(r).map(s)}const a=new Ot(r,{styleGridAriaLabel:s,enabledProperties:o,styleButtonViews:l});o.styles&&(0,it.focusChildOnDropdownOpen)(e,(()=>a.stylesView.children.find((t=>t.isOn))));if(o.startIndex){const e=t.commands.get("listStart");a.startIndexFieldView.bind("isEnabled").to(e),a.startIndexFieldView.fieldView.bind("value").to(e),a.on("listStart",((e,i)=>t.execute("listStart",i)))}if(o.reversed){const e=t.commands.get("listReversed");a.reversedSwitchButtonView.bind("isEnabled").to(e),a.reversedSwitchButtonView.bind("isOn").to(e,"value",(t=>!!t)),a.on("listReversed",(()=>{const i=e.value;t.execute("listReversed",{reversed:!i})}))}return a.delegate("execute").to(e),a}({editor:t,dropdownView:a,parentCommandName:e,styleGridAriaLabel:s,styleDefinitions:r});a.panelView.children.add(i)})),a.on("execute",(()=>{t.editing.view.focus()})),a}}class Kt extends t.Plugin{static get requires(){return[Mt,jt]}static get pluginName(){return"DocumentListProperties"}}class $t extends t.Command{constructor(t,e){super(t),this._indentBy="forward"==e?1:-1}refresh(){this.isEnabled=this._checkEnabled()}execute(){const t=this.editor.model,e=t.document;let i=Array.from(e.selection.getSelectedBlocks());t.change((t=>{const e=i[i.length-1];let n=e.nextSibling;for(;n&&"listItem"==n.name&&n.getAttribute("listIndent")>e.getAttribute("listIndent");)i.push(n),n=n.nextSibling;this._indentBy<0&&(i=i.reverse());for(const e of i){const i=e.getAttribute("listIndent")+this._indentBy;i<0?t.rename(e,"paragraph"):t.setAttribute("listIndent",i,e)}this.fire("_executeCleanup",i)}))}_checkEnabled(){const t=(0,r.first)(this.editor.model.document.selection.getSelectedBlocks());if(!t||!t.is("element","listItem"))return!1;if(this._indentBy>0){const e=t.getAttribute("listIndent"),i=t.getAttribute("listType");let n=t.previousSibling;for(;n&&n.is("element","listItem")&&n.getAttribute("listIndent")>=e;){if(n.getAttribute("listIndent")==e)return n.getAttribute("listType")==i;n=n.previousSibling}return!1}return!0}}class qt extends t.Command{constructor(t,e){super(t),this.type=e}refresh(){this.value=this._getValue(),this.isEnabled=this._checkEnabled()}execute(t={}){const e=this.editor.model,i=e.document,n=Array.from(i.selection.getSelectedBlocks()).filter((t=>Zt(t,e.schema))),s=void 0!==t.forceValue?!t.forceValue:this.value;e.change((t=>{if(s){let e=n[n.length-1].nextSibling,i=Number.POSITIVE_INFINITY,s=[];for(;e&&"listItem"==e.name&&0!==e.getAttribute("listIndent");){const t=e.getAttribute("listIndent");t<i&&(i=t);const n=t-i;s.push({element:e,listIndent:n}),e=e.nextSibling}s=s.reverse();for(const e of s)t.setAttribute("listIndent",e.listIndent,e.element)}if(!s){let t=Number.POSITIVE_INFINITY;for(const e of n)e.is("element","listItem")&&e.getAttribute("listIndent")<t&&(t=e.getAttribute("listIndent"));t=0===t?1:t,Wt(n,!0,t),Wt(n,!1,t)}for(const e of n.reverse())s&&"listItem"==e.name?t.rename(e,"paragraph"):s||"listItem"==e.name?s||"listItem"!=e.name||e.getAttribute("listType")==this.type||t.setAttribute("listType",this.type,e):(t.setAttributes({listType:this.type,listIndent:0},e),t.rename(e,"listItem"));this.fire("_executeCleanup",n)}))}_getValue(){const t=(0,r.first)(this.editor.model.document.selection.getSelectedBlocks());return!!t&&t.is("element","listItem")&&t.getAttribute("listType")==this.type}_checkEnabled(){if(this.value)return!0;const t=this.editor.model.document.selection,e=this.editor.model.schema,i=(0,r.first)(t.getSelectedBlocks());return!!i&&Zt(i,e)}}function Wt(t,e,i){const n=e?t[0]:t[t.length-1];if(n.is("element","listItem")){let s=n[e?"previousSibling":"nextSibling"],r=n.getAttribute("listIndent");for(;s&&s.is("element","listItem")&&s.getAttribute("listIndent")>=i;)r>s.getAttribute("listIndent")&&(r=s.getAttribute("listIndent")),s.getAttribute("listIndent")==r&&t[e?"unshift":"push"](s),s=s[e?"previousSibling":"nextSibling"]}}function Zt(t,e){return e.checkChild(t.parent,"listItem")&&!e.isObject(t)}class Gt extends t.Plugin{static get pluginName(){return"ListUtils"}getListTypeFromListStyleType(t){return ht(t)}getSelectedListItems(t){return ut(t)}getSiblingNodes(t,e){return dt(t,e)}}function Yt(t){return(e,i,n)=>{const s=n.consumable;if(!s.test(i.item,"insert")||!s.test(i.item,"attribute:listType")||!s.test(i.item,"attribute:listIndent"))return;s.consume(i.item,"insert"),s.consume(i.item,"attribute:listType"),s.consume(i.item,"attribute:listIndent");const r=i.item;st(r,nt(r,n),n,t)}}const Jt=(t,e,i)=>{if(!i.consumable.test(e.item,t.name))return;const n=i.mapper.toViewElement(e.item),s=i.writer;s.breakContainer(s.createPositionBefore(n)),s.breakContainer(s.createPositionAfter(n));const r=n.parent,o="numbered"==e.attributeNewValue?"ol":"ul";s.rename(o,r)},Qt=(t,e,i)=>{i.consumable.consume(e.item,t.name);const n=i.mapper.toViewElement(e.item).parent,s=i.writer;rt(s,n,n.nextSibling),rt(s,n.previousSibling,n)};const Xt=(t,e,i)=>{if(i.consumable.test(e.item,t.name)&&"listItem"!=e.item.name){let t=i.mapper.toViewPosition(e.range.start);const n=i.writer,s=[];for(;("ul"==t.parent.name||"ol"==t.parent.name)&&(t=n.breakContainer(t),"li"==t.parent.name);){const e=t,i=n.createPositionAt(t.parent,"end");if(!e.isEqual(i)){const t=n.remove(n.createRange(e,i));s.push(t)}t=n.createPositionAfter(t.parent)}if(s.length>0){for(let e=0;e<s.length;e++){const i=t.nodeBefore;if(t=n.insert(t,s[e]).end,e>0){const e=rt(n,i,i.nextSibling);e&&e.parent==i&&t.offset--}}rt(n,t.nodeBefore,t.nodeAfter)}}},te=(t,e,i)=>{const n=i.mapper.toViewPosition(e.position),s=n.nodeBefore,r=n.nodeAfter;rt(i.writer,s,r)},ee=(t,e,i)=>{if(i.consumable.consume(e.viewItem,{name:!0})){const t=i.writer,n=t.createElement("listItem"),s=function(t){let e=0,i=t.parent;for(;i;){if(i.is("element","li"))e++;else{const t=i.previousSibling;t&&t.is("element","li")&&e++}i=i.parent}return e}(e.viewItem);t.setAttribute("listIndent",s,n);const r=e.viewItem.parent&&"ol"==e.viewItem.parent.name?"numbered":"bulleted";if(t.setAttribute("listType",r,n),!i.safeInsert(n,e.modelCursor))return;const o=function(t,e,i){const{writer:n,schema:s}=i;let r=n.createPositionAfter(t);for(const o of e)if("ul"==o.name||"ol"==o.name)r=i.convertItem(o,r).modelCursor;else{const e=i.convertItem(o,n.createPositionAt(t,"end")),l=e.modelRange.start.nodeAfter;l&&l.is("element")&&!s.checkChild(t,l.name)&&(t=e.modelCursor.parent.is("element","listItem")?e.modelCursor.parent:oe(e.modelCursor),r=n.createPositionAfter(t))}return r}(n,e.viewItem.getChildren(),i);e.modelRange=t.createRange(e.modelCursor,o),i.updateConversionResult(n,e)}},ie=(t,e,i)=>{if(i.consumable.test(e.viewItem,{name:!0})){const t=Array.from(e.viewItem.getChildren());for(const e of t){!(e.is("element","li")||ae(e))&&e._remove()}}},ne=(t,e,i)=>{if(i.consumable.test(e.viewItem,{name:!0})){if(0===e.viewItem.childCount)return;const t=[...e.viewItem.getChildren()];let i=!1;for(const e of t)i&&!ae(e)&&e._remove(),ae(e)&&(i=!0)}};function se(t){return(e,i)=>{if(i.isPhantom)return;const n=i.modelPosition.nodeBefore;if(n&&n.is("element","listItem")){const e=i.mapper.toViewElement(n),s=e.getAncestors().find(ae),r=t.createPositionAt(e,0).getWalker();for(const t of r){if("elementStart"==t.type&&t.item.is("element","li")){i.viewPosition=t.previousPosition;break}if("elementEnd"==t.type&&t.item==s){i.viewPosition=t.nextPosition;break}}}}}const re=function(t,[e,i]){const n=this;let s,r=e.is("documentFragment")?e.getChild(0):e;if(s=i?n.createSelection(i):n.document.selection,r&&r.is("element","listItem")){const t=s.getFirstPosition();let e=null;if(t.parent.is("element","listItem")?e=t.parent:t.nodeBefore&&t.nodeBefore.is("element","listItem")&&(e=t.nodeBefore),e){const t=e.getAttribute("listIndent");if(t>0)for(;r&&r.is("element","listItem");)r._setAttribute("listIndent",r.getAttribute("listIndent")+t),r=r.nextSibling}}};function oe(t){const e=new z.TreeWalker({startPosition:t});let i;do{i=e.next()}while(!i.value.item.is("element","listItem"));return i.value.item}function le(t,e,i,n,s,r){const o=lt(e.nodeBefore,{sameIndent:!0,smallerIndent:!0,listIndent:t}),l=s.mapper,a=s.writer,c=o?o.getAttribute("listIndent"):null;let d;if(o)if(c==t){const t=l.toViewElement(o).parent;d=a.createPositionAfter(t)}else{const t=r.createPositionAt(o,"end");d=l.toViewPosition(t)}else d=i;d=ot(d);for(const t of[...n.getChildren()])ae(t)&&(d=a.move(a.createRangeOn(t),d).end,rt(a,t,t.nextSibling),rt(a,t.previousSibling,t))}function ae(t){return t.is("element","ol")||t.is("element","ul")}class ce extends t.Plugin{static get pluginName(){return"ListEditing"}static get requires(){return[s.Enter,e.Delete,Gt]}init(){const t=this.editor;t.model.schema.register("listItem",{inheritAllFrom:"$block",allowAttributes:["listType","listIndent"]});const e=t.data,i=t.editing;var n;t.model.document.registerPostFixer((e=>function(t,e){const i=t.document.differ.getChanges(),n=new Map;let s=!1;for(const n of i)if("insert"==n.type&&"listItem"==n.name)r(n.position);else if("insert"==n.type&&"listItem"!=n.name){if("$text"!=n.name){const i=n.position.nodeAfter;i.hasAttribute("listIndent")&&(e.removeAttribute("listIndent",i),s=!0),i.hasAttribute("listType")&&(e.removeAttribute("listType",i),s=!0),i.hasAttribute("listStyle")&&(e.removeAttribute("listStyle",i),s=!0),i.hasAttribute("listReversed")&&(e.removeAttribute("listReversed",i),s=!0),i.hasAttribute("listStart")&&(e.removeAttribute("listStart",i),s=!0);for(const e of Array.from(t.createRangeIn(i)).filter((t=>t.item.is("element","listItem"))))r(e.previousPosition)}r(n.position.getShiftedBy(n.length))}else"remove"==n.type&&"listItem"==n.name?r(n.position):("attribute"==n.type&&"listIndent"==n.attributeKey||"attribute"==n.type&&"listType"==n.attributeKey)&&r(n.range.start);for(const t of n.values())o(t),l(t);return s;function r(t){const e=t.nodeBefore;if(e&&e.is("element","listItem")){let t=e;if(n.has(t))return;for(let e=t.previousSibling;e&&e.is("element","listItem");e=t.previousSibling)if(t=e,n.has(t))return;n.set(e,t)}else{const e=t.nodeAfter;e&&e.is("element","listItem")&&n.set(e,e)}}function o(t){let i=0,n=null;for(;t&&t.is("element","listItem");){const r=t.getAttribute("listIndent");if(r>i){let o;null===n?(n=r-i,o=i):(n>r&&(n=r),o=r-n),e.setAttribute("listIndent",o,t),s=!0}else n=null,i=t.getAttribute("listIndent")+1;t=t.nextSibling}}function l(t){let i=[],n=null;for(;t&&t.is("element","listItem");){const r=t.getAttribute("listIndent");if(n&&n.getAttribute("listIndent")>r&&(i=i.slice(0,r+1)),0!=r)if(i[r]){const n=i[r];t.getAttribute("listType")!=n&&(e.setAttribute("listType",n,t),s=!0)}else i[r]=t.getAttribute("listType");n=t,t=t.nextSibling}}}(t.model,e))),i.mapper.registerViewToModelLength("li",de),e.mapper.registerViewToModelLength("li",de),i.mapper.on("modelToViewPosition",se(i.view)),i.mapper.on("viewToModelPosition",(n=t.model,(t,e)=>{const i=e.viewPosition,s=i.parent,r=e.mapper;if("ul"==s.name||"ol"==s.name){if(i.isAtEnd){const t=r.toModelElement(i.nodeBefore),s=r.getModelLength(i.nodeBefore);e.modelPosition=n.createPositionBefore(t).getShiftedBy(s)}else{const t=r.toModelElement(i.nodeAfter);e.modelPosition=n.createPositionBefore(t)}t.stop()}else if("li"==s.name&&i.nodeBefore&&("ul"==i.nodeBefore.name||"ol"==i.nodeBefore.name)){const o=r.toModelElement(s);let l=1,a=i.nodeBefore;for(;a&&ae(a);)l+=r.getModelLength(a),a=a.previousSibling;e.modelPosition=n.createPositionBefore(o).getShiftedBy(l),t.stop()}})),e.mapper.on("modelToViewPosition",se(i.view)),t.conversion.for("editingDowncast").add((e=>{e.on("insert",Xt,{priority:"high"}),e.on("insert:listItem",Yt(t.model)),e.on("attribute:listType:listItem",Jt,{priority:"high"}),e.on("attribute:listType:listItem",Qt,{priority:"low"}),e.on("attribute:listIndent:listItem",function(t){return(e,i,n)=>{if(!n.consumable.consume(i.item,"attribute:listIndent"))return;const s=n.mapper.toViewElement(i.item),r=n.writer;r.breakContainer(r.createPositionBefore(s)),r.breakContainer(r.createPositionAfter(s));const o=s.parent,l=o.previousSibling,a=r.createRangeOn(o);r.remove(a),l&&l.nextSibling&&rt(r,l,l.nextSibling),le(i.attributeOldValue+1,i.range.start,a.start,s,n,t),st(i.item,s,n,t);for(const t of i.item.getChildren())n.consumable.consume(t,"insert")}}(t.model)),e.on("remove:listItem",function(t){return(e,i,n)=>{const s=n.mapper.toViewPosition(i.position).getLastMatchingPosition((t=>!t.item.is("element","li"))).nodeAfter,r=n.writer;r.breakContainer(r.createPositionBefore(s)),r.breakContainer(r.createPositionAfter(s));const o=s.parent,l=o.previousSibling,a=r.createRangeOn(o),c=r.remove(a);l&&l.nextSibling&&rt(r,l,l.nextSibling),le(n.mapper.toModelElement(s).getAttribute("listIndent")+1,i.position,a.start,s,n,t);for(const t of r.createRangeIn(c).getItems())n.mapper.unbindViewElement(t);e.stop()}}(t.model)),e.on("remove",te,{priority:"low"})})),t.conversion.for("dataDowncast").add((e=>{e.on("insert",Xt,{priority:"high"}),e.on("insert:listItem",Yt(t.model))})),t.conversion.for("upcast").add((t=>{t.on("element:ul",ie,{priority:"high"}),t.on("element:ol",ie,{priority:"high"}),t.on("element:li",ne,{priority:"high"}),t.on("element:li",ee)})),t.model.on("insertContent",re,{priority:"high"}),t.commands.add("numberedList",new qt(t,"numbered")),t.commands.add("bulletedList",new qt(t,"bulleted")),t.commands.add("indentList",new $t(t,"forward")),t.commands.add("outdentList",new $t(t,"backward"));const s=i.view.document;this.listenTo(s,"enter",((t,e)=>{const i=this.editor.model.document,n=i.selection.getLastPosition().parent;i.selection.isCollapsed&&"listItem"==n.name&&n.isEmpty&&(this.editor.execute("outdentList"),e.preventDefault(),t.stop())}),{context:"li"}),this.listenTo(s,"delete",((t,e)=>{if("backward"!==e.direction)return;const i=this.editor.model.document.selection;if(!i.isCollapsed)return;const n=i.getFirstPosition();if(!n.isAtStart)return;const s=n.parent;if("listItem"!==s.name)return;s.previousSibling&&"listItem"===s.previousSibling.name||(this.editor.execute("outdentList"),e.preventDefault(),t.stop())}),{context:"li"}),this.listenTo(t.editing.view.document,"tab",((e,i)=>{const n=i.shiftKey?"outdentList":"indentList";this.editor.commands.get(n).isEnabled&&(t.execute(n),i.stopPropagation(),i.preventDefault(),e.stop())}),{context:"li"})}afterInit(){const t=this.editor.commands,e=t.get("indent"),i=t.get("outdent");e&&e.registerChildCommand(t.get("indentList")),i&&i.registerChildCommand(t.get("outdentList"))}}function de(t){let e=1;for(const i of t.getChildren())if("ul"==i.name||"ol"==i.name)for(const t of i.getChildren())e+=de(t);return e}class ue extends t.Plugin{static get requires(){return[ce,yt]}static get pluginName(){return"List"}}class me extends t.Command{constructor(t,e){super(t),this.defaultType=e}refresh(){this.value=this._getValue(),this.isEnabled=this._checkEnabled()}execute(t={}){this._tryToConvertItemsToList(t);const e=this.editor.model,i=ut(e);i.length&&e.change((e=>{for(const n of i)e.setAttribute("listStyle",t.type||this.defaultType,n)}))}_getValue(){const t=this.editor.model.document.selection.getFirstPosition().parent;return t&&t.is("element","listItem")?t.getAttribute("listStyle"):null}_checkEnabled(){const t=this.editor,e=t.commands.get("numberedList"),i=t.commands.get("bulletedList");return e.isEnabled||i.isEnabled}_tryToConvertItemsToList(t){if(!t.type)return;const e=ht(t.type);if(!e)return;const i=this.editor,n=`${e}List`;i.commands.get(n).value||i.execute(n)}}class pe extends t.Command{refresh(){const t=this._getValue();this.value=t,this.isEnabled=null!=t}execute(t={}){const e=this.editor.model,i=ut(e).filter((t=>"numbered"==t.getAttribute("listType")));e.change((e=>{for(const n of i)e.setAttribute("listReversed",!!t.reversed,n)}))}_getValue(){const t=this.editor.model.document.selection.getFirstPosition().parent;return t&&t.is("element","listItem")&&"numbered"==t.getAttribute("listType")?t.getAttribute("listReversed"):null}}class he extends t.Command{refresh(){const t=this._getValue();this.value=t,this.isEnabled=null!=t}execute({startIndex:t=1}={}){const e=this.editor.model,i=ut(e).filter((t=>"numbered"==t.getAttribute("listType")));e.change((e=>{for(const n of i)e.setAttribute("listStart",t>=0?t:1,n)}))}_getValue(){const t=this.editor.model.document.selection.getFirstPosition().parent;return t&&t.is("element","listItem")&&"numbered"==t.getAttribute("listType")?t.getAttribute("listStart"):null}}const fe="default";class ge extends t.Plugin{static get requires(){return[ce]}static get pluginName(){return"ListPropertiesEditing"}constructor(t){super(t),t.config.define("list",{properties:{styles:!0,startIndex:!1,reversed:!1}})}init(){const t=this.editor,e=t.model,i=function(t){const e=[];t.styles&&e.push({attributeName:"listStyle",defaultValue:fe,addCommand(t){t.commands.add("listStyle",new me(t,fe))},appliesToListItem:()=>!0,setAttributeOnDowncast(t,e,i){e&&e!==fe?t.setStyle("list-style-type",e,i):t.removeStyle("list-style-type",i)},getAttributeOnUpcast:t=>t.getStyle("list-style-type")||fe});t.reversed&&e.push({attributeName:"listReversed",defaultValue:!1,addCommand(t){t.commands.add("listReversed",new pe(t))},appliesToListItem:t=>"numbered"==t.getAttribute("listType"),setAttributeOnDowncast(t,e,i){e?t.setAttribute("reversed","reversed",i):t.removeAttribute("reversed",i)},getAttributeOnUpcast:t=>t.hasAttribute("reversed")});t.startIndex&&e.push({attributeName:"listStart",defaultValue:1,addCommand(t){t.commands.add("listStart",new he(t))},appliesToListItem:t=>"numbered"==t.getAttribute("listType"),setAttributeOnDowncast(t,e,i){0==e||e>1?t.setAttribute("start",e,i):t.removeAttribute("start",i)},getAttributeOnUpcast(t){const e=t.getAttribute("start");return e>=0?e:1}});return e}(t.config.get("list.properties"));e.schema.extend("listItem",{allowAttributes:i.map((t=>t.attributeName))});for(const e of i)e.addCommand(t);var n;this.listenTo(t.commands.get("indentList"),"_executeCleanup",function(t,e){return(i,n)=>{const s=n[0],r=s.getAttribute("listIndent"),o=n.filter((t=>t.getAttribute("listIndent")===r));let l=null;s.previousSibling.getAttribute("listIndent")+1!==r&&(l=lt(s.previousSibling,{sameIndent:!0,direction:"backward",listIndent:r})),t.model.change((t=>{for(const i of o)for(const n of e)if(n.appliesToListItem(i)){const e=null==l?n.defaultValue:l.getAttribute(n.attributeName);t.setAttribute(n.attributeName,e,i)}}))}}(t,i)),this.listenTo(t.commands.get("outdentList"),"_executeCleanup",function(t,e){return(i,n)=>{if(!(n=n.reverse().filter((t=>t.is("element","listItem")))).length)return;const s=n[0].getAttribute("listIndent"),r=n[0].getAttribute("listType");let o=n[0].previousSibling;if(o.is("element","listItem"))for(;o.getAttribute("listIndent")!==s;)o=o.previousSibling;else o=null;o||(o=n[n.length-1].nextSibling),o&&o.is("element","listItem")&&o.getAttribute("listType")===r&&t.model.change((t=>{const i=n.filter((t=>t.getAttribute("listIndent")===s));for(const n of i)for(const i of e)if(i.appliesToListItem(n)){const e=i.attributeName,s=o.getAttribute(e);t.setAttribute(e,s,n)}}))}}(t,i)),this.listenTo(t.commands.get("bulletedList"),"_executeCleanup",ve(t)),this.listenTo(t.commands.get("numberedList"),"_executeCleanup",ve(t)),e.document.registerPostFixer(function(t,e){return i=>{let n=!1;const s=we(t.model.document.differ.getChanges()).filter((t=>"todo"!==t.getAttribute("listType")));if(!s.length)return n;let r=s[s.length-1].nextSibling;if((!r||!r.is("element","listItem"))&&(r=s[0].previousSibling,r)){const t=s[0].getAttribute("listIndent");for(;r.is("element","listItem")&&r.getAttribute("listIndent")!==t&&(r=r.previousSibling,r););}for(const t of e){const e=t.attributeName;for(const o of s)if(t.appliesToListItem(o))if(o.hasAttribute(e)){const s=o.previousSibling;ye(s,o,t.attributeName)&&(i.setAttribute(e,s.getAttribute(e),o),n=!0)}else be(r,o,t)?i.setAttribute(e,r.getAttribute(e),o):i.setAttribute(e,t.defaultValue,o),n=!0;else i.removeAttribute(e,o)}return n}}(t,i)),t.conversion.for("upcast").add((n=i,t=>{t.on("element:li",((t,e,i)=>{if(!e.modelRange)return;const s=e.viewItem.parent,r=e.modelRange.start.nodeAfter||e.modelRange.end.nodeBefore;for(const t of n)if(t.appliesToListItem(r)){const e=t.getAttributeOnUpcast(s);i.writer.setAttribute(t.attributeName,e,r)}}),{priority:"low"})})),t.conversion.for("downcast").add(function(t){return i=>{for(const n of t)i.on(`attribute:${n.attributeName}:listItem`,((t,i,s)=>{const r=s.writer,o=i.item,l=lt(o.previousSibling,{sameIndent:!0,listIndent:o.getAttribute("listIndent"),direction:"backward"}),a=s.mapper.toViewElement(o);e(o,l)||r.breakContainer(r.createPositionBefore(a)),n.setAttributeOnDowncast(r,i.attributeNewValue,a.parent)}),{priority:"low"})};function e(t,e){return e&&t.getAttribute("listType")===e.getAttribute("listType")&&t.getAttribute("listIndent")===e.getAttribute("listIndent")&&t.getAttribute("listStyle")===e.getAttribute("listStyle")&&t.getAttribute("listReversed")===e.getAttribute("listReversed")&&t.getAttribute("listStart")===e.getAttribute("listStart")}}(i)),this._mergeListAttributesWhileMergingLists(i)}afterInit(){const t=this.editor;t.commands.get("todoList")&&t.model.document.registerPostFixer(function(t){return e=>{const i=we(t.model.document.differ.getChanges()).filter((t=>"todo"===t.getAttribute("listType")&&(t.hasAttribute("listStyle")||t.hasAttribute("listReversed")||t.hasAttribute("listStart"))));if(!i.length)return!1;for(const t of i)e.removeAttribute("listStyle",t),e.removeAttribute("listReversed",t),e.removeAttribute("listStart",t);return!0}}(t))}_mergeListAttributesWhileMergingLists(t){const e=this.editor.model;let i;this.listenTo(e,"deleteContent",((t,[e])=>{const n=e.getFirstPosition(),s=e.getLastPosition();if(n.parent===s.parent)return;if(!n.parent.is("element","listItem"))return;const r=s.parent.nextSibling;if(!r||!r.is("element","listItem"))return;const o=lt(n.parent,{sameIndent:!0,listIndent:r.getAttribute("listIndent")});o&&o.getAttribute("listType")===r.getAttribute("listType")&&(i=o)}),{priority:"high"}),this.listenTo(e,"deleteContent",(()=>{i&&(e.change((e=>{const n=lt(i.nextSibling,{sameIndent:!0,listIndent:i.getAttribute("listIndent"),direction:"forward"});if(!n)return void(i=null);const s=[n,...dt(e.createPositionAt(n,0),"forward")];for(const n of s)for(const s of t)if(s.appliesToListItem(n)){const t=s.attributeName,r=i.getAttribute(t);e.setAttribute(t,r,n)}})),i=null)}),{priority:"low"})}}function be(t,e,i){if(!t)return!1;const n=t.getAttribute(i.attributeName);return!!n&&(n!=i.defaultValue&&t.getAttribute("listType")===e.getAttribute("listType"))}function ye(t,e,i){if(!t||!t.is("element","listItem"))return!1;if(e.getAttribute("listType")!==t.getAttribute("listType"))return!1;const n=t.getAttribute("listIndent");if(n<1||n!==e.getAttribute("listIndent"))return!1;const s=t.getAttribute(i);return!(!s||s===e.getAttribute(i))}function ve(t){return(e,i)=>{i=i.filter((t=>t.is("element","listItem"))),t.model.change((t=>{for(const e of i)t.removeAttribute("listStyle",e)}))}}function we(t){const e=[];for(const i of t){const t=Ae(i);t&&t.is("element","listItem")&&e.push(t)}return e}function Ae(t){return"attribute"===t.type?t.range.start.nodeAfter:"insert"===t.type?t.position.nodeAfter:null}class Ie extends t.Plugin{static get requires(){return[ge,jt]}static get pluginName(){return"ListProperties"}}const ke="todoListChecked";class Te extends t.Command{constructor(t){super(t),this._selectedElements=[],this.on("execute",(()=>{this.refresh()}),{priority:"highest"})}refresh(){this._selectedElements=this._getSelectedItems(),this.value=this._selectedElements.every((t=>!!t.getAttribute(ke))),this.isEnabled=!!this._selectedElements.length}_getSelectedItems(){const t=this.editor.model,e=t.schema,i=t.document.selection.getFirstRange(),n=i.start.parent,s=[];e.checkAttribute(n,ke)&&s.push(n);for(const t of i.getItems())e.checkAttribute(t,ke)&&!s.includes(t)&&s.push(t);return s}execute(t={}){this.editor.model.change((e=>{for(const i of this._selectedElements){(void 0===t.forceValue?!this.value:t.forceValue)?e.setAttribute(ke,!0,i):e.removeAttribute(ke,i)}}))}}const xe=(t,e,i)=>{const n=e.modelCursor,s=n.parent,r=e.viewItem;if("checkbox"!=r.getAttribute("type")||"listItem"!=s.name||!n.isAtStart)return;if(!i.consumable.consume(r,{name:!0}))return;const o=i.writer;o.setAttribute("listType","todo",s),e.viewItem.hasAttribute("checked")&&o.setAttribute("todoListChecked",!0,s),e.modelRange=o.createRange(n)};function _e(t){return(e,i)=>{const n=i.modelPosition,s=n.parent;if(!s.is("element","listItem")||"todo"!=s.getAttribute("listType"))return;const r=Ce(i.mapper.toViewElement(s),t);r&&(i.viewPosition=i.mapper.findPositionIn(r,n.offset))}}function Se(t,e,i,n){return e.createUIElement("label",{class:"todo-list__label",contenteditable:!1},(function(e){const s=(0,r.createElement)(document,"input",{type:"checkbox",tabindex:"-1"});i&&s.setAttribute("checked","checked"),s.addEventListener("change",(()=>n(t)));const o=this.toDomElement(e);return o.appendChild(s),o}))}function Ce(t,e){const i=e.createRangeIn(t);for(const t of i)if(t.item.is("containerElement","span")&&t.item.hasClass("todo-list__label__description"))return t.item}const Le=(0,r.parseKeystroke)("Ctrl+Enter");class Ee extends t.Plugin{static get pluginName(){return"TodoListEditing"}static get requires(){return[ce]}init(){const t=this.editor,{editing:e,data:i,model:n}=t;n.schema.extend("listItem",{allowAttributes:["todoListChecked"]}),n.schema.addAttributeCheck(((t,e)=>{const i=t.last;if("todoListChecked"==e&&"listItem"==i.name&&"todo"!=i.getAttribute("listType"))return!1})),t.commands.add("todoList",new qt(t,"todo"));const s=new Te(t);var o,l;t.commands.add("checkTodoList",s),t.commands.add("todoListCheck",s),i.downcastDispatcher.on("insert:listItem",function(t){return(e,i,n)=>{const s=n.consumable;if(!s.test(i.item,"insert")||!s.test(i.item,"attribute:listType")||!s.test(i.item,"attribute:listIndent"))return;if("todo"!=i.item.getAttribute("listType"))return;const r=i.item;s.consume(r,"insert"),s.consume(r,"attribute:listType"),s.consume(r,"attribute:listIndent"),s.consume(r,"attribute:todoListChecked");const o=n.writer,l=nt(r,n);o.addClass("todo-list",l.parent);const a=o.createContainerElement("label",{class:"todo-list__label"}),c=o.createEmptyElement("input",{type:"checkbox",disabled:"disabled"}),d=o.createContainerElement("span",{class:"todo-list__label__description"});r.getAttribute("todoListChecked")&&o.setAttribute("checked","checked",c),o.insert(o.createPositionAt(l,0),a),o.insert(o.createPositionAt(a,0),c),o.insert(o.createPositionAfter(c),d),st(r,l,n,t)}}(n),{priority:"high"}),i.upcastDispatcher.on("element:input",xe,{priority:"high"}),e.downcastDispatcher.on("insert:listItem",function(t,e){return(i,n,s)=>{const r=s.consumable;if(!r.test(n.item,"insert")||!r.test(n.item,"attribute:listType")||!r.test(n.item,"attribute:listIndent"))return;if("todo"!=n.item.getAttribute("listType"))return;const o=n.item;r.consume(o,"insert"),r.consume(o,"attribute:listType"),r.consume(o,"attribute:listIndent"),r.consume(o,"attribute:todoListChecked");const l=s.writer,a=nt(o,s),c=!!o.getAttribute("todoListChecked"),d=Se(o,l,c,e),u=l.createContainerElement("span",{class:"todo-list__label__description"});l.addClass("todo-list",a.parent),l.insert(l.createPositionAt(a,0),d),l.insert(l.createPositionAfter(d),u),st(o,a,s,t)}}(n,(t=>this._handleCheckmarkChange(t))),{priority:"high"}),e.downcastDispatcher.on("attribute:listType:listItem",(o=t=>this._handleCheckmarkChange(t),l=e.view,(t,e,i)=>{if(!i.consumable.consume(e.item,t.name))return;const n=i.mapper.toViewElement(e.item),s=i.writer,r=function(t,e){const i=e.createRangeIn(t);for(const t of i)if(t.item.is("uiElement","label"))return t.item}(n,l);if("todo"==e.attributeNewValue){const t=!!e.item.getAttribute("todoListChecked"),i=Se(e.item,s,t,o),r=s.createContainerElement("span",{class:"todo-list__label__description"}),l=s.createRangeIn(n),a=ct(n),c=ot(l.start),d=a?s.createPositionBefore(a):l.end,u=s.createRange(c,d);s.addClass("todo-list",n.parent),s.move(u,s.createPositionAt(r,0)),s.insert(s.createPositionAt(n,0),i),s.insert(s.createPositionAfter(i),r)}else if("todo"==e.attributeOldValue){const t=Ce(n,l);s.removeClass("todo-list",n.parent),s.remove(r),s.move(s.createRangeIn(t),s.createPositionBefore(t)),s.remove(t)}})),e.downcastDispatcher.on("attribute:todoListChecked:listItem",function(t){return(e,i,n)=>{if("todo"!=i.item.getAttribute("listType"))return;if(!n.consumable.consume(i.item,"attribute:todoListChecked"))return;const{mapper:s,writer:r}=n,o=!!i.item.getAttribute("todoListChecked"),l=s.toViewElement(i.item).getChild(0),a=Se(i.item,r,o,t);r.insert(r.createPositionAfter(l),a),r.remove(l)}}((t=>this._handleCheckmarkChange(t)))),e.mapper.on("modelToViewPosition",_e(e.view)),i.mapper.on("modelToViewPosition",_e(e.view)),this.listenTo(e.view.document,"arrowKey",function(t,e){return(i,n)=>{if("left"!=(0,r.getLocalizedArrowKeyCodeDirection)(n.keyCode,e.contentLanguageDirection))return;const s=t.schema,o=t.document.selection;if(!o.isCollapsed)return;const l=o.getFirstPosition(),a=l.parent;if("listItem"===a.name&&"todo"==a.getAttribute("listType")&&l.isAtStart){const e=s.getNearestSelectionRange(t.createPositionBefore(a),"backward");e&&t.change((t=>t.setSelection(e))),n.preventDefault(),n.stopPropagation(),i.stop()}}}(n,t.locale),{context:"li"}),this.listenTo(e.view.document,"keydown",((e,i)=>{(0,r.getCode)(i)===Le&&(t.execute("checkTodoList"),e.stop())}),{priority:"high"});const a=new Set;this.listenTo(n,"applyOperation",((t,e)=>{const i=e[0];if("rename"==i.type&&"listItem"==i.oldName){const t=i.position.nodeAfter;t.hasAttribute("todoListChecked")&&a.add(t)}else if("changeAttribute"==i.type&&"listType"==i.key&&"todo"===i.oldValue)for(const t of i.range.getItems())t.hasAttribute("todoListChecked")&&"todo"!==t.getAttribute("listType")&&a.add(t)})),n.document.registerPostFixer((t=>{let e=!1;for(const i of a)t.removeAttribute("todoListChecked",i),e=!0;return a.clear(),e}))}_handleCheckmarkChange(t){const e=this.editor,i=e.model,n=Array.from(i.document.selection.getRanges());i.change((i=>{i.setSelection(t,"end"),e.execute("checkTodoList"),i.setSelection(n)}))}}class Pe extends t.Plugin{static get pluginName(){return"TodoListUI"}init(){const t=this.editor.t;at(this.editor,"todoList",t("To-do List"),'<svg viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="m2.315 14.705 2.224-2.24a.689.689 0 0 1 .963 0 .664.664 0 0 1 0 .949L2.865 16.07a.682.682 0 0 1-.112.089.647.647 0 0 1-.852-.051L.688 14.886a.635.635 0 0 1 0-.903.647.647 0 0 1 .91 0l.717.722zm5.185.045a.75.75 0 0 1 .75-.75h9.5a.75.75 0 1 1 0 1.5h-9.5a.75.75 0 0 1-.75-.75zM2.329 5.745l2.21-2.226a.689.689 0 0 1 .963 0 .664.664 0 0 1 0 .95L2.865 7.125a.685.685 0 0 1-.496.196.644.644 0 0 1-.468-.187L.688 5.912a.635.635 0 0 1 0-.903.647.647 0 0 1 .91 0l.73.736zM7.5 5.75A.75.75 0 0 1 8.25 5h9.5a.75.75 0 1 1 0 1.5h-9.5a.75.75 0 0 1-.75-.75z"/></svg>')}}var Ve=i(528),ze={injectType:"singletonStyleTag",attributes:{"data-cke":!0},insert:"head",singleton:!0};Z()(Ve.Z,ze);Ve.Z.locals;class Be extends t.Plugin{static get requires(){return[Ee,Pe]}static get pluginName(){return"TodoList"}}class Ne extends t.Command{constructor(t){super(t),this.on("execute",(()=>{this.refresh()}),{priority:"highest"})}refresh(){const t=this._getSelectedItems();this.value=this._getValue(t),this.isEnabled=!!t.length}execute(t={}){this.editor.model.change((e=>{const i=this._getSelectedItems(),n=void 0===t.forceValue?!this._getValue(i):t.forceValue;for(const t of i)n?e.setAttribute("todoListChecked",!0,t):e.removeAttribute("todoListChecked",t)}))}_getValue(t){return t.every((t=>t.getAttribute("todoListChecked")))}_getSelectedItems(){const t=this.editor.model,e=t.schema,i=t.document.selection.getFirstRange(),n=i.start.parent,s=[];e.checkAttribute(n,"todoListChecked")&&s.push(...u(n));for(const t of i.getItems({shallow:!0}))e.checkAttribute(t,"todoListChecked")&&!s.includes(t)&&s.push(...u(t));return s}}class Me extends z.DomEventObserver{constructor(){super(...arguments),this.domEventType=["change"]}onDomEvent(t){if(t.target){const e=this.view.domConverter.mapDomToView(t.target);e&&e.is("element","input")&&"checkbox"==e.getAttribute("type")&&e.findAncestor({classes:"todo-list__label"})&&this.fire("todoCheckboxChange",t)}}}const De=(0,r.parseKeystroke)("Ctrl+Enter");class Re extends t.Plugin{static get pluginName(){return"TodoDocumentListEditing"}static get requires(){return[tt]}init(){const t=this.editor,e=t.model,i=t.editing,n=t.plugins.get(tt),s=t.config.get("list.multiBlock")?"paragraph":"listItem";t.commands.add("todoList",new L(t,"todo")),t.commands.add("checkTodoList",new Ne(t)),i.view.addObserver(Me),e.schema.extend("$listItem",{allowAttributes:"todoListChecked"}),e.schema.addAttributeCheck(((t,e)=>{const i=t.last;if("todoListChecked"==e)return!(!i.getAttribute("listItemId")||"todo"!=i.getAttribute("listType"))&&void 0})),t.conversion.for("upcast").add((t=>{t.on("element:input",((t,e,i)=>{const n=e.modelCursor,s=n.parent,r=e.viewItem;if(!i.consumable.test(r,{name:!0}))return;if("checkbox"!=r.getAttribute("type")||!n.isAtStart||!s.hasAttribute("listType"))return;i.consumable.consume(r,{name:!0});const o=i.writer;o.setAttribute("listType","todo",s),e.viewItem.hasAttribute("checked")&&o.setAttribute("todoListChecked",!0,s),e.modelRange=o.createRange(n)})),t.on("element:label",Oe({name:"label",classes:"todo-list__label"})),t.on("element:label",Oe({name:"label",classes:["todo-list__label","todo-list__label_without-description"]})),t.on("element:span",Oe({name:"span",classes:"todo-list__label__description"})),t.on("element:ul",function(t){const e=new z.Matcher(t);return(t,i,n)=>{const s=e.match(i.viewItem);if(!s)return;const r=s.match;r.name=!1,n.consumable.consume(i.viewItem,r)}}({name:"ul",classes:"todo-list"}))})),t.conversion.for("downcast").elementToElement({model:s,view:(t,{writer:e})=>{if(Fe(t,n.getListAttributeNames()))return e.createContainerElement("span",{class:"todo-list__label__description"})},converterPriority:"highest"}),n.registerDowncastStrategy({scope:"list",attributeName:"listType",setAttributeOnDowncast(t,e,i){"todo"==e?t.addClass("todo-list",i):t.removeClass("todo-list",i)}}),n.registerDowncastStrategy({scope:"itemMarker",attributeName:"todoListChecked",createElement(t,e,{dataPipeline:i}){if("todo"!=e.getAttribute("listType"))return null;const n=t.createEmptyElement("input",{type:"checkbox",...e.getAttribute("todoListChecked")?{checked:"checked"}:null,...i?{disabled:"disabled"}:{tabindex:"-1"}});return i?n:t.createContainerElement("span",{contenteditable:"false"},n)},canWrapElement:t=>Fe(t,n.getListAttributeNames()),createWrapperElement(t,e,{dataPipeline:i}){const s=["todo-list__label"];return Fe(e,n.getListAttributeNames())||s.push("todo-list__label_without-description"),t.createAttributeElement(i?"label":"span",{class:s.join(" ")})}}),i.mapper.registerViewToModelLength("input",(t=>"checkbox"==t.getAttribute("type")&&t.findAncestor({classes:"todo-list__label"})?0:i.mapper.toModelElement(t)?1:0)),n.on("checkElement",((t,{modelElement:e,viewElement:i})=>{const s=Fe(e,n.getListAttributeNames());i.hasClass("todo-list__label__description")!=s&&(t.return=!0,t.stop())})),n.on("checkElement",((e,{modelElement:i,viewElement:n})=>{const s="todo"==i.getAttribute("listType")&&h(i);let r=!1;const o=t.editing.view.createPositionBefore(n).getWalker({direction:"backward"});for(const{item:e}of o){if(e.is("element")&&t.editing.mapper.toModelElement(e))break;e.is("element","input")&&"checkbox"==e.getAttribute("type")&&(r=!0)}r!=s&&(e.return=!0,e.stop())})),n.on("postFixer",((t,{listNodes:e,writer:i})=>{for(const{node:n,previousNodeInList:s}of e){if(!s)continue;if(s.getAttribute("listItemId")!=n.getAttribute("listItemId"))continue;const e=s.hasAttribute("todoListChecked"),r=n.hasAttribute("todoListChecked");r&&!e?(i.removeAttribute("todoListChecked",n),t.return=!0):!r&&e&&(i.setAttribute("todoListChecked",!0,n),t.return=!0)}})),e.document.registerPostFixer((t=>{const i=e.document.differ.getChanges();let n=!1;for(const e of i)if("attribute"==e.type&&"listType"==e.attributeKey){const i=e.range.start.nodeAfter;"todo"==e.attributeOldValue&&i.hasAttribute("todoListChecked")&&(t.removeAttribute("todoListChecked",i),n=!0)}else if("insert"==e.type&&"$text"!=e.name)for(const{item:i}of t.createRangeOn(e.position.nodeAfter))i.is("element")&&"todo"!=i.getAttribute("listType")&&i.hasAttribute("todoListChecked")&&(t.removeAttribute("todoListChecked",i),n=!0);return n})),this.listenTo(i.view.document,"keydown",((e,i)=>{(0,r.getCode)(i)===De&&(t.execute("checkTodoList"),e.stop())}),{priority:"high"}),this.listenTo(i.view.document,"todoCheckboxChange",((t,e)=>{const n=e.target;if(!n||!n.is("element","input"))return;const s=i.view.createPositionAfter(n),r=i.mapper.toModelPosition(s).parent;r&&d(r)&&"todo"==r.getAttribute("listType")&&this._handleCheckmarkChange(r)})),this.listenTo(i.view.document,"arrowKey",function(t,e){return(i,n)=>{const s=(0,r.getLocalizedArrowKeyCodeDirection)(n.keyCode,e.contentLanguageDirection),o=t.schema,l=t.document.selection;if(!l.isCollapsed)return;const a=l.getFirstPosition(),c=a.parent;if("right"==s&&a.isAtEnd){const e=o.getNearestSelectionRange(t.createPositionAfter(c),"forward");if(!e)return;const s=e.start.parent;s&&d(s)&&"todo"==s.getAttribute("listType")&&(t.change((t=>t.setSelection(e))),n.preventDefault(),n.stopPropagation(),i.stop())}else if("left"==s&&a.isAtStart&&d(c)&&"todo"==c.getAttribute("listType")){const e=o.getNearestSelectionRange(t.createPositionBefore(c),"backward");if(!e)return;t.change((t=>t.setSelection(e))),n.preventDefault(),n.stopPropagation(),i.stop()}}}(e,t.locale),{context:"$text"}),this.listenTo(i.mapper,"viewToModelPosition",((t,i)=>{const n=i.viewPosition.parent,s=n.is("attributeElement","li")&&0==i.viewPosition.offset,r=He(n)&&i.viewPosition.offset<=1,o=n.is("element","span")&&"false"==n.getAttribute("contenteditable")&&He(n.parent);if(!s&&!r&&!o)return;const l=i.modelPosition.nodeAfter;l&&"todo"==l.getAttribute("listType")&&(i.modelPosition=e.createPositionAt(l,0))}),{priority:"low"})}_handleCheckmarkChange(t){const e=this.editor,i=e.model,n=Array.from(i.document.selection.getRanges());i.change((i=>{i.setSelection(t,"end"),e.execute("checkTodoList"),i.setSelection(n)}))}}function Oe(t){const e=new z.Matcher(t);return(t,i,n)=>{const s=e.match(i.viewItem);s&&n.consumable.consume(i.viewItem,s.match)&&Object.assign(i,n.convertChildren(i.viewItem,i.modelCursor))}}function Fe(t,e){return(t.is("element","paragraph")||t.is("element","listItem"))&&"todo"==t.getAttribute("listType")&&h(t)&&function(t,e){for(const i of t.getAttributeKeys())if(!i.startsWith("selection:")&&!e.includes(i))return!1;return!0}(t,e)}function He(t){return!!t&&t.is("attributeElement")&&t.hasClass("todo-list__label")}class je extends t.Plugin{static get requires(){return[Re,Pe]}static get pluginName(){return"TodoDocumentList"}}})(),(window.CKEditor5=window.CKEditor5||{}).list=n})();;
/*!
 * @license Copyright (c) 2003-2023, CKSource Holding sp. z o.o. All rights reserved.
 * For licensing, see LICENSE.md.
 */(()=>{var e={945:(e,t,n)=>{e.exports=n(79)("./src/clipboard.js")},704:(e,t,n)=>{e.exports=n(79)("./src/core.js")},492:(e,t,n)=>{e.exports=n(79)("./src/engine.js")},79:e=>{"use strict";e.exports=CKEditor5.dll}},t={};function n(r){var s=t[r];if(void 0!==s)return s.exports;var i=t[r]={exports:{}};return e[r](i,i.exports,n),i.exports}n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var r={};(()=>{"use strict";n.r(r),n.d(r,{MSWordNormalizer:()=>d,PasteFromOffice:()=>C,parseHtml:()=>x});var e=n(704),t=n(945),s=n(492);function i(e,t){if(!e.childCount)return;const n=new s.UpcastWriter(e.document),r=function(e,t){const n=t.createRangeIn(e),r=new s.Matcher({name:/^p|h\d+$/,styles:{"mso-list":/.*/}}),i=[];for(const e of n)if("elementStart"===e.type&&r.match(e.item)){const t=l(e.item);i.push({element:e.item,id:t.id,order:t.order,indent:t.indent})}return i}(e,n);if(!r.length)return;let i=null,a=1;r.forEach(((e,l)=>{const u=function(e,t){if(!e)return!0;if(e.id!==t.id)return t.indent-e.indent!=1;const n=t.element.previousSibling;if(!n)return!0;return r=n,!(r.is("element","ol")||r.is("element","ul"));var r}(r[l-1],e),m=u?null:r[l-1],f=(p=e,(d=m)?p.indent-d.indent:p.indent-1);var d,p;if(u&&(i=null,a=1),!i||0!==f){const r=function(e,t){const n=new RegExp(`@list l${e.id}:level${e.indent}\\s*({[^}]*)`,"gi"),r=/mso-level-number-format:([^;]{0,100});/gi,s=/mso-level-start-at:\s{0,100}([0-9]{0,10})\s{0,100};/gi,i=n.exec(t);let c="decimal",l="ol",a=null;if(i&&i[1]){const t=r.exec(i[1]);if(t&&t[1]&&(c=t[1].trim(),l="bullet"!==c&&"image"!==c?"ol":"ul"),"bullet"===c){const t=function(e){const t=function(e){if(e.getChild(0).is("$text"))return null;for(const t of e.getChildren()){if(!t.is("element","span"))continue;const e=t.getChild(0);if(e)return e.is("$text")?e:e.getChild(0)}return null}(e);if(!t)return null;const n=t._data;if("o"===n)return"circle";if("·"===n)return"disc";if("§"===n)return"square";return null}(e.element);t&&(c=t)}else{const e=s.exec(i[1]);e&&e[1]&&(a=parseInt(e[1]))}}return{type:l,startIndex:a,style:o(c)}}(e,t);if(i){if(e.indent>a){const e=i.getChild(i.childCount-1),t=e.getChild(e.childCount-1);i=c(r,t,n),a+=1}else if(e.indent<a){const t=a-e.indent;i=function(e,t){const n=e.getAncestors({parentFirst:!0});let r=null,s=0;for(const e of n)if((e.is("element","ul")||e.is("element","ol"))&&s++,s===t){r=e;break}return r}(i,t),a=e.indent}}else i=c(r,e.element,n);e.indent<=a&&(i.is("element",r.type)||(i=n.rename(r.type,i)))}const g=function(e,t){return function(e,t){const n=new s.Matcher({name:"span",styles:{"mso-list":"Ignore"}}),r=t.createRangeIn(e);for(const e of r)"elementStart"===e.type&&n.match(e.item)&&t.remove(e.item)}(e,t),t.removeStyle("text-indent",e),t.rename("li",e)}(e.element,n);n.appendChild(g,i)}))}function o(e){if(e.startsWith("arabic-leading-zero"))return"decimal-leading-zero";switch(e){case"alpha-upper":return"upper-alpha";case"alpha-lower":return"lower-alpha";case"roman-upper":return"upper-roman";case"roman-lower":return"lower-roman";case"circle":case"disc":case"square":return e;default:return null}}function c(e,t,n){const r=t.parent,s=n.createElement(e.type),i=r.getChildIndex(t)+1;return n.insertChild(i,s,r),e.style&&n.setStyle("list-style-type",e.style,s),e.startIndex&&e.startIndex>1&&n.setAttribute("start",e.startIndex,s),s}function l(e){const t={},n=e.getStyle("mso-list");if(n){const e=n.match(/(^|\s{1,100})l(\d+)/i),r=n.match(/\s{0,100}lfo(\d+)/i),s=n.match(/\s{0,100}level(\d+)/i);e&&r&&s&&(t.id=e[2],t.order=r[1],t.indent=parseInt(s[1]))}return t}function a(e,t){if(!e.childCount)return;const n=new s.UpcastWriter(e.document),r=function(e,t){const n=t.createRangeIn(e),r=new s.Matcher({name:/v:(.+)/}),i=[];for(const e of n){if("elementStart"!=e.type)continue;const t=e.item,n=t.previousSibling,s=n&&n.is("element")?n.name:null;r.match(t)&&t.getAttribute("o:gfxdata")&&"v:shapetype"!==s&&i.push(e.item.getAttribute("id"))}return i}(e,n);!function(e,t,n){const r=n.createRangeIn(t),i=new s.Matcher({name:"img"}),o=[];for(const t of r)if(t.item.is("element")&&i.match(t.item)){const n=t.item,r=n.getAttribute("v:shapes")?n.getAttribute("v:shapes").split(" "):[];r.length&&r.every((t=>e.indexOf(t)>-1))?o.push(n):n.getAttribute("src")||o.push(n)}for(const e of o)n.remove(e)}(r,e,n),function(e,t,n){const r=n.createRangeIn(t),s=[];for(const t of r)if("elementStart"==t.type&&t.item.is("element","v:shape")){const n=t.item.getAttribute("id");if(e.includes(n))continue;i(t.item.parent.getChildren(),n)||s.push(t.item)}for(const e of s){const t={src:o(e)};e.hasAttribute("alt")&&(t.alt=e.getAttribute("alt"));const r=n.createElement("img",t);n.insertChild(e.index+1,r,e.parent)}function i(e,t){for(const n of e)if(n.is("element")){if("img"==n.name&&n.getAttribute("v:shapes")==t)return!0;if(i(n.getChildren(),t))return!0}return!1}function o(e){for(const t of e.getChildren())if(t.is("element")&&t.getAttribute("src"))return t.getAttribute("src")}}(r,e,n),function(e,t){const n=t.createRangeIn(e),r=new s.Matcher({name:/v:(.+)/}),i=[];for(const e of n)"elementStart"==e.type&&r.match(e.item)&&i.push(e.item);for(const e of i)t.remove(e)}(e,n);const i=function(e,t){const n=t.createRangeIn(e),r=new s.Matcher({name:"img"}),i=[];for(const e of n)e.item.is("element")&&r.match(e.item)&&e.item.getAttribute("src").startsWith("file://")&&i.push(e.item);return i}(e,n);i.length&&function(e,t,n){if(e.length===t.length)for(let r=0;r<e.length;r++){const s=`data:${t[r].type};base64,${u(t[r].hex)}`;n.setAttribute("src",s,e[r])}}(i,function(e){if(!e)return[];const t=/{\\pict[\s\S]+?\\bliptag-?\d+(\\blipupi-?\d+)?({\\\*\\blipuid\s?[\da-fA-F]+)?[\s}]*?/,n=new RegExp("(?:("+t.source+"))([\\da-fA-F\\s]+)\\}","g"),r=e.match(n),s=[];if(r)for(const e of r){let n=!1;e.includes("\\pngblip")?n="image/png":e.includes("\\jpegblip")&&(n="image/jpeg"),n&&s.push({hex:e.replace(t,"").replace(/[^\da-fA-F]/g,""),type:n})}return s}(t),n)}function u(e){return btoa(e.match(/\w{2}/g).map((e=>String.fromCharCode(parseInt(e,16)))).join(""))}const m=/<meta\s*name="?generator"?\s*content="?microsoft\s*word\s*\d+"?\/?>/i,f=/xmlns:o="urn:schemas-microsoft-com/i;class d{constructor(e){this.document=e}isActive(e){return m.test(e)||f.test(e)}execute(e){const{body:t,stylesString:n}=e._parsedData;i(t,n),a(t,e.dataTransfer.getData("text/rtf")),function(e){const t=[],n=new s.UpcastWriter(e.document);for(const{item:r}of n.createRangeIn(e))if(r.is("element")){for(const e of r.getClassNames())/\bmso/gi.exec(e)&&n.removeClass(e,r);for(const e of r.getStyleNames())/\bmso/gi.exec(e)&&n.removeStyle(e,r);r.is("element","w:sdt")&&t.push(r)}for(const e of t){const t=e.parent,r=t.getChildIndex(e);n.insertChild(r,e.getChildren(),t),n.remove(e)}}(t),e.content=t}}function p(e,t,n,{blockElements:r,inlineObjectElements:s}){let i=n.createPositionAt(e,"forward"==t?"after":"before");return i=i.getLastMatchingPosition((({item:e})=>e.is("element")&&!r.includes(e.name)&&!s.includes(e.name)),{direction:t}),"forward"==t?i.nodeAfter:i.nodeBefore}function g(e,t){return!!e&&e.is("element")&&t.includes(e.name)}const h=/id=("|')docs-internal-guid-[-0-9a-f]+("|')/i;class b{constructor(e){this.document=e}isActive(e){return h.test(e)}execute(e){const t=new s.UpcastWriter(this.document),{body:n}=e._parsedData;!function(e,t){for(const n of e.getChildren())if(n.is("element","b")&&"normal"===n.getStyle("font-weight")){const r=e.getChildIndex(n);t.remove(n),t.insertChild(r,n.getChildren(),e)}}(n,t),function(e,t){for(const n of t.createRangeIn(e)){const e=n.item;if(e.is("element","li")){const n=e.getChild(0);n&&n.is("element","p")&&t.unwrapElement(n)}}}(n,t),function(e,t){const n=new s.ViewDocument(t.document.stylesProcessor),r=new s.DomConverter(n,{renderingMode:"data"}),i=r.blockElements,o=r.inlineObjectElements,c=[];for(const n of t.createRangeIn(e)){const e=n.item;if(e.is("element","br")){const n=p(e,"forward",t,{blockElements:i,inlineObjectElements:o}),r=p(e,"backward",t,{blockElements:i,inlineObjectElements:o}),s=g(n,i);(g(r,i)||s)&&c.push(e)}}for(const e of c)e.hasClass("Apple-interchange-newline")?t.remove(e):t.replace(e,t.createElement("p"))}(n,t),e.content=n}}const y=/<google-sheets-html-origin/i;class v{constructor(e){this.document=e}isActive(e){return y.test(e)}execute(e){const t=new s.UpcastWriter(this.document),{body:n}=e._parsedData;!function(e,t){for(const n of e.getChildren())if(n.is("element","google-sheets-html-origin")){const r=e.getChildIndex(n);t.remove(n),t.insertChild(r,n.getChildren(),e)}}(n,t),function(e,t){for(const n of e.getChildren())n.is("element","table")&&n.hasAttribute("xmlns")&&t.removeAttribute("xmlns",n)}(n,t),function(e,t){for(const n of e.getChildren())n.is("element","table")&&"0px"===n.getStyle("width")&&t.removeStyle("width",n)}(n,t),function(e,t){for(const n of Array.from(e.getChildren()))n.is("element","style")&&t.remove(n)}(n,t),e.content=n}}function w(e){return e.replace(/<span(?: class="Apple-converted-space"|)>(\s+)<\/span>/g,((e,t)=>1===t.length?" ":Array(t.length+1).join("  ").substr(0,t.length)))}function x(e,t){const n=new DOMParser,r=function(e){return w(w(e)).replace(/(<span\s+style=['"]mso-spacerun:yes['"]>[^\S\r\n]*?)[\r\n]+([^\S\r\n]*<\/span>)/g,"$1$2").replace(/<span\s+style=['"]mso-spacerun:yes['"]><\/span>/g,"").replace(/(<span\s+style=['"]letter-spacing:[^'"]+?['"]>)[\r\n]+(<\/span>)/g,"$1 $2").replace(/ <\//g," </").replace(/ <o:p><\/o:p>/g," <o:p></o:p>").replace(/<o:p>(&nbsp;|\u00A0)<\/o:p>/g,"").replace(/>([^\S\r\n]*[\r\n]\s*)</g,"><")}(function(e){const t="</body>",n="</html>",r=e.indexOf(t);if(r<0)return e;const s=e.indexOf(n,r+t.length);return e.substring(0,r+t.length)+(s>=0?e.substring(s):"")}(e=(e=e.replace(/<!--\[if gte vml 1]>/g,"")).replace(/<o:SmartTagType(?:\s+[^\s>=]+(?:="[^"]*")?)*\s*\/?>/gi,""))),i=n.parseFromString(r,"text/html");!function(e){e.querySelectorAll("span[style*=spacerun]").forEach((e=>{const t=e,n=t.innerText.length||0;t.innerText=Array(n+1).join("  ").substr(0,n)}))}(i);const o=i.body.innerHTML,c=function(e,t){const n=new s.ViewDocument(t),r=new s.DomConverter(n,{renderingMode:"data"}),i=e.createDocumentFragment(),o=e.body.childNodes;for(;o.length>0;)i.appendChild(o[0]);return r.domToView(i,{skipComments:!0})}(i,t),l=function(e){const t=[],n=[],r=Array.from(e.getElementsByTagName("style"));for(const e of r)e.sheet&&e.sheet.cssRules&&e.sheet.cssRules.length&&(t.push(e.sheet),n.push(e.innerHTML));return{styles:t,stylesString:n.join(" ")}}(i);return{body:c,bodyString:o,styles:l.styles,stylesString:l.stylesString}}class C extends e.Plugin{static get pluginName(){return"PasteFromOffice"}static get requires(){return[t.ClipboardPipeline]}init(){const e=this.editor,t=e.plugins.get("ClipboardPipeline"),n=e.editing.view.document,r=[];r.push(new d(n)),r.push(new b(n)),r.push(new v(n)),t.on("inputTransformation",((t,s)=>{if(s._isTransformedWithPasteFromOffice)return;if(e.model.document.selection.getFirstPosition().parent.is("element","codeBlock"))return;const i=s.dataTransfer.getData("text/html"),o=r.find((e=>e.isActive(i)));o&&(s._parsedData||(s._parsedData=x(i,n.stylesProcessor)),o.execute(s),s._isTransformedWithPasteFromOffice=!0)}),{priority:"high"})}}})(),(window.CKEditor5=window.CKEditor5||{}).pasteFromOffice=r})();;
!function(e){const t=e.en=e.en||{};t.dictionary=Object.assign(t.dictionary||{},{Source:"Source"})}(window.CKEDITOR_TRANSLATIONS||(window.CKEDITOR_TRANSLATIONS={})),
/*!
 * @license Copyright (c) 2003-2023, CKSource Holding sp. z o.o. All rights reserved.
 * For licensing, see LICENSE.md.
 */(()=>{var e={40:(e,t,i)=>{"use strict";i.d(t,{Z:()=>r});var n=i(609),o=i.n(n)()((function(e){return e[1]}));o.push([e.id,'.ck-source-editing-area{overflow:hidden;position:relative}.ck-source-editing-area textarea,.ck-source-editing-area:after{border:1px solid transparent;font-family:monospace;font-size:var(--ck-font-size-normal);line-height:var(--ck-line-height-base);margin:0;padding:var(--ck-spacing-large);white-space:pre-wrap}.ck-source-editing-area:after{content:attr(data-value) " ";display:block;visibility:hidden}.ck-source-editing-area textarea{border-color:var(--ck-color-base-border);border-radius:0;box-sizing:border-box;height:100%;outline:none;overflow:hidden;position:absolute;resize:none;width:100%}.ck-rounded-corners .ck-source-editing-area textarea,.ck-source-editing-area textarea.ck-rounded-corners{border-radius:var(--ck-border-radius);border-top-left-radius:0;border-top-right-radius:0}.ck-source-editing-area textarea:not([readonly]):focus{border:var(--ck-focus-ring);box-shadow:var(--ck-inner-shadow),0 0;outline:none}',""]);const r=o},609:e=>{"use strict";e.exports=function(e){var t=[];return t.toString=function(){return this.map((function(t){var i=e(t);return t[2]?"@media ".concat(t[2]," {").concat(i,"}"):i})).join("")},t.i=function(e,i,n){"string"==typeof e&&(e=[[null,e,""]]);var o={};if(n)for(var r=0;r<this.length;r++){var a=this[r][0];null!=a&&(o[a]=!0)}for(var s=0;s<e.length;s++){var d=[].concat(e[s]);n&&o[d[0]]||(i&&(d[2]?d[2]="".concat(i," and ").concat(d[2]):d[2]=i),t.push(d))}},t}},62:(e,t,i)=>{"use strict";var n,o=function(){return void 0===n&&(n=Boolean(window&&document&&document.all&&!window.atob)),n},r=function(){var e={};return function(t){if(void 0===e[t]){var i=document.querySelector(t);if(window.HTMLIFrameElement&&i instanceof window.HTMLIFrameElement)try{i=i.contentDocument.head}catch(e){i=null}e[t]=i}return e[t]}}(),a=[];function s(e){for(var t=-1,i=0;i<a.length;i++)if(a[i].identifier===e){t=i;break}return t}function d(e,t){for(var i={},n=[],o=0;o<e.length;o++){var r=e[o],d=t.base?r[0]+t.base:r[0],c=i[d]||0,l="".concat(d," ").concat(c);i[d]=c+1;var u=s(l),h={css:r[1],media:r[2],sourceMap:r[3]};-1!==u?(a[u].references++,a[u].updater(h)):a.push({identifier:l,updater:p(h,t),references:1}),n.push(l)}return n}function c(e){var t=document.createElement("style"),n=e.attributes||{};if(void 0===n.nonce){var o=i.nc;o&&(n.nonce=o)}if(Object.keys(n).forEach((function(e){t.setAttribute(e,n[e])})),"function"==typeof e.insert)e.insert(t);else{var a=r(e.insert||"head");if(!a)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");a.appendChild(t)}return t}var l,u=(l=[],function(e,t){return l[e]=t,l.filter(Boolean).join("\n")});function h(e,t,i,n){var o=i?"":n.media?"@media ".concat(n.media," {").concat(n.css,"}"):n.css;if(e.styleSheet)e.styleSheet.cssText=u(t,o);else{var r=document.createTextNode(o),a=e.childNodes;a[t]&&e.removeChild(a[t]),a.length?e.insertBefore(r,a[t]):e.appendChild(r)}}function m(e,t,i){var n=i.css,o=i.media,r=i.sourceMap;if(o?e.setAttribute("media",o):e.removeAttribute("media"),r&&"undefined"!=typeof btoa&&(n+="\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(encodeURIComponent(JSON.stringify(r))))," */")),e.styleSheet)e.styleSheet.cssText=n;else{for(;e.firstChild;)e.removeChild(e.firstChild);e.appendChild(document.createTextNode(n))}}var f=null,g=0;function p(e,t){var i,n,o;if(t.singleton){var r=g++;i=f||(f=c(t)),n=h.bind(null,i,r,!1),o=h.bind(null,i,r,!0)}else i=c(t),n=m.bind(null,i,t),o=function(){!function(e){if(null===e.parentNode)return!1;e.parentNode.removeChild(e)}(i)};return n(e),function(t){if(t){if(t.css===e.css&&t.media===e.media&&t.sourceMap===e.sourceMap)return;n(e=t)}else o()}}e.exports=function(e,t){(t=t||{}).singleton||"boolean"==typeof t.singleton||(t.singleton=o());var i=d(e=e||[],t);return function(e){if(e=e||[],"[object Array]"===Object.prototype.toString.call(e)){for(var n=0;n<i.length;n++){var o=s(i[n]);a[o].references--}for(var r=d(e,t),c=0;c<i.length;c++){var l=s(i[c]);0===a[l].references&&(a[l].updater(),a.splice(l,1))}i=r}}}},704:(e,t,i)=>{e.exports=i(79)("./src/core.js")},273:(e,t,i)=>{e.exports=i(79)("./src/ui.js")},209:(e,t,i)=>{e.exports=i(79)("./src/utils.js")},79:e=>{"use strict";e.exports=CKEditor5.dll}},t={};function i(n){var o=t[n];if(void 0!==o)return o.exports;var r=t[n]={id:n,exports:{}};return e[n](r,r.exports,i),r.exports}i.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return i.d(t,{a:t}),t},i.d=(e,t)=>{for(var n in t)i.o(t,n)&&!i.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},i.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),i.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},i.nc=void 0;var n={};(()=>{"use strict";i.r(n),i.d(n,{SourceEditing:()=>h});var e=i(704),t=i(273),o=i(209);function r(e){const t=[{name:"address",isVoid:!1},{name:"article",isVoid:!1},{name:"aside",isVoid:!1},{name:"blockquote",isVoid:!1},{name:"details",isVoid:!1},{name:"dialog",isVoid:!1},{name:"dd",isVoid:!1},{name:"div",isVoid:!1},{name:"dl",isVoid:!1},{name:"dt",isVoid:!1},{name:"fieldset",isVoid:!1},{name:"figcaption",isVoid:!1},{name:"figure",isVoid:!1},{name:"footer",isVoid:!1},{name:"form",isVoid:!1},{name:"h1",isVoid:!1},{name:"h2",isVoid:!1},{name:"h3",isVoid:!1},{name:"h4",isVoid:!1},{name:"h5",isVoid:!1},{name:"h6",isVoid:!1},{name:"header",isVoid:!1},{name:"hgroup",isVoid:!1},{name:"hr",isVoid:!0},{name:"li",isVoid:!1},{name:"main",isVoid:!1},{name:"nav",isVoid:!1},{name:"ol",isVoid:!1},{name:"p",isVoid:!1},{name:"section",isVoid:!1},{name:"table",isVoid:!1},{name:"tbody",isVoid:!1},{name:"td",isVoid:!1},{name:"th",isVoid:!1},{name:"thead",isVoid:!1},{name:"tr",isVoid:!1},{name:"ul",isVoid:!1}],i=t.map((e=>e.name)).join("|"),n=e.replace(new RegExp(`</?(${i})( .*?)?>`,"g"),"\n$&\n").replace(/<br[^>]*>/g,"$&\n").split("\n");let o=0;return n.filter((e=>e.length)).map((e=>function(e,t){return t.some((t=>!t.isVoid&&!!new RegExp(`<${t.name}( .*?)?>`).test(e)))}(e,t)?a(e,o++):function(e,t){return t.some((t=>new RegExp(`</${t.name}>`).test(e)))}(e,t)?a(e,--o):a(e,o))).join("\n")}function a(e,t,i="    "){return`${i.repeat(Math.max(0,t))}${e}`}var s=i(62),d=i.n(s),c=i(40),l={injectType:"singletonStyleTag",attributes:{"data-cke":!0},insert:"head",singleton:!0};d()(c.Z,l);c.Z.locals;const u="SourceEditingMode";class h extends e.Plugin{static get pluginName(){return"SourceEditing"}static get requires(){return[e.PendingActions]}constructor(e){super(e),this.set("isSourceEditingMode",!1),this._elementReplacer=new o.ElementReplacer,this._replacedRoots=new Map,this._dataFromRoots=new Map}init(){const i=this.editor,n=i.t;i.ui.componentFactory.add("sourceEditing",(o=>{const r=new t.ButtonView(o);return r.set({label:n("Source"),icon:'<svg viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="m12.5 0 5 4.5v15.003h-16V0h11zM3 1.5v3.25l-1.497 1-.003 8 1.5 1v3.254L7.685 18l-.001 1.504H17.5V8.002L16 9.428l-.004-4.22-4.222-3.692L3 1.5z"/><path d="M4.06 6.64a.75.75 0 0 1 .958 1.15l-.085.07L2.29 9.75l2.646 1.89c.302.************.951l-.058.095a.75.75 0 0 1-.951.232l-.095-.058-3.5-2.5V9.14l3.496-2.5zm4.194 6.22a.75.75 0 0 1-.958-1.149l.085-.07 2.643-1.89-2.646-1.89a.75.75 0 0 1-.232-.952l.058-.095a.75.75 0 0 1 .95-.232l.096.058 3.5 2.5v1.22l-3.496 2.5zm7.644-.836 2.122 2.122-5.825 5.809-2.125-.005.003-2.116zm2.539-1.847 1.414 1.414a.5.5 0 0 1 0 .707l-1.06 1.06-2.122-2.12 1.061-1.061a.5.5 0 0 1 .707 0z"/></svg>',tooltip:!0,withText:!0,class:"ck-source-editing-button"}),r.bind("isOn").to(this,"isSourceEditingMode"),r.bind("isEnabled").to(this,"isEnabled",i,"isReadOnly",i.plugins.get(e.PendingActions),"hasAny",((e,t,i)=>!!e&&(!t&&!i))),this.listenTo(r,"execute",(()=>{this.isSourceEditingMode=!this.isSourceEditingMode})),r})),this._isAllowedToHandleSourceEditingMode()&&(this.on("change:isSourceEditingMode",((e,t,i)=>{i?(this._showSourceEditing(),this._disableCommands()):(this._hideSourceEditing(),this._enableCommands())})),this.on("change:isEnabled",((e,t,i)=>this._handleReadOnlyMode(!i))),this.listenTo(i,"change:isReadOnly",((e,t,i)=>this._handleReadOnlyMode(i)))),i.data.on("get",(()=>{this.isSourceEditingMode&&this.updateEditorData()}),{priority:"high"})}afterInit(){const e=this.editor;["RealTimeCollaborativeEditing","CommentsEditing","TrackChangesEditing","RevisionHistory"].some((t=>e.plugins.has(t)))&&console.warn("You initialized the editor with the source editing feature and at least one of the collaboration features. Please be advised that the source editing feature may not work, and be careful when editing document source that contains markers created by the collaboration features."),e.plugins.has("RestrictedEditingModeEditing")&&console.warn("You initialized the editor with the source editing feature and restricted editing feature. Please be advised that the source editing feature may not work, and be careful when editing document source that contains markers created by the restricted editing feature.")}updateEditorData(){const e=this.editor,t={};for(const[e,i]of this._replacedRoots){const n=this._dataFromRoots.get(e),o=i.dataset.value;n!==o&&(t[e]=o,this._dataFromRoots.set(e,o))}Object.keys(t).length&&e.data.set(t,{batchType:{isUndoable:!0}})}_showSourceEditing(){const e=this.editor,t=e.editing.view,i=e.model;i.change((e=>{e.setSelection(null),e.removeSelectionAttribute(i.document.selection.getAttributeKeys())}));for(const[i,n]of t.domRoots){const r=m(e.data.get({rootName:i})),a=(0,o.createElement)(n.ownerDocument,"textarea",{rows:"1","aria-label":"Source code editing area"}),s=(0,o.createElement)(n.ownerDocument,"div",{class:"ck-source-editing-area","data-value":r},[a]);a.value=r,a.setSelectionRange(0,0),a.addEventListener("input",(()=>{s.dataset.value=a.value,e.ui.update()})),t.change((e=>{const n=t.document.getRoot(i);e.addClass("ck-hidden",n)})),e.ui.setEditableElement("sourceEditing:"+i,a),this._replacedRoots.set(i,s),this._elementReplacer.replace(n,s),this._dataFromRoots.set(i,r)}this._focusSourceEditing()}_hideSourceEditing(){const e=this.editor.editing.view;this.updateEditorData(),e.change((t=>{for(const[i]of this._replacedRoots)t.removeClass("ck-hidden",e.document.getRoot(i))})),this._elementReplacer.restore(),this._replacedRoots.clear(),this._dataFromRoots.clear(),e.focus()}_focusSourceEditing(){const e=this.editor,[t]=this._replacedRoots.values(),i=t.querySelector("textarea");e.editing.view.document.isFocused=!1,i.focus()}_disableCommands(){const e=this.editor;for(const t of e.commands.commands())t.forceDisabled(u)}_enableCommands(){const e=this.editor;for(const t of e.commands.commands())t.clearForceDisabled(u)}_handleReadOnlyMode(e){if(this.isSourceEditingMode)for(const[,t]of this._replacedRoots)t.querySelector("textarea").readOnly=e}_isAllowedToHandleSourceEditingMode(){const e=this.editor.ui.view.editable;return e&&!e.hasExternalElement}}function m(e){return function(e){return e.startsWith("<")}(e)?r(e):e}})(),(window.CKEditor5=window.CKEditor5||{}).sourceEditing=n})();;
/* @license GNU-GPL-2.0-or-later https://www.drupal.org/licensing/faq */
(function($,Drupal,{focusable}){Drupal.behaviors.dialog={attach(context,settings){const $context=$(context);if(!$('#drupal-modal').length)$('<div id="drupal-modal" class="ui-front"></div>').hide().appendTo('body');const $dialog=$context.closest('.ui-dialog-content');if($dialog.length){if($dialog.dialog('option','drupalAutoButtons'))$dialog.trigger('dialogButtonsChange');setTimeout(function(){if(!$dialog[0].contains(document.activeElement)){$dialog.dialog('instance')._focusedElement=null;$dialog.dialog('instance')._focusTabbable();}},0);}const originalClose=settings.dialog.close;settings.dialog.close=function(event,...args){originalClose.apply(settings.dialog,[event,...args]);const $element=$(event.target);const ajaxContainer=$element.data('uiDialog')?$element.data('uiDialog').opener.closest('[data-drupal-ajax-container]'):[];if(ajaxContainer.length&&(document.activeElement===document.body||$(document.activeElement).not(':visible'))){const focusableChildren=focusable(ajaxContainer[0]);if(focusableChildren.length>0)setTimeout(()=>{focusableChildren[0].focus();},0);}$(event.target).remove();};},prepareDialogButtons($dialog){const buttons=[];const $buttons=$dialog.find('.form-actions input[type=submit], .form-actions a.button, .form-actions a.action-link');$buttons.each(function(){const $originalButton=$(this);this.style.display='none';buttons.push({text:$originalButton.html()||$originalButton.attr('value'),class:$originalButton.attr('class'),'data-once':$originalButton.data('once'),click(e){if($originalButton[0].tagName==='A')$originalButton[0].click();else $originalButton.trigger('mousedown').trigger('mouseup').trigger('click');e.preventDefault();}});});return buttons;}};Drupal.AjaxCommands.prototype.openDialog=function(ajax,response,status){if(!response.selector)return false;let $dialog=$(response.selector);if(!$dialog.length)$dialog=$(`<div id="${response.selector.replace(/^#/,'')}" class="ui-front"></div>`).appendTo('body');if(!ajax.wrapper)ajax.wrapper=$dialog.attr('id');response.command='insert';response.method='html';ajax.commands.insert(ajax,response,status);if(!response.dialogOptions.buttons){response.dialogOptions.drupalAutoButtons=true;response.dialogOptions.buttons=Drupal.behaviors.dialog.prepareDialogButtons($dialog);}$dialog.on('dialogButtonsChange',()=>{const buttons=Drupal.behaviors.dialog.prepareDialogButtons($dialog);$dialog.dialog('option','buttons',buttons);});response.dialogOptions=response.dialogOptions||{};const dialog=Drupal.dialog($dialog.get(0),response.dialogOptions);if(response.dialogOptions.modal)dialog.showModal();else dialog.show();$dialog.parent().find('.ui-dialog-buttonset').addClass('form-actions');};Drupal.AjaxCommands.prototype.closeDialog=function(ajax,response,status){const $dialog=$(response.selector);if($dialog.length){Drupal.dialog($dialog.get(0)).close();if(!response.persist)$dialog.remove();}$dialog.off('dialogButtonsChange');};Drupal.AjaxCommands.prototype.setDialogOption=function(ajax,response,status){const $dialog=$(response.selector);if($dialog.length)$dialog.dialog('option',response.optionName,response.optionValue);};$(window).on('dialog:aftercreate',(e,dialog,$element,settings)=>{$element.on('click.dialog','.dialog-cancel',(e)=>{dialog.close('cancel');e.preventDefault();e.stopPropagation();});});$(window).on('dialog:beforeclose',(e,dialog,$element)=>{$element.off('.dialog');});})(jQuery,Drupal,window.tabbable);;
/* @license GNU-GPL-2.0-or-later https://raw.githubusercontent.com/jquery-form/form/master/LICENSE */
/*!
 * jQuery Form Plugin
 * version: 4.3.0
 * Requires jQuery v1.7.2 or later
 * Project repository: https://github.com/jquery-form/form

 * Copyright 2017 Kevin Morris
 * Copyright 2006 M. Alsup

 * Dual licensed under the LGPL-2.1+ or MIT licenses
 * https://github.com/jquery-form/form#license

 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 */
!function(r){"function"==typeof define&&define.amd?define(["jquery"],r):"object"==typeof module&&module.exports?module.exports=function(e,t){return void 0===t&&(t="undefined"!=typeof window?require("jquery"):require("jquery")(e)),r(t),t}:r(jQuery)}(function(q){"use strict";var m=/\r?\n/g,S={};S.fileapi=void 0!==q('<input type="file">').get(0).files,S.formdata=void 0!==window.FormData;var _=!!q.fn.prop;function o(e){var t=e.data;e.isDefaultPrevented()||(e.preventDefault(),q(e.target).closest("form").ajaxSubmit(t))}function i(e){var t=e.target,r=q(t);if(!r.is("[type=submit],[type=image]")){var a=r.closest("[type=submit]");if(0===a.length)return;t=a[0]}var n,o=t.form;"image"===(o.clk=t).type&&(void 0!==e.offsetX?(o.clk_x=e.offsetX,o.clk_y=e.offsetY):"function"==typeof q.fn.offset?(n=r.offset(),o.clk_x=e.pageX-n.left,o.clk_y=e.pageY-n.top):(o.clk_x=e.pageX-t.offsetLeft,o.clk_y=e.pageY-t.offsetTop)),setTimeout(function(){o.clk=o.clk_x=o.clk_y=null},100)}function N(){var e;q.fn.ajaxSubmit.debug&&(e="[jquery.form] "+Array.prototype.join.call(arguments,""),window.console&&window.console.log?window.console.log(e):window.opera&&window.opera.postError&&window.opera.postError(e))}q.fn.attr2=function(){if(!_)return this.attr.apply(this,arguments);var e=this.prop.apply(this,arguments);return e&&e.jquery||"string"==typeof e?e:this.attr.apply(this,arguments)},q.fn.ajaxSubmit=function(M,e,t,r){if(!this.length)return N("ajaxSubmit: skipping submit process - no element selected"),this;var O,a,n,o,X=this;"function"==typeof M?M={success:M}:"string"==typeof M||!1===M&&0<arguments.length?(M={url:M,data:e,dataType:t},"function"==typeof r&&(M.success=r)):void 0===M&&(M={}),O=M.method||M.type||this.attr2("method"),n=(n=(n="string"==typeof(a=M.url||this.attr2("action"))?q.trim(a):"")||window.location.href||"")&&(n.match(/^([^#]+)/)||[])[1],o=/(MSIE|Trident)/.test(navigator.userAgent||"")&&/^https/i.test(window.location.href||"")?"javascript:false":"about:blank",M=q.extend(!0,{url:n,success:q.ajaxSettings.success,type:O||q.ajaxSettings.type,iframeSrc:o},M);var i={};if(this.trigger("form-pre-serialize",[this,M,i]),i.veto)return N("ajaxSubmit: submit vetoed via form-pre-serialize trigger"),this;if(M.beforeSerialize&&!1===M.beforeSerialize(this,M))return N("ajaxSubmit: submit aborted via beforeSerialize callback"),this;var s=M.traditional;void 0===s&&(s=q.ajaxSettings.traditional);var u,c,C=[],l=this.formToArray(M.semantic,C,M.filtering);if(M.data&&(c=q.isFunction(M.data)?M.data(l):M.data,M.extraData=c,u=q.param(c,s)),M.beforeSubmit&&!1===M.beforeSubmit(l,this,M))return N("ajaxSubmit: submit aborted via beforeSubmit callback"),this;if(this.trigger("form-submit-validate",[l,this,M,i]),i.veto)return N("ajaxSubmit: submit vetoed via form-submit-validate trigger"),this;var f=q.param(l,s);u&&(f=f?f+"&"+u:u),"GET"===M.type.toUpperCase()?(M.url+=(0<=M.url.indexOf("?")?"&":"?")+f,M.data=null):M.data=f;var d,m,p,h=[];M.resetForm&&h.push(function(){X.resetForm()}),M.clearForm&&h.push(function(){X.clearForm(M.includeHidden)}),!M.dataType&&M.target?(d=M.success||function(){},h.push(function(e,t,r){var a=arguments,n=M.replaceTarget?"replaceWith":"html";q(M.target)[n](e).each(function(){d.apply(this,a)})})):M.success&&(q.isArray(M.success)?q.merge(h,M.success):h.push(M.success)),M.success=function(e,t,r){for(var a=M.context||this,n=0,o=h.length;n<o;n++)h[n].apply(a,[e,t,r||X,X])},M.error&&(m=M.error,M.error=function(e,t,r){var a=M.context||this;m.apply(a,[e,t,r,X])}),M.complete&&(p=M.complete,M.complete=function(e,t){var r=M.context||this;p.apply(r,[e,t,X])});var v=0<q("input[type=file]:enabled",this).filter(function(){return""!==q(this).val()}).length,g="multipart/form-data",x=X.attr("enctype")===g||X.attr("encoding")===g,y=S.fileapi&&S.formdata;N("fileAPI :"+y);var b,T=(v||x)&&!y;!1!==M.iframe&&(M.iframe||T)?M.closeKeepAlive?q.get(M.closeKeepAlive,function(){b=w(l)}):b=w(l):b=(v||x)&&y?function(e){for(var r=new FormData,t=0;t<e.length;t++)r.append(e[t].name,e[t].value);if(M.extraData){var a=function(e){var t,r,a=q.param(e,M.traditional).split("&"),n=a.length,o=[];for(t=0;t<n;t++)a[t]=a[t].replace(/\+/g," "),r=a[t].split("="),o.push([decodeURIComponent(r[0]),decodeURIComponent(r[1])]);return o}(M.extraData);for(t=0;t<a.length;t++)a[t]&&r.append(a[t][0],a[t][1])}M.data=null;var n=q.extend(!0,{},q.ajaxSettings,M,{contentType:!1,processData:!1,cache:!1,type:O||"POST"});M.uploadProgress&&(n.xhr=function(){var e=q.ajaxSettings.xhr();return e.upload&&e.upload.addEventListener("progress",function(e){var t=0,r=e.loaded||e.position,a=e.total;e.lengthComputable&&(t=Math.ceil(r/a*100)),M.uploadProgress(e,r,a,t)},!1),e});n.data=null;var o=n.beforeSend;return n.beforeSend=function(e,t){M.formData?t.data=M.formData:t.data=r,o&&o.call(this,e,t)},q.ajax(n)}(l):q.ajax(M),X.removeData("jqxhr").data("jqxhr",b);for(var j=0;j<C.length;j++)C[j]=null;return this.trigger("form-submit-notify",[this,M]),this;function w(e){var t,r,l,f,o,d,m,p,a,n,h,v,i=X[0],g=q.Deferred();if(g.abort=function(e){p.abort(e)},e)for(r=0;r<C.length;r++)t=q(C[r]),_?t.prop("disabled",!1):t.removeAttr("disabled");(l=q.extend(!0,{},q.ajaxSettings,M)).context=l.context||l,o="jqFormIO"+(new Date).getTime();var s=i.ownerDocument,u=X.closest("body");if(l.iframeTarget?(n=(d=q(l.iframeTarget,s)).attr2("name"))?o=n:d.attr2("name",o):(d=q('<iframe name="'+o+'" src="'+l.iframeSrc+'" />',s)).css({position:"absolute",top:"-1000px",left:"-1000px"}),m=d[0],p={aborted:0,responseText:null,responseXML:null,status:0,statusText:"n/a",getAllResponseHeaders:function(){},getResponseHeader:function(){},setRequestHeader:function(){},abort:function(e){var t="timeout"===e?"timeout":"aborted";N("aborting upload... "+t),this.aborted=1;try{m.contentWindow.document.execCommand&&m.contentWindow.document.execCommand("Stop")}catch(e){}d.attr("src",l.iframeSrc),p.error=t,l.error&&l.error.call(l.context,p,t,e),f&&q.event.trigger("ajaxError",[p,l,t]),l.complete&&l.complete.call(l.context,p,t)}},(f=l.global)&&0==q.active++&&q.event.trigger("ajaxStart"),f&&q.event.trigger("ajaxSend",[p,l]),l.beforeSend&&!1===l.beforeSend.call(l.context,p,l))return l.global&&q.active--,g.reject(),g;if(p.aborted)return g.reject(),g;(a=i.clk)&&(n=a.name)&&!a.disabled&&(l.extraData=l.extraData||{},l.extraData[n]=a.value,"image"===a.type&&(l.extraData[n+".x"]=i.clk_x,l.extraData[n+".y"]=i.clk_y));var x=1,y=2;function b(t){var r=null;try{t.contentWindow&&(r=t.contentWindow.document)}catch(e){N("cannot get iframe.contentWindow document: "+e)}if(r)return r;try{r=t.contentDocument?t.contentDocument:t.document}catch(e){N("cannot get iframe.contentDocument: "+e),r=t.document}return r}var c=q("meta[name=csrf-token]").attr("content"),T=q("meta[name=csrf-param]").attr("content");function j(){var e=X.attr2("target"),t=X.attr2("action"),r=X.attr("enctype")||X.attr("encoding")||"multipart/form-data";i.setAttribute("target",o),O&&!/post/i.test(O)||i.setAttribute("method","POST"),t!==l.url&&i.setAttribute("action",l.url),l.skipEncodingOverride||O&&!/post/i.test(O)||X.attr({encoding:"multipart/form-data",enctype:"multipart/form-data"}),l.timeout&&(v=setTimeout(function(){h=!0,A(x)},l.timeout));var a=[];try{if(l.extraData)for(var n in l.extraData)l.extraData.hasOwnProperty(n)&&(q.isPlainObject(l.extraData[n])&&l.extraData[n].hasOwnProperty("name")&&l.extraData[n].hasOwnProperty("value")?a.push(q('<input type="hidden" name="'+l.extraData[n].name+'">',s).val(l.extraData[n].value).appendTo(i)[0]):a.push(q('<input type="hidden" name="'+n+'">',s).val(l.extraData[n]).appendTo(i)[0]));l.iframeTarget||d.appendTo(u),m.attachEvent?m.attachEvent("onload",A):m.addEventListener("load",A,!1),setTimeout(function e(){try{var t=b(m).readyState;N("state = "+t),t&&"uninitialized"===t.toLowerCase()&&setTimeout(e,50)}catch(e){N("Server abort: ",e," (",e.name,")"),A(y),v&&clearTimeout(v),v=void 0}},15);try{i.submit()}catch(e){document.createElement("form").submit.apply(i)}}finally{i.setAttribute("action",t),i.setAttribute("enctype",r),e?i.setAttribute("target",e):X.removeAttr("target"),q(a).remove()}}T&&c&&(l.extraData=l.extraData||{},l.extraData[T]=c),l.forceSync?j():setTimeout(j,10);var w,S,k,D=50;function A(e){if(!p.aborted&&!k){if((S=b(m))||(N("cannot access response document"),e=y),e===x&&p)return p.abort("timeout"),void g.reject(p,"timeout");if(e===y&&p)return p.abort("server abort"),void g.reject(p,"error","server abort");if(S&&S.location.href!==l.iframeSrc||h){m.detachEvent?m.detachEvent("onload",A):m.removeEventListener("load",A,!1);var t,r="success";try{if(h)throw"timeout";var a="xml"===l.dataType||S.XMLDocument||q.isXMLDoc(S);if(N("isXml="+a),!a&&window.opera&&(null===S.body||!S.body.innerHTML)&&--D)return N("requeing onLoad callback, DOM not available"),void setTimeout(A,250);var n=S.body?S.body:S.documentElement;p.responseText=n?n.innerHTML:null,p.responseXML=S.XMLDocument?S.XMLDocument:S,a&&(l.dataType="xml"),p.getResponseHeader=function(e){return{"content-type":l.dataType}[e.toLowerCase()]},n&&(p.status=Number(n.getAttribute("status"))||p.status,p.statusText=n.getAttribute("statusText")||p.statusText);var o,i,s,u=(l.dataType||"").toLowerCase(),c=/(json|script|text)/.test(u);c||l.textarea?(o=S.getElementsByTagName("textarea")[0])?(p.responseText=o.value,p.status=Number(o.getAttribute("status"))||p.status,p.statusText=o.getAttribute("statusText")||p.statusText):c&&(i=S.getElementsByTagName("pre")[0],s=S.getElementsByTagName("body")[0],i?p.responseText=i.textContent?i.textContent:i.innerText:s&&(p.responseText=s.textContent?s.textContent:s.innerText)):"xml"===u&&!p.responseXML&&p.responseText&&(p.responseXML=F(p.responseText));try{w=E(p,u,l)}catch(e){r="parsererror",p.error=t=e||r}}catch(e){N("error caught: ",e),r="error",p.error=t=e||r}p.aborted&&(N("upload aborted"),r=null),p.status&&(r=200<=p.status&&p.status<300||304===p.status?"success":"error"),"success"===r?(l.success&&l.success.call(l.context,w,"success",p),g.resolve(p.responseText,"success",p),f&&q.event.trigger("ajaxSuccess",[p,l])):r&&(void 0===t&&(t=p.statusText),l.error&&l.error.call(l.context,p,r,t),g.reject(p,"error",t),f&&q.event.trigger("ajaxError",[p,l,t])),f&&q.event.trigger("ajaxComplete",[p,l]),f&&!--q.active&&q.event.trigger("ajaxStop"),l.complete&&l.complete.call(l.context,p,r),k=!0,l.timeout&&clearTimeout(v),setTimeout(function(){l.iframeTarget?d.attr("src",l.iframeSrc):d.remove(),p.responseXML=null},100)}}}var F=q.parseXML||function(e,t){return window.ActiveXObject?((t=new ActiveXObject("Microsoft.XMLDOM")).async="false",t.loadXML(e)):t=(new DOMParser).parseFromString(e,"text/xml"),t&&t.documentElement&&"parsererror"!==t.documentElement.nodeName?t:null},L=q.parseJSON||function(e){return window.eval("("+e+")")},E=function(e,t,r){var a=e.getResponseHeader("content-type")||"",n=("xml"===t||!t)&&0<=a.indexOf("xml"),o=n?e.responseXML:e.responseText;return n&&"parsererror"===o.documentElement.nodeName&&q.error&&q.error("parsererror"),r&&r.dataFilter&&(o=r.dataFilter(o,t)),"string"==typeof o&&(("json"===t||!t)&&0<=a.indexOf("json")?o=L(o):("script"===t||!t)&&0<=a.indexOf("javascript")&&q.globalEval(o)),o};return g}},q.fn.ajaxForm=function(e,t,r,a){if(("string"==typeof e||!1===e&&0<arguments.length)&&(e={url:e,data:t,dataType:r},"function"==typeof a&&(e.success=a)),(e=e||{}).delegation=e.delegation&&q.isFunction(q.fn.on),e.delegation||0!==this.length)return e.delegation?(q(document).off("submit.form-plugin",this.selector,o).off("click.form-plugin",this.selector,i).on("submit.form-plugin",this.selector,e,o).on("click.form-plugin",this.selector,e,i),this):(e.beforeFormUnbind&&e.beforeFormUnbind(this,e),this.ajaxFormUnbind().on("submit.form-plugin",e,o).on("click.form-plugin",e,i));var n={s:this.selector,c:this.context};return!q.isReady&&n.s?(N("DOM not ready, queuing ajaxForm"),q(function(){q(n.s,n.c).ajaxForm(e)})):N("terminating; zero elements found by selector"+(q.isReady?"":" (DOM not ready)")),this},q.fn.ajaxFormUnbind=function(){return this.off("submit.form-plugin click.form-plugin")},q.fn.formToArray=function(e,t,r){var a=[];if(0===this.length)return a;var n,o,i,s,u,c,l,f,d,m,p=this[0],h=this.attr("id"),v=(v=e||void 0===p.elements?p.getElementsByTagName("*"):p.elements)&&q.makeArray(v);if(h&&(e||/(Edge|Trident)\//.test(navigator.userAgent))&&(n=q(':input[form="'+h+'"]').get()).length&&(v=(v||[]).concat(n)),!v||!v.length)return a;for(q.isFunction(r)&&(v=q.map(v,r)),o=0,c=v.length;o<c;o++)if((m=(u=v[o]).name)&&!u.disabled)if(e&&p.clk&&"image"===u.type)p.clk===u&&(a.push({name:m,value:q(u).val(),type:u.type}),a.push({name:m+".x",value:p.clk_x},{name:m+".y",value:p.clk_y}));else if((s=q.fieldValue(u,!0))&&s.constructor===Array)for(t&&t.push(u),i=0,l=s.length;i<l;i++)a.push({name:m,value:s[i]});else if(S.fileapi&&"file"===u.type){t&&t.push(u);var g=u.files;if(g.length)for(i=0;i<g.length;i++)a.push({name:m,value:g[i],type:u.type});else a.push({name:m,value:"",type:u.type})}else null!=s&&(t&&t.push(u),a.push({name:m,value:s,type:u.type,required:u.required}));return e||!p.clk||(m=(d=(f=q(p.clk))[0]).name)&&!d.disabled&&"image"===d.type&&(a.push({name:m,value:f.val()}),a.push({name:m+".x",value:p.clk_x},{name:m+".y",value:p.clk_y})),a},q.fn.formSerialize=function(e){return q.param(this.formToArray(e))},q.fn.fieldSerialize=function(n){var o=[];return this.each(function(){var e=this.name;if(e){var t=q.fieldValue(this,n);if(t&&t.constructor===Array)for(var r=0,a=t.length;r<a;r++)o.push({name:e,value:t[r]});else null!=t&&o.push({name:this.name,value:t})}}),q.param(o)},q.fn.fieldValue=function(e){for(var t=[],r=0,a=this.length;r<a;r++){var n=this[r],o=q.fieldValue(n,e);null==o||o.constructor===Array&&!o.length||(o.constructor===Array?q.merge(t,o):t.push(o))}return t},q.fieldValue=function(e,t){var r=e.name,a=e.type,n=e.tagName.toLowerCase();if(void 0===t&&(t=!0),t&&(!r||e.disabled||"reset"===a||"button"===a||("checkbox"===a||"radio"===a)&&!e.checked||("submit"===a||"image"===a)&&e.form&&e.form.clk!==e||"select"===n&&-1===e.selectedIndex))return null;if("select"!==n)return q(e).val().replace(m,"\r\n");var o=e.selectedIndex;if(o<0)return null;for(var i=[],s=e.options,u="select-one"===a,c=u?o+1:s.length,l=u?o:0;l<c;l++){var f=s[l];if(f.selected&&!f.disabled){var d=(d=f.value)||(f.attributes&&f.attributes.value&&!f.attributes.value.specified?f.text:f.value);if(u)return d;i.push(d)}}return i},q.fn.clearForm=function(e){return this.each(function(){q("input,select,textarea",this).clearFields(e)})},q.fn.clearFields=q.fn.clearInputs=function(r){var a=/^(?:color|date|datetime|email|month|number|password|range|search|tel|text|time|url|week)$/i;return this.each(function(){var e=this.type,t=this.tagName.toLowerCase();a.test(e)||"textarea"===t?this.value="":"checkbox"===e||"radio"===e?this.checked=!1:"select"===t?this.selectedIndex=-1:"file"===e?/MSIE/.test(navigator.userAgent)?q(this).replaceWith(q(this).clone(!0)):q(this).val(""):r&&(!0===r&&/hidden/.test(e)||"string"==typeof r&&q(this).is(r))&&(this.value="")})},q.fn.resetForm=function(){return this.each(function(){var t=q(this),e=this.tagName.toLowerCase();switch(e){case"input":this.checked=this.defaultChecked;case"textarea":return this.value=this.defaultValue,!0;case"option":case"optgroup":var r=t.parents("select");return r.length&&r[0].multiple?"option"===e?this.selected=this.defaultSelected:t.find("option").resetForm():r.resetForm(),!0;case"select":return t.find("option").each(function(e){if(this.selected=this.defaultSelected,this.defaultSelected&&!t[0].multiple)return t[0].selectedIndex=e,!1}),!0;case"label":var a=q(t.attr("for")),n=t.find("input,select,textarea");return a[0]&&n.unshift(a[0]),n.resetForm(),!0;case"form":return"function"!=typeof this.reset&&("object"!=typeof this.reset||this.reset.nodeType)||this.reset(),!0;default:return t.find("form,input,label,select,textarea").resetForm(),!0}})},q.fn.enable=function(e){return void 0===e&&(e=!0),this.each(function(){this.disabled=!e})},q.fn.selected=function(r){return void 0===r&&(r=!0),this.each(function(){var e,t=this.type;"checkbox"===t||"radio"===t?this.checked=r:"option"===this.tagName.toLowerCase()&&(e=q(this).parent("select"),r&&e[0]&&"select-one"===e[0].type&&e.find("option").selected(!1),this.selected=r)})},q.fn.ajaxSubmit.debug=!1});

;
/* @license GNU-GPL-2.0-or-later https://www.drupal.org/licensing/faq */
((Drupal,once)=>{function init(el){const tabs=el.querySelector('.tabs');const expandedClass='is-expanded';const activeTab=tabs.querySelector('.is-active');function isTabsMobileLayout(){return tabs.querySelector('.tabs__trigger').clientHeight>0;}function handleTriggerClick(e){e.currentTarget.setAttribute('aria-expanded',!tabs.classList.contains(expandedClass));tabs.classList.toggle(expandedClass);}if(isTabsMobileLayout()&&!activeTab.matches('.tabs__tab:first-child')){const newActiveTab=activeTab.cloneNode(true);const firstTab=tabs.querySelector('.tabs__tab:first-child');tabs.insertBefore(newActiveTab,firstTab);tabs.removeChild(activeTab);}tabs.querySelector('.tabs__trigger').addEventListener('click',handleTriggerClick);}Drupal.behaviors.primaryTabs={attach(context){once('olivero-tabs','[data-drupal-nav-primary-tabs]',context).forEach(init);}};})(Drupal,once);;
((Drupal,once,tabbable)=>{function isNavOpen(navWrapper){return navWrapper.classList.contains('is-active');}function toggleNav(props,state){const value=!!state;props.navButton.setAttribute('aria-expanded',value);props.body.classList.toggle('is-overlay-active',value);props.body.classList.toggle('is-fixed',value);props.navWrapper.classList.toggle('is-active',value);}function init(props){props.navButton.setAttribute('aria-controls',props.navWrapperId);props.navButton.setAttribute('aria-expanded','false');props.navButton.addEventListener('click',()=>{toggleNav(props,!isNavOpen(props.navWrapper));});document.addEventListener('keyup',(e)=>{if(e.key==='Escape')if(props.olivero.areAnySubNavsOpen())props.olivero.closeAllSubNav();else toggleNav(props,false);});props.overlay.addEventListener('click',()=>{toggleNav(props,false);});props.overlay.addEventListener('touchstart',()=>{toggleNav(props,false);});props.header.addEventListener('keydown',(e)=>{if(e.key==='Tab'&&isNavOpen(props.navWrapper)){const tabbableNavElements=tabbable.tabbable(props.navWrapper);tabbableNavElements.unshift(props.navButton);const firstTabbableEl=tabbableNavElements[0];const lastTabbableEl=tabbableNavElements[tabbableNavElements.length-1];if(e.shiftKey){if(document.activeElement===firstTabbableEl&&!props.olivero.isDesktopNav()){lastTabbableEl.focus();e.preventDefault();}}else{if(document.activeElement===lastTabbableEl&&!props.olivero.isDesktopNav()){firstTabbableEl.focus();e.preventDefault();}}}});window.addEventListener('resize',()=>{if(props.olivero.isDesktopNav()){toggleNav(props,false);props.body.classList.remove('is-overlay-active');props.body.classList.remove('is-fixed');}Drupal.olivero.closeAllSubNav();});props.navWrapper.addEventListener('click',(e)=>{if(e.target.matches(`[href*="${window.location.pathname}#"], [href*="${window.location.pathname}#"] *, [href^="#"], [href^="#"] *`))toggleNav(props,false);});}Drupal.behaviors.oliveroNavigation={attach(context){const headerId='header';const header=once('navigation',`#${headerId}`,context).shift();const navWrapperId='header-nav';if(header){const navWrapper=header.querySelector(`#${navWrapperId}`);const {olivero}=Drupal;const navButton=context.querySelector('[data-drupal-selector="mobile-nav-button"]');const body=document.body;const overlay=context.querySelector('[data-drupal-selector="header-nav-overlay"]');init({olivero,header,navWrapperId,navWrapper,navButton,body,overlay});}}};})(Drupal,once,tabbable);;
((Drupal)=>{const {isDesktopNav}=Drupal.olivero;const secondLevelNavMenus=document.querySelectorAll('[data-drupal-selector="primary-nav-menu-item-has-children"]');function toggleSubNav(topLevelMenuItem,toState){const buttonSelector='[data-drupal-selector="primary-nav-submenu-toggle-button"]';const button=topLevelMenuItem.querySelector(buttonSelector);const state=toState!==undefined?toState:button.getAttribute('aria-expanded')!=='true';if(state){if(isDesktopNav())secondLevelNavMenus.forEach((el)=>{el.querySelector(buttonSelector).setAttribute('aria-expanded','false');el.querySelector('[data-drupal-selector="primary-nav-menu--level-2"]').classList.remove('is-active-menu-parent');el.querySelector('[data-drupal-selector="primary-nav-menu-🥕"]').classList.remove('is-active-menu-parent');});}else topLevelMenuItem.classList.remove('is-touch-event');button.setAttribute('aria-expanded',state);topLevelMenuItem.querySelector('[data-drupal-selector="primary-nav-menu--level-2"]').classList.toggle('is-active-menu-parent',state);topLevelMenuItem.querySelector('[data-drupal-selector="primary-nav-menu-🥕"]').classList.toggle('is-active-menu-parent',state);}Drupal.olivero.toggleSubNav=toggleSubNav;function handleBlur(e){if(!Drupal.olivero.isDesktopNav())return;setTimeout(()=>{const menuParentItem=e.target.closest('[data-drupal-selector="primary-nav-menu-item-has-children"]');if(!menuParentItem.contains(document.activeElement))toggleSubNav(menuParentItem,false);},200);}secondLevelNavMenus.forEach((el)=>{const button=el.querySelector('[data-drupal-selector="primary-nav-submenu-toggle-button"]');button.removeAttribute('aria-hidden');button.removeAttribute('tabindex');el.addEventListener('touchstart',()=>{el.classList.add('is-touch-event');},{passive:true});el.addEventListener('mouseover',()=>{if(isDesktopNav()&&!el.classList.contains('is-touch-event')){el.classList.add('is-active-mouseover-event');toggleSubNav(el,true);setTimeout(()=>{el.classList.remove('is-active-mouseover-event');},500);}});button.addEventListener('click',()=>{if(!el.classList.contains('is-active-mouseover-event'))toggleSubNav(el);});el.addEventListener('mouseout',()=>{if(isDesktopNav()&&!document.activeElement.matches('[aria-expanded="true"], .is-active-menu-parent *'))toggleSubNav(el,false);});el.addEventListener('blur',handleBlur,true);});function closeAllSubNav(){secondLevelNavMenus.forEach((el)=>{if(el.contains(document.activeElement))el.querySelector('[data-drupal-selector="primary-nav-submenu-toggle-button"]').focus();toggleSubNav(el,false);});}Drupal.olivero.closeAllSubNav=closeAllSubNav;function areAnySubNavsOpen(){let subNavsAreOpen=false;secondLevelNavMenus.forEach((el)=>{const button=el.querySelector('[data-drupal-selector="primary-nav-submenu-toggle-button"]');const state=button.getAttribute('aria-expanded')==='true';if(state)subNavsAreOpen=true;});return subNavsAreOpen;}Drupal.olivero.areAnySubNavsOpen=areAnySubNavsOpen;document.addEventListener('keyup',(e)=>{if(e.key==='Escape')if(isDesktopNav())closeAllSubNav();});document.addEventListener('touchstart',(e)=>{if(areAnySubNavsOpen()&&!e.target.matches('[data-drupal-selector="header-nav"], [data-drupal-selector="header-nav"] *'))closeAllSubNav();},{passive:true});})(Drupal);;
((Drupal,once)=>{function transitionToDesktopNavigation(navWrapper,navItem){document.body.classList.remove('is-always-mobile-nav');if(navWrapper.clientHeight>navItem.clientHeight)document.body.classList.add('is-always-mobile-nav');}function checkIfDesktopNavigationWraps(entries){const navItem=document.querySelector('.primary-nav__menu-item');if(Drupal.olivero.isDesktopNav()&&entries[0].contentRect.height>navItem.clientHeight){const navMediaQuery=window.matchMedia(`(max-width: ${window.innerWidth+15}px)`);document.body.classList.add('is-always-mobile-nav');navMediaQuery.addEventListener('change',()=>{transitionToDesktopNavigation(entries[0].target,navItem);},{once:true});}}function init(primaryNav){const resizeObserver=new ResizeObserver(checkIfDesktopNavigationWraps);resizeObserver.observe(primaryNav);}Drupal.behaviors.automaticMobileNav={attach(context){once('olivero-automatic-mobile-nav','[data-drupal-selector="primary-nav-menu--level-1"]',context).forEach(init);}};})(Drupal,once);;
((Drupal)=>{const searchWideButtonSelector='[data-drupal-selector="block-search-wide-button"]';const searchWideButton=document.querySelector(searchWideButtonSelector);const searchWideWrapperSelector='[data-drupal-selector="block-search-wide-wrapper"]';const searchWideWrapper=document.querySelector(searchWideWrapperSelector);function searchIsVisible(){return searchWideWrapper.classList.contains('is-active');}Drupal.olivero.searchIsVisible=searchIsVisible;function watchForClickOut(e){const clickInSearchArea=e.target.matches(`
      ${searchWideWrapperSelector},
      ${searchWideWrapperSelector} *,
      ${searchWideButtonSelector},
      ${searchWideButtonSelector} *
    `);if(!clickInSearchArea&&searchIsVisible())toggleSearchVisibility(false);}function watchForFocusOut(e){if(e.relatedTarget){const inSearchBar=e.relatedTarget.matches(`${searchWideWrapperSelector}, ${searchWideWrapperSelector} *`);const inSearchButton=e.relatedTarget.matches(`${searchWideButtonSelector}, ${searchWideButtonSelector} *`);if(!inSearchBar&&!inSearchButton)toggleSearchVisibility(false);}}function watchForEscapeOut(e){if(e.key==='Escape')toggleSearchVisibility(false);}function handleFocus(){if(searchIsVisible())searchWideWrapper.querySelector('input[type="search"]').focus();else{if(searchWideWrapper.contains(document.activeElement))searchWideButton.focus();}}function toggleSearchVisibility(visibility){searchWideButton.setAttribute('aria-expanded',visibility===true);searchWideWrapper.classList.toggle('is-active',visibility===true);searchWideWrapper.addEventListener('transitionend',handleFocus,{once:true});if(visibility===true){Drupal.olivero.closeAllSubNav();document.addEventListener('click',watchForClickOut,{capture:true});document.addEventListener('focusout',watchForFocusOut,{capture:true});document.addEventListener('keyup',watchForEscapeOut,{capture:true});}else{document.removeEventListener('click',watchForClickOut,{capture:true});document.removeEventListener('focusout',watchForFocusOut,{capture:true});document.removeEventListener('keyup',watchForEscapeOut,{capture:true});}}Drupal.olivero.toggleSearchVisibility=toggleSearchVisibility;Drupal.behaviors.searchWide={attach(context){const searchWideButtonEl=once('search-wide',searchWideButtonSelector,context).shift();if(searchWideButtonEl){searchWideButtonEl.setAttribute('aria-expanded',searchIsVisible());searchWideButtonEl.addEventListener('click',()=>{toggleSearchVisibility(!searchIsVisible());});}}};})(Drupal);;
((Drupal,drupalSettings)=>{const replacementsSelector=`script[data-big-pipe-replacement-for-placeholder-with-id]`;const ajaxObject=Drupal.ajax({url:'',base:false,element:false,progress:false});function mapTextContentToAjaxResponse(content){if(content==='')return false;try{return JSON.parse(content);}catch(e){return false;}}function processReplacement(replacement){const id=replacement.dataset.bigPipeReplacementForPlaceholderWithId;const content=replacement.textContent.trim();if(typeof drupalSettings.bigPipePlaceholderIds[id]==='undefined')return;const response=mapTextContentToAjaxResponse(content);if(response===false)return;delete drupalSettings.bigPipePlaceholderIds[id];ajaxObject.success(response,'success');}function checkMutation(node){return Boolean(node.nodeType===Node.ELEMENT_NODE&&node.nodeName==='SCRIPT'&&node.dataset&&node.dataset.bigPipeReplacementForPlaceholderWithId);}function checkMutationAndProcess(node){if(checkMutation(node))processReplacement(node);}function processMutations(mutations){mutations.forEach(({addedNodes,type,target})=>{addedNodes.forEach(checkMutationAndProcess);if(type==='characterData'&&checkMutation(target.parentNode)&&drupalSettings.bigPipePlaceholderIds[target.parentNode.dataset.bigPipeReplacementForPlaceholderWithId]===true)processReplacement(target.parentNode);});}const observer=new MutationObserver(processMutations);Drupal.attachBehaviors(document);document.querySelectorAll(replacementsSelector).forEach(processReplacement);observer.observe(document.body,{childList:true,subtree:true,characterData:true});window.addEventListener('DOMContentLoaded',()=>{const mutations=observer.takeRecords();observer.disconnect();if(mutations.length)processMutations(mutations);Drupal.ajax.instances[ajaxObject.instanceIndex]=null;});})(Drupal,drupalSettings);;
