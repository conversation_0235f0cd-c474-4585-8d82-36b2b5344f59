/* @license GNU-GPL-2.0-or-later https://www.drupal.org/licensing/faq */
.tabs{--tabs-height:var(--sp3);--tabs-padding-inline:var(--sp1-5);--tabs-active-border-size:0.375rem;--tabs-highlight-color:var(--color--primary-50);--tabs-text-color:var(--color-text-neutral-soft);--tabs-text-color-active:var(--color--gray-5);--tabs-letter-spacing:1px;--tabs-font-size:var(--font-size-s);--tabs-background-color:var(--color--gray-100);--tabs-background-color-hover:var(--color--gray-95);--tabs-border-width:1px;--tabs-border-color:var(--color--gray-95);--tabs-transition-duration:0.2s;display:flex;flex-direction:column;width:100%;margin:0;margin-inline-start:0;padding:0;list-style:none;}@media (min-width:43.75rem){.tabs{flex-direction:row;flex-wrap:wrap;}}.tabs__tab{display:none;margin:0;margin-block-end:calc(-1 * var(--tabs-border-width));}.tabs__tab.is-active{display:flex;}@media (min-width:43.75rem){.tabs__tab{display:flex;margin-block-end:0;margin-inline-start:calc(-1 * var(--tabs-border-width));}}html:not(.js) .tabs__tab{display:flex;}.tabs.is-expanded .tabs__tab{display:flex;}.tabs--secondary .tabs__tab{display:block;}@media (min-width:43.75rem){.tabs--secondary .tabs__tab{display:flex;}}.tabs__link{display:flex;flex-grow:1;align-items:center;height:var(--tabs-height);padding-block:0;padding-inline:var(--tabs-padding-inline);transition:background-color var(--tabs-transition-duration);-webkit-text-decoration:none;text-decoration:none;letter-spacing:var(--tabs-letter-spacing);color:var(--tabs-text-color);border:var(--tabs-border-width) solid var(--tabs-border-color);background-color:var(--tabs-background-color);font-size:var(--tabs-font-size);}.tabs__link:hover{color:var(--tabs-text-color-active);background-color:var(--tabs-background-color-hover);}.tabs__link:focus{position:relative;outline:solid 3px var(--tabs-highlight-color);outline-offset:-3px;}.tabs__link.is-active{position:relative;color:var(--tabs-text-color-active);font-weight:600;}.tabs__link.is-active::after{position:absolute;inset-block-start:calc(-1 * var(--tabs-border-width));inset-inline-start:calc(-1 * var(--tabs-border-width));height:calc(100% + var(--tabs-border-width) * 2);content:"";border-inline-start:var(--tabs-active-border-size) solid var(--tabs-highlight-color);}@media (min-width:43.75rem){.tabs__link.is-active::after{inset-block:auto calc(-1 * var(--tabs-border-width));width:calc(100% + 2 * var(--tabs-border-width));height:0;border-block-start:var(--tabs-active-border-size) solid var(--tabs-highlight-color);border-inline-start:0;}}@media (min-width:43.75rem){.tabs--secondary .tabs__link{border-color:transparent;background-color:transparent;}}.tabs__trigger{display:flex;align-items:center;justify-content:center;width:var(--tabs-height);margin-block:0;margin-inline:calc(-1 * var(--tabs-border-width)) 0;cursor:pointer;border:solid var(--tabs-border-width) var(--tabs-border-color);background-color:var(--tabs-background-color);}.tabs__trigger:hover{background-color:var(--tabs-background-color-hover);}.tabs__trigger:focus{position:relative;border-color:var(--tabs-highlight-color);outline:none;}html:not(.js) .tabs__trigger{display:none;}@media (min-width:43.75rem){.tabs__trigger{display:none;}}.tabs__trigger-icon{position:relative;display:block;width:var(--sp);height:0.625rem;margin-block-start:calc(-2 * var(--tabs-border-width));}.tabs__trigger-icon > span{position:absolute;inset-inline-start:0;display:block;width:100%;transition:transform var(--tabs-transition-duration),opacity var(--tabs-transition-duration),top var(--tabs-transition-duration);border-block-start:solid 2px var(--tabs-highlight-color);}.tabs__trigger-icon > span:nth-child(1){inset-block-start:0;}.tabs__trigger[aria-expanded="true"] :is(.tabs__trigger-icon > span:nth-child(1)){inset-block-start:calc(50% + 1px);transform:rotate(45deg);}.tabs__trigger-icon > span:nth-child(2){inset-block-start:calc(50% + 1px);}.tabs__trigger[aria-expanded="true"] :is(.tabs__trigger-icon > span:nth-child(2)){opacity:0;}.tabs__trigger-icon > span:nth-child(3){inset-block-start:calc(100% + 2px);}.tabs__trigger[aria-expanded="true"] :is(.tabs__trigger-icon > span:nth-child(3)){inset-block-start:calc(50% + 1px);transform:rotate(-45deg);}
